{"node": {"0001588e13ae7208556648741910b6beec37cf5d51": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "rsc"}}, "0031a4283ff3f6e85b6e5f3ab2d85a6d110b92dea8": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "rsc"}}, "40097649f73a2891a9fc24939a91049971aed781b7": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "action-browser"}}, "403258f765b5145f904c616586271fca83d0c47b92": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "action-browser"}}}, "edge": {}}