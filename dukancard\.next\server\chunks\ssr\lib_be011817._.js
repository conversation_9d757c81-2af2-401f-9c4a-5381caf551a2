module.exports = {

"[project]/lib/config/plans.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Central configuration for all plan-related constants
 * This file serves as the single source of truth for plan information
 */ // Plan types
__turbopack_context__.s({
    "PLANS": (()=>PLANS),
    "getPlanById": (()=>getPlanById),
    "getPlanByRazorpayPlanId": (()=>getPlanByRazorpayPlanId),
    "getPlanId": (()=>getPlanId),
    "getProductLimit": (()=>getProductLimit),
    "getRazorpayPlanId": (()=>getRazorpayPlanId),
    "getSubscriptionRazorpayPlanId": (()=>getSubscriptionRazorpayPlanId),
    "hasFeature": (()=>hasFeature),
    "mapRazorpayPlanToDukancardPlan": (()=>mapRazorpayPlanToDukancardPlan),
    "pricingPlans": (()=>pricingPlans)
});
// Define Razorpay plan IDs
// Different plan IDs for production and development environments
const RAZORPAY_PLAN_IDS = {
    free: {
        monthly: "free-plan-monthly",
        yearly: "free-plan-yearly"
    },
    basic: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgoJF3OfM6mB0",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgr1XzaksqvSZ"
    },
    growth: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMGvYCN7BM0V",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMdSbSeFrykv"
    },
    pro: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnN4mwUu6H2Ho",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnNYfrCExI496"
    },
    enterprise: {
        monthly: "enterprise-plan-monthly-razorpay",
        yearly: "enterprise-plan-yearly-razorpay"
    }
};
// Set payment gateway to Razorpay
const _paymentGateway = "razorpay";
const PLANS = [
    {
        id: "free",
        name: "Free",
        description: "Basic features for individuals and startups",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.free,
        pricing: {
            monthly: 0,
            yearly: 0
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Simple digital business card with contact information"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 5,
                description: "Showcase your products or services (limited to 5)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: false,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: false,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 1,
                description: "Upload and display 1 image in your gallery"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "basic",
        name: "Basic",
        description: "Essential features for small businesses",
        recommended: false,
        razorpayPlanIds: RAZORPAY_PLAN_IDS.basic,
        pricing: {
            monthly: 99,
            yearly: 999
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Basic digital business card with contact information"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 15,
                description: "Showcase your products or services (up to 15)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: false,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 3,
                description: "Upload and display up to 3 images in your gallery"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "growth",
        name: "Growth",
        description: "Advanced features for growing businesses",
        recommended: true,
        razorpayPlanIds: RAZORPAY_PLAN_IDS.growth,
        pricing: {
            monthly: 499,
            yearly: 4990
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Premium digital business card with enhanced features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 50,
                description: "Showcase your products or services (up to 50)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 10,
                description: "Upload and display up to 10 images"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "pro",
        name: "Pro",
        description: "Premium features for established businesses",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.pro,
        pricing: {
            monthly: 1999,
            yearly: 19990
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Elite digital business card with premium features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: "unlimited",
                description: "Showcase unlimited products or services"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: true,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: true,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 50,
                description: "Upload and display up to 50 images"
            },
            {
                name: "Priority Support",
                included: true,
                description: "Priority email and chat support"
            },
            {
                name: "Dukancard Branding",
                included: false,
                description: "No Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "enterprise",
        name: "Enterprise",
        description: "Custom solutions for large businesses",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.enterprise,
        pricing: {
            monthly: 0,
            yearly: 0
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Enterprise-grade digital business card with all premium features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: "unlimited",
                description: "Showcase unlimited products or services"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: true,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: true,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 100,
                description: "Upload and display up to 100 images"
            },
            {
                name: "Dedicated Account Manager",
                included: true,
                description: "Get a dedicated account manager"
            },
            {
                name: "Custom Analytics Dashboard",
                included: true,
                description: "Get a custom analytics dashboard"
            },
            {
                name: "24/7 Priority Support",
                included: true,
                description: "24/7 priority support"
            },
            {
                name: "White-Label Option",
                included: true,
                description: "Use your own branding instead of Dukancard"
            },
            {
                name: "Dukancard Branding",
                included: false,
                description: "No Dukancard branding on your business card"
            }
        ]
    }
];
function getPlanById(planId) {
    return PLANS.find((plan)=>plan.id === planId);
}
function getPlanByRazorpayPlanId(planId) {
    return PLANS.find((plan)=>plan.razorpayPlanIds.monthly === planId || plan.razorpayPlanIds.yearly === planId);
}
function mapRazorpayPlanToDukancardPlan(razorpayPlanId) {
    const plan = getPlanByRazorpayPlanId(razorpayPlanId);
    return plan?.id || "free";
}
function getRazorpayPlanId(planType, planCycle) {
    const plan = getPlanById(planType);
    if (!plan) {
        return null;
    }
    const planId = plan.razorpayPlanIds[planCycle];
    return planId || null;
}
function getSubscriptionRazorpayPlanId(planId, planCycle) {
    if (planId === "free") {
        return planCycle === "monthly" ? "free-plan-monthly" : "free-plan-yearly";
    } else if (planId === "basic") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgoJF3OfM6mB0" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgr1XzaksqvSZ";
    } else if (planId === "growth") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMGvYCN7BM0V" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMdSbSeFrykv";
    } else if (planId === "pro") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnN4mwUu6H2Ho" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnNYfrCExI496";
    } else {
        throw new Error(`Invalid plan selected: ${planId}`);
    }
}
function getPlanId(planType, planCycle) {
    // Use Razorpay plan IDs since we've migrated to Razorpay
    const planId = getRazorpayPlanId(planType, planCycle);
    return planId === null ? undefined : planId;
}
function getProductLimit(planType) {
    if (!planType) return 0;
    const plan = getPlanById(planType);
    if (!plan) return 0;
    const productFeature = plan.features.find((feature)=>feature.name === "Product Listings");
    if (!productFeature || !productFeature.included) return 0;
    return productFeature.limit === "unlimited" ? Infinity : productFeature.limit || 0;
}
function hasFeature(planType, featureName) {
    if (!planType) return false;
    const plan = getPlanById(planType);
    if (!plan) return false;
    const feature = plan.features.find((feature)=>feature.name === featureName);
    return feature?.included || false;
}
function pricingPlans() {
    return PLANS;
}
}}),
"[project]/lib/PricingPlans.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getPlanLimit": (()=>getPlanLimit),
    "onboardingPlans": (()=>onboardingPlans),
    "pricingPlans": (()=>pricingPlans)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$config$2f$plans$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/config/plans.ts [app-ssr] (ecmascript)");
;
// Convert a ConfigPlan to a PricingPlan for backward compatibility
function convertToPricingPlan(plan, billingCycle) {
    // All plans are available now, Enterprise will show "Contact Sales"
    const available = true;
    // Handle Enterprise plan pricing differently
    const isEnterprise = plan.id === "enterprise";
    // Calculate savings for yearly plans (skip for Enterprise)
    const monthlyCost = plan.pricing.monthly;
    const yearlyCost = plan.pricing.yearly;
    const yearlySavings = isEnterprise ? 0 : monthlyCost * 12 - yearlyCost;
    return {
        id: plan.id,
        name: `${plan.name} Plan`,
        razorpayPlanIds: {
            monthly: plan.razorpayPlanIds.monthly || null,
            yearly: plan.razorpayPlanIds.yearly || null
        },
        price: isEnterprise ? "Contact Sales" : billingCycle === "monthly" ? `₹${plan.pricing.monthly.toLocaleString("en-IN")}` : `₹${plan.pricing.yearly.toLocaleString("en-IN")}`,
        yearlyPrice: isEnterprise ? "Contact Sales" : `₹${plan.pricing.yearly.toLocaleString("en-IN")}`,
        period: isEnterprise ? "" : billingCycle === "monthly" ? "/month" : "/year",
        savings: isEnterprise ? undefined : billingCycle === "yearly" ? `Save ₹${yearlySavings.toLocaleString("en-IN")}` : undefined,
        description: plan.description,
        features: plan.features.map((f)=>{
            if (f.name === "Product Listings" && f.included) {
                const limit = f.limit === "unlimited" ? "Unlimited" : f.limit;
                return `Product/Service Listings (up to ${limit})`;
            }
            return f.included ? f.name : `❌ ${f.name}`;
        }),
        button: isEnterprise ? "Contact Sales" : "Subscribe Now",
        available,
        featured: plan.id === "free" || plan.id === "basic" || plan.id === "growth" || plan.id === "pro",
        recommended: plan.recommended || false,
        mostPopular: plan.recommended || false
    };
}
const pricingPlans = (billingCycle)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$config$2f$plans$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PLANS"].map((plan)=>convertToPricingPlan(plan, billingCycle));
};
const onboardingPlans = pricingPlans("monthly");
const getPlanLimit = (planId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$config$2f$plans$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProductLimit"])(planId);
};
}}),
"[project]/lib/schemas/locationSchemas.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "businessNameSchema": (()=>businessNameSchema),
    "businessNameSearchSchema": (()=>businessNameSearchSchema),
    "citySchema": (()=>citySchema),
    "combinedSearchParamsSchema": (()=>combinedSearchParamsSchema),
    "combinedSearchSchema": (()=>combinedSearchSchema),
    "discoverySearchSchema": (()=>discoverySearchSchema),
    "paginationSchema": (()=>paginationSchema),
    "pincodeSchema": (()=>pincodeSchema),
    "sortingSchema": (()=>sortingSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
;
const pincodeSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])({
    pincode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().regex(/^\d{6}$/, {
        message: "Pincode must be exactly 6 digits."
    }),
    locality: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().optional().nullable()
});
const citySchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])({
    city: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().min(2, {
        message: "City name must be at least 2 characters."
    }),
    locality: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().optional().nullable()
});
const businessNameSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])({
    businessName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().min(1, {
        message: "Business name is required."
    })
});
const combinedSearchSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])({
    businessName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().optional().nullable(),
    pincode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().regex(/^\d{6}$/, {
        message: "Pincode must be exactly 6 digits."
    }).optional().nullable(),
    city: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().optional().nullable(),
    locality: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().optional().nullable(),
    category: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().optional().nullable()
});
const paginationSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])({
    page: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["number"])().int().positive().default(1),
    limit: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["number"])().int().positive().max(50).default(20)
});
const sortingSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])({
    sortBy: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enum"])([
        "name_asc",
        "name_desc",
        "created_asc",
        "created_desc",
        "likes_asc",
        "likes_desc",
        "subscriptions_asc",
        "subscriptions_desc",
        "rating_asc",
        "rating_desc"
    ]).default("created_desc")
});
const discoverySearchSchema = pincodeSchema.extend({
    viewType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enum"])([
        "cards",
        "products"
    ]),
    ...paginationSchema.shape,
    ...sortingSchema.shape
});
const businessNameSearchSchema = businessNameSchema.extend({
    viewType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enum"])([
        "cards",
        "products"
    ]),
    ...paginationSchema.shape,
    ...sortingSchema.shape
});
const combinedSearchParamsSchema = combinedSearchSchema.extend({
    viewType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enum"])([
        "cards",
        "products"
    ]),
    ...paginationSchema.shape,
    ...sortingSchema.shape
});
}}),
"[project]/lib/client/locationUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCityDetailsClient": (()=>getCityDetailsClient),
    "getCitySuggestionsClient": (()=>getCitySuggestionsClient),
    "getPincodeDetailsClient": (()=>getPincodeDetailsClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/client.ts [app-ssr] (ecmascript)");
"use client";
;
async function getCitySuggestionsClient(query) {
    if (!query || query.length < 2) {
        return {
            error: "Query must be at least 2 characters."
        };
    }
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Use the PostgreSQL function to get distinct cities (up to 5)
        const { data: cityData, error: cityError } = await supabase.rpc('get_distinct_cities', {
            search_query: `%${query}%`,
            result_limit: 5
        });
        if (cityError) {
            console.error("City Suggestions Error:", cityError);
            // Fallback to regular query if RPC fails (in case the function doesn't exist)
            try {
                // Use DISTINCT ON to get unique city names directly from the database
                const { data: fallbackData, error: fallbackError } = await supabase.from("pincodes").select("DivisionName").ilike("DivisionName", `%${query}%`).order("DivisionName").limit(100);
                if (fallbackError) {
                    throw fallbackError;
                }
                if (!fallbackData || fallbackData.length === 0) {
                    return {
                        cities: []
                    };
                }
                // Get unique cities and format them
                const cities = [
                    ...new Set(fallbackData.map((item)=>item.DivisionName.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase())))
                ];
                return {
                    cities: cities.slice(0, 5)
                };
            } catch (fallbackErr) {
                console.error("Fallback City Query Error:", fallbackErr);
                return {
                    error: "Database error fetching city suggestions."
                };
            }
        }
        if (!cityData || cityData.length === 0) {
            return {
                cities: []
            };
        }
        // Format the city names to Title Case
        // The response from get_distinct_cities is an array of objects with a 'city' property
        const cities = cityData.map((item)=>item.city.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase()));
        return {
            cities
        };
    } catch (e) {
        console.error("City Suggestions Exception:", e);
        return {
            error: "An unexpected error occurred during city suggestions."
        };
    }
}
async function getPincodeDetailsClient(pincode) {
    if (!pincode || !/^\d{6}$/.test(pincode)) {
        return {
            error: "Invalid Pincode format."
        };
    }
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // First get city and state from pincodes table
        const { data: pincodeData, error: pincodeError } = await supabase.from("pincodes").select("OfficeName, DivisionName, StateName").eq("Pincode", pincode).order("OfficeName");
        if (pincodeError) {
            console.error("Pincode Fetch Error:", pincodeError);
            return {
                error: "Database error fetching pincode details."
            };
        }
        if (!pincodeData || pincodeData.length === 0) {
            return {
                error: "Pincode not found."
            };
        }
        // State names are already in title case format in the database
        const state = pincodeData[0].StateName;
        // Format city name (DivisionName) to Title Case
        const city = pincodeData[0].DivisionName.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase());
        // Get unique localities from post office names
        const localities = [
            ...new Set(pincodeData.map((item)=>item.OfficeName.replace(" B.O", "").trim()))
        ];
        return {
            city,
            state,
            localities
        };
    } catch (e) {
        console.error("Pincode Lookup Exception:", e);
        return {
            error: "An unexpected error occurred during pincode lookup."
        };
    }
}
async function getCityDetailsClient(city, expectedState) {
    if (!city || city.length < 2) {
        return {
            error: "City name must be at least 2 characters."
        };
    }
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Get state for the city - DivisionName is the city column
        // Use exact matching for city name to ensure we get the correct city
        const { data: cityData, error: cityError } = await supabase.from("pincodes").select("StateName, DivisionName").eq("DivisionName", city).limit(1);
        if (cityError) {
            console.error("City Fetch Error:", cityError);
            return {
                error: "Database error fetching city details."
            };
        }
        if (!cityData || cityData.length === 0) {
            // Try with case-insensitive search as fallback
            const { data: fallbackData, error: fallbackError } = await supabase.from("pincodes").select("StateName, DivisionName").ilike("DivisionName", city).limit(1);
            if (fallbackError || !fallbackData || fallbackData.length === 0) {
                return {
                    error: "City not found."
                };
            }
            // State names are already in title case format in the database
            const state = fallbackData[0].StateName;
            // Check if the city belongs to the expected state
            const isValidState = expectedState ? state === expectedState : true;
            return {
                state,
                isValidState
            };
        }
        // State names are already in title case format in the database
        const state = cityData[0].StateName;
        // Check if the city belongs to the expected state
        const isValidState = expectedState ? state === expectedState : true;
        return {
            state,
            isValidState
        };
    } catch (e) {
        console.error("City Lookup Exception:", e);
        return {
            error: "An unexpected error occurred during city lookup."
        };
    }
}
}}),
"[project]/lib/schemas/authSchemas.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmailOTPSchema": (()=>EmailOTPSchema),
    "IndianMobileSchema": (()=>IndianMobileSchema),
    "MobilePasswordLoginSchema": (()=>MobilePasswordLoginSchema),
    "PasswordComplexitySchema": (()=>PasswordComplexitySchema),
    "VerifyOTPSchema": (()=>VerifyOTPSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
;
const IndianMobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(10, {
    message: "Mobile number must be 10 digits"
}).max(10, {
    message: "Mobile number must be 10 digits"
}).regex(/^[6-9]\d{9}$/, {
    message: "Please enter a valid Indian mobile number"
});
const EmailOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    })
});
const VerifyOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    }),
    otp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(6, {
        message: "OTP must be 6 digits"
    }).max(6, {
        message: "OTP must be 6 digits"
    }).regex(/^\d{6}$/, {
        message: "OTP must be 6 digits"
    })
});
const PasswordComplexitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(6, "Password must be at least 6 characters long").regex(/[A-Z]/, "Password must contain at least one uppercase letter").regex(/[a-z]/, "Password must contain at least one lowercase letter").regex(/\d/, "Password must contain at least one number").regex(/[^a-zA-Z0-9]/, "Password must contain at least one special character");
const MobilePasswordLoginSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    mobile: IndianMobileSchema,
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Password is required"
    })
});
}}),
"[project]/lib/utils/customBranding.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCustomBrandingStyles": (()=>generateCustomBrandingStyles),
    "getBrandingText": (()=>getBrandingText),
    "getDefaultCustomBrandingSettings": (()=>getDefaultCustomBrandingSettings),
    "getPrimaryThemeColor": (()=>getPrimaryThemeColor),
    "getThemeSpecificHeaderImage": (()=>getThemeSpecificHeaderImage),
    "hasActiveCustomBranding": (()=>hasActiveCustomBranding),
    "hasAnyHeaderImage": (()=>hasAnyHeaderImage),
    "hasCustomBrandingAccess": (()=>hasCustomBrandingAccess),
    "shouldShowDukancardBranding": (()=>shouldShowDukancardBranding),
    "validateCustomBrandingSettings": (()=>validateCustomBrandingSettings)
});
function hasCustomBrandingAccess(userPlan) {
    return userPlan === "pro" || userPlan === "enterprise";
}
function shouldShowDukancardBranding(userPlan, customBranding) {
    // For users NOT on Pro/Enterprise plans, ALWAYS show DukanCard branding
    // regardless of any custom branding settings
    if (!hasCustomBrandingAccess(userPlan)) {
        return true;
    }
    // For Pro/Enterprise users, only hide branding if they explicitly set it to hidden
    return !customBranding?.hide_dukancard_branding;
}
function getThemeSpecificHeaderImage(userPlan, customBranding, currentTheme) {
    // Only return custom images for Pro/Enterprise users
    if (!hasCustomBrandingAccess(userPlan) || !customBranding) {
        return null;
    }
    const isDark = currentTheme === "dark";
    // Priority 1: Theme-specific images
    if (isDark && customBranding.custom_header_image_dark_url?.trim()) {
        return customBranding.custom_header_image_dark_url;
    }
    if (!isDark && customBranding.custom_header_image_light_url?.trim()) {
        return customBranding.custom_header_image_light_url;
    }
    // Priority 2: Fallback to opposite theme if current theme image is missing
    if (isDark && customBranding.custom_header_image_light_url?.trim()) {
        return customBranding.custom_header_image_light_url;
    }
    if (!isDark && customBranding.custom_header_image_dark_url?.trim()) {
        return customBranding.custom_header_image_dark_url;
    }
    // Priority 3: Legacy single image field for backward compatibility
    if (customBranding.custom_header_image_url?.trim()) {
        return customBranding.custom_header_image_url;
    }
    return null;
}
function getBrandingText(userPlan, customBranding) {
    // Only return custom text for Pro/Enterprise users
    if (hasCustomBrandingAccess(userPlan) && customBranding?.custom_header_text) {
        return customBranding.custom_header_text;
    }
    return null; // Will show default Dukancard branding
}
function getPrimaryThemeColor(userPlan, customBranding, fallbackThemeColor) {
    const defaultColor = "var(--brand-gold)";
    // Use theme_color if available and user is Pro/Enterprise
    if (hasCustomBrandingAccess(userPlan) && fallbackThemeColor) {
        return fallbackThemeColor;
    }
    return defaultColor;
}
function generateCustomBrandingStyles(userPlan, customBranding, fallbackThemeColor) {
    const primaryColor = getPrimaryThemeColor(userPlan, customBranding, fallbackThemeColor);
    const styles = {
        "--theme-color": primaryColor,
        "--theme-color-80": `${primaryColor}CC`,
        "--theme-color-50": `${primaryColor}80`,
        "--theme-color-30": `${primaryColor}4D`,
        "--theme-color-20": `${primaryColor}33`,
        "--theme-color-10": `${primaryColor}1A`,
        "--theme-color-5": `${primaryColor}0D`,
        "--theme-accent-end": "#E5C76E"
    };
    return styles;
}
function validateCustomBrandingSettings(settings) {
    const errors = [];
    // Validate header text length
    if (settings.custom_header_text && settings.custom_header_text.length > 50) {
        errors.push("Custom header text must be 50 characters or less");
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
function getDefaultCustomBrandingSettings() {
    return {
        custom_header_text: "",
        hide_dukancard_branding: false
    };
}
function hasActiveCustomBranding(customBranding) {
    if (!customBranding) return false;
    return !!(customBranding.custom_header_text || customBranding.custom_header_image_url || customBranding.custom_header_image_light_url || customBranding.custom_header_image_dark_url || customBranding.hide_dukancard_branding);
}
function hasAnyHeaderImage(customBranding) {
    if (!customBranding) return false;
    return !!(customBranding.custom_header_image_url?.trim() || customBranding.custom_header_image_light_url?.trim() || customBranding.custom_header_image_dark_url?.trim());
}
}}),
"[project]/lib/config/categories.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BUSINESS_CATEGORIES": (()=>BUSINESS_CATEGORIES),
    "CATEGORY_GROUPS": (()=>CATEGORY_GROUPS),
    "getCategories": (()=>getCategories),
    "getCategoryByName": (()=>getCategoryByName),
    "getCategoryBySlug": (()=>getCategoryBySlug),
    "getPopularCategories": (()=>getPopularCategories)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$utensils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Utensils$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/utensils.js [app-ssr] (ecmascript) <export default as Utensils>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/briefcase.js [app-ssr] (ecmascript) <export default as Briefcase>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/graduation-cap.js [app-ssr] (ecmascript) <export default as GraduationCap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/car.js [app-ssr] (ecmascript) <export default as Car>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Store$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/store.js [app-ssr] (ecmascript) <export default as Store>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$stethoscope$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Stethoscope$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/stethoscope.js [app-ssr] (ecmascript) <export default as Stethoscope>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scissors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scissors$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/scissors.js [app-ssr] (ecmascript) <export default as Scissors>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wrench.js [app-ssr] (ecmascript) <export default as Wrench>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$bag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingBag$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shopping-bag.js [app-ssr] (ecmascript) <export default as ShoppingBag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$laptop$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Laptop$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/laptop.js [app-ssr] (ecmascript) <export default as Laptop>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-ssr] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dumbbell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dumbbell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/dumbbell.js [app-ssr] (ecmascript) <export default as Dumbbell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plane$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plane$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plane.js [app-ssr] (ecmascript) <export default as Plane>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$coffee$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Coffee$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/coffee.js [app-ssr] (ecmascript) <export default as Coffee>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$music$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Music$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/music.js [app-ssr] (ecmascript) <export default as Music>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$camera$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Camera$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/camera.js [app-ssr] (ecmascript) <export default as Camera>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pen.js [app-ssr] (ecmascript) <export default as Pen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2d$tool$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PenTool$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pen-tool.js [app-ssr] (ecmascript) <export default as PenTool>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Code$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/code.js [app-ssr] (ecmascript) <export default as Code>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shirt$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shirt$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shirt.js [app-ssr] (ecmascript) <export default as Shirt>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/truck.js [app-ssr] (ecmascript) <export default as Truck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/building.js [app-ssr] (ecmascript) <export default as Building>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$landmark$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Landmark$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/landmark.js [app-ssr] (ecmascript) <export default as Landmark>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/hammer.js [app-ssr] (ecmascript) <export default as Hammer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$leaf$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Leaf$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/leaf.js [app-ssr] (ecmascript) <export default as Leaf>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-ssr] (ecmascript) <export default as Palette>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/book-open.js [app-ssr] (ecmascript) <export default as BookOpen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2d$pulse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartPulse$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/heart-pulse.js [app-ssr] (ecmascript) <export default as HeartPulse>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-ssr] (ecmascript) <export default as Sparkles>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gem$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/gem.js [app-ssr] (ecmascript) <export default as Gem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/smartphone.js [app-ssr] (ecmascript) <export default as Smartphone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$megaphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Megaphone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/megaphone.js [app-ssr] (ecmascript) <export default as Megaphone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/banknote.js [app-ssr] (ecmascript) <export default as Banknote>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gavel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gavel$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/gavel.js [app-ssr] (ecmascript) <export default as Gavel>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scroll$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/scroll.js [app-ssr] (ecmascript) <export default as Scroll>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$handshake$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Handshake$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/handshake.js [app-ssr] (ecmascript) <export default as Handshake>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$warehouse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Warehouse$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/warehouse.js [app-ssr] (ecmascript) <export default as Warehouse>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$factory$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Factory$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/factory.js [app-ssr] (ecmascript) <export default as Factory>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tractor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Tractor$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/tractor.js [app-ssr] (ecmascript) <export default as Tractor>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$cake$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Cake$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/cake.js [app-ssr] (ecmascript) <export default as Cake>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bed$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bed.js [app-ssr] (ecmascript) <export default as Bed>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$luggage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Luggage$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/luggage.js [app-ssr] (ecmascript) <export default as Luggage>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bus.js [app-ssr] (ecmascript) <export default as Bus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$flower$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Flower$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/flower.js [app-ssr] (ecmascript) <export default as Flower>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shopping-cart.js [app-ssr] (ecmascript) <export default as ShoppingCart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-ssr] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mic$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mic.js [app-ssr] (ecmascript) <export default as Mic>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$film$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Film$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/film.js [app-ssr] (ecmascript) <export default as Film>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gamepad$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gamepad2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/gamepad-2.js [app-ssr] (ecmascript) <export default as Gamepad2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/printer.js [app-ssr] (ecmascript) <export default as Printer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$cog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Cog$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/cog.js [app-ssr] (ecmascript) <export default as Cog>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$boxes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Boxes$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/boxes.js [app-ssr] (ecmascript) <export default as Boxes>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wallet.js [app-ssr] (ecmascript) <export default as Wallet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$badge$2d$indian$2d$rupee$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BadgeIndianRupee$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/badge-indian-rupee.js [app-ssr] (ecmascript) <export default as BadgeIndianRupee>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/building-2.js [app-ssr] (ecmascript) <export default as Building2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$presentation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Presentation$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/presentation.js [app-ssr] (ecmascript) <export default as Presentation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-ssr] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$droplets$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Droplets$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/droplets.js [app-ssr] (ecmascript) <export default as Droplets>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$recycle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Recycle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/recycle.js [app-ssr] (ecmascript) <export default as Recycle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$microscope$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Microscope$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/microscope.js [app-ssr] (ecmascript) <export default as Microscope>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pill$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pill.js [app-ssr] (ecmascript) <export default as Pill>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$baby$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Baby$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/baby.js [app-ssr] (ecmascript) <export default as Baby>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$glasses$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Glasses$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/glasses.js [app-ssr] (ecmascript) <export default as Glasses>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$footprints$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Footprints$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/footprints.js [app-ssr] (ecmascript) <export default as Footprints>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bike$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bike$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bike.js [app-ssr] (ecmascript) <export default as Bike>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sofa$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sofa$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sofa.js [app-ssr] (ecmascript) <export default as Sofa>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$stamp$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Stamp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/stamp.js [app-ssr] (ecmascript) <export default as Stamp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield-check.js [app-ssr] (ecmascript) <export default as ShieldCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bug.js [app-ssr] (ecmascript) <export default as Bug>");
;
const BUSINESS_CATEGORIES = [
    // Food & Dining
    {
        name: "Restaurants",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$utensils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Utensils$3e$__["Utensils"],
        slug: "restaurants",
        description: "Restaurants and dining establishments",
        isPopular: true
    },
    {
        name: "Cafes & Bakeries",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$coffee$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Coffee$3e$__["Coffee"],
        slug: "cafes-bakeries",
        description: "Coffee shops, bakeries, and dessert places",
        isPopular: true
    },
    {
        name: "Food Delivery",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__["Truck"],
        slug: "food-delivery",
        description: "Food delivery services"
    },
    {
        name: "Catering",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$utensils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Utensils$3e$__["Utensils"],
        slug: "catering",
        description: "Catering services for events"
    },
    {
        name: "Sweet Shops",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$cake$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Cake$3e$__["Cake"],
        slug: "sweet-shops",
        description: "Traditional sweet shops and confectioneries"
    },
    {
        name: "Street Food",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$utensils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Utensils$3e$__["Utensils"],
        slug: "street-food",
        description: "Street food vendors and stalls"
    },
    {
        name: "Cloud Kitchen",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$utensils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Utensils$3e$__["Utensils"],
        slug: "cloud-kitchen",
        description: "Delivery-only food businesses"
    },
    {
        name: "Tiffin Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$utensils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Utensils$3e$__["Utensils"],
        slug: "tiffin-services",
        description: "Home-cooked meal delivery services"
    },
    // Retail & Shopping
    {
        name: "Retail Stores",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Store$3e$__["Store"],
        slug: "retail",
        description: "General retail and shops",
        isPopular: true
    },
    {
        name: "Grocery & Supermarkets",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__["ShoppingCart"],
        slug: "grocery",
        description: "Grocery stores and supermarkets",
        isPopular: true
    },
    {
        name: "Fashion & Clothing",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shirt$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shirt$3e$__["Shirt"],
        slug: "fashion",
        description: "Clothing and fashion retailers",
        isPopular: true
    },
    {
        name: "Electronics",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__["Smartphone"],
        slug: "electronics",
        description: "Electronics and gadget stores"
    },
    {
        name: "Home Decor",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sofa$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sofa$3e$__["Sofa"],
        slug: "home-decor",
        description: "Home decor and furnishing stores"
    },
    {
        name: "Jewelry",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gem$3e$__["Gem"],
        slug: "jewelry",
        description: "Jewelry and accessory stores"
    },
    {
        name: "Bookstores",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"],
        slug: "bookstores",
        description: "Book shops and stationers"
    },
    {
        name: "Footwear",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$footprints$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Footprints$3e$__["Footprints"],
        slug: "footwear",
        description: "Shoe and footwear stores"
    },
    {
        name: "Gift Shops",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$bag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingBag$3e$__["ShoppingBag"],
        slug: "gift-shops",
        description: "Gift and souvenir shops"
    },
    {
        name: "Eyewear",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$glasses$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Glasses$3e$__["Glasses"],
        slug: "eyewear",
        description: "Optical shops and eyewear retailers"
    },
    {
        name: "Mobile Shops",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__["Smartphone"],
        slug: "mobile-shops",
        description: "Mobile phone retailers and repair shops"
    },
    // Professional Services
    {
        name: "Legal Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gavel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gavel$3e$__["Gavel"],
        slug: "legal",
        description: "Lawyers and legal consultants",
        isPopular: true
    },
    {
        name: "Financial Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__["Banknote"],
        slug: "financial",
        description: "Financial advisors and services",
        isPopular: true
    },
    {
        name: "Accounting",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$badge$2d$indian$2d$rupee$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BadgeIndianRupee$3e$__["BadgeIndianRupee"],
        slug: "accounting",
        description: "Accounting and tax services"
    },
    {
        name: "Consulting",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"],
        slug: "consulting",
        description: "Business and management consulting"
    },
    {
        name: "Insurance",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__["Wallet"],
        slug: "insurance",
        description: "Insurance services and agents"
    },
    {
        name: "HR Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
        slug: "hr-services",
        description: "Human resources and recruitment services"
    },
    {
        name: "Tax Consultants",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$badge$2d$indian$2d$rupee$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BadgeIndianRupee$3e$__["BadgeIndianRupee"],
        slug: "tax-consultants",
        description: "Tax filing and consultation services"
    },
    {
        name: "Notary Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$stamp$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Stamp$3e$__["Stamp"],
        slug: "notary",
        description: "Notary and document verification services"
    },
    {
        name: "Translation Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scroll$3e$__["Scroll"],
        slug: "translation",
        description: "Language translation and interpretation services"
    },
    // Healthcare
    {
        name: "Medical Clinics",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$stethoscope$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Stethoscope$3e$__["Stethoscope"],
        slug: "medical",
        description: "Medical clinics and doctors",
        isPopular: true
    },
    {
        name: "Dental Care",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "dental",
        description: "Dental clinics and services"
    },
    {
        name: "Pharmacy",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pill$3e$__["Pill"],
        slug: "pharmacy",
        description: "Pharmacies and medical supplies"
    },
    {
        name: "Mental Health",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2d$pulse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartPulse$3e$__["HeartPulse"],
        slug: "mental-health",
        description: "Mental health services and counseling"
    },
    {
        name: "Alternative Medicine",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$leaf$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Leaf$3e$__["Leaf"],
        slug: "alternative-medicine",
        description: "Ayurveda, homeopathy, and alternative treatments"
    },
    {
        name: "Diagnostic Centers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$microscope$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Microscope$3e$__["Microscope"],
        slug: "diagnostic",
        description: "Medical testing and diagnostic centers"
    },
    {
        name: "Physiotherapy",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2d$pulse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartPulse$3e$__["HeartPulse"],
        slug: "physiotherapy",
        description: "Physiotherapy and rehabilitation services"
    },
    {
        name: "Veterinary",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2d$pulse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartPulse$3e$__["HeartPulse"],
        slug: "veterinary",
        description: "Veterinary clinics and pet healthcare"
    },
    {
        name: "Elder Care",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2d$pulse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartPulse$3e$__["HeartPulse"],
        slug: "elder-care",
        description: "Elder care and assisted living services"
    },
    {
        name: "Maternity Care",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$baby$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Baby$3e$__["Baby"],
        slug: "maternity",
        description: "Maternity and childcare services"
    },
    // Beauty & Wellness
    {
        name: "Salon & Spa",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scissors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scissors$3e$__["Scissors"],
        slug: "salon-spa",
        description: "Beauty salons and spa services",
        isPopular: true
    },
    {
        name: "Fitness",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dumbbell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Dumbbell$3e$__["Dumbbell"],
        slug: "fitness",
        description: "Gyms and fitness centers",
        isPopular: true
    },
    {
        name: "Yoga & Meditation",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
        slug: "yoga",
        description: "Yoga studios and meditation centers"
    },
    {
        name: "Cosmetics",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "cosmetics",
        description: "Cosmetics and beauty products"
    },
    {
        name: "Barber Shops",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scissors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scissors$3e$__["Scissors"],
        slug: "barber",
        description: "Men's grooming and barber shops"
    },
    {
        name: "Wellness Centers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "wellness",
        description: "Wellness and holistic health centers"
    },
    {
        name: "Massage Therapy",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "massage",
        description: "Massage and bodywork services"
    },
    {
        name: "Skin Care",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "skin-care",
        description: "Skin care clinics and dermatology"
    },
    // Education & Training
    {
        name: "Schools",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__["GraduationCap"],
        slug: "schools",
        description: "Schools and educational institutions",
        isPopular: true
    },
    {
        name: "Coaching Centers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"],
        slug: "coaching",
        description: "Coaching and tutoring centers"
    },
    {
        name: "Vocational Training",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$presentation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Presentation$3e$__["Presentation"],
        slug: "vocational",
        description: "Vocational and skill training"
    },
    {
        name: "Online Education",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$laptop$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Laptop$3e$__["Laptop"],
        slug: "online-education",
        description: "Online courses and e-learning"
    },
    {
        name: "Language Schools",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"],
        slug: "language",
        description: "Language learning and training centers"
    },
    {
        name: "Music Classes",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$music$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Music$3e$__["Music"],
        slug: "music-classes",
        description: "Music schools and training"
    },
    {
        name: "Dance Classes",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$music$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Music$3e$__["Music"],
        slug: "dance-classes",
        description: "Dance schools and training"
    },
    {
        name: "Art Schools",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__["Palette"],
        slug: "art-schools",
        description: "Art and craft education"
    },
    {
        name: "Driving Schools",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
        slug: "driving-education",
        description: "Driving training and education"
    },
    {
        name: "Playschools",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$baby$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Baby$3e$__["Baby"],
        slug: "playschools",
        description: "Preschools and early education"
    },
    {
        name: "Tuition Centers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"],
        slug: "tuition",
        description: "Private tutoring and academic support"
    },
    // Technology
    {
        name: "IT Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Code$3e$__["Code"],
        slug: "it-services",
        description: "IT services and support",
        isPopular: true
    },
    {
        name: "Software Development",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$laptop$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Laptop$3e$__["Laptop"],
        slug: "software",
        description: "Software development companies"
    },
    {
        name: "Web Development",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Code$3e$__["Code"],
        slug: "web-development",
        description: "Web design and development services"
    },
    {
        name: "Digital Marketing",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$megaphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Megaphone$3e$__["Megaphone"],
        slug: "digital-marketing",
        description: "Digital marketing agencies and services"
    },
    {
        name: "App Development",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__["Smartphone"],
        slug: "app-development",
        description: "Mobile app development services"
    },
    {
        name: "IT Hardware",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$laptop$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Laptop$3e$__["Laptop"],
        slug: "it-hardware",
        description: "Computer hardware sales and services"
    },
    {
        name: "Cyber Security",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"],
        slug: "cyber-security",
        description: "Cybersecurity services and solutions"
    },
    {
        name: "Cloud Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Code$3e$__["Code"],
        slug: "cloud-services",
        description: "Cloud computing and hosting services"
    },
    {
        name: "Data Analytics",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Code$3e$__["Code"],
        slug: "data-analytics",
        description: "Data analysis and business intelligence"
    },
    // Automotive
    {
        name: "Auto Repair",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__["Wrench"],
        slug: "auto-repair",
        description: "Car repair and service centers",
        isPopular: true
    },
    {
        name: "Car Dealerships",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
        slug: "car-dealerships",
        description: "New and used car dealerships"
    },
    {
        name: "Auto Parts",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$cog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Cog$3e$__["Cog"],
        slug: "auto-parts",
        description: "Automotive parts and accessories"
    },
    {
        name: "Two-Wheeler Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bike$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bike$3e$__["Bike"],
        slug: "two-wheeler",
        description: "Motorcycle and scooter services"
    },
    {
        name: "Car Wash",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
        slug: "car-wash",
        description: "Car washing and detailing services"
    },
    {
        name: "Tyre Shops",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
        slug: "tyre-shops",
        description: "Tyre sales and services"
    },
    {
        name: "Auto Electricians",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"],
        slug: "auto-electricians",
        description: "Automotive electrical repair services"
    },
    {
        name: "Vehicle Rental",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
        slug: "vehicle-rental",
        description: "Car and bike rental services"
    },
    // Real Estate & Construction
    {
        name: "Real Estate",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"],
        slug: "real-estate",
        description: "Property and real estate services",
        isPopular: true
    },
    {
        name: "Construction",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__["Hammer"],
        slug: "construction",
        description: "Construction services and contractors"
    },
    {
        name: "Interior Design",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__["Palette"],
        slug: "interior-design",
        description: "Interior design and decoration services"
    },
    {
        name: "Architecture",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"],
        slug: "architecture",
        description: "Architectural services and firms"
    },
    {
        name: "Property Management",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"],
        slug: "property-management",
        description: "Property management services"
    },
    {
        name: "Building Materials",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$boxes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Boxes$3e$__["Boxes"],
        slug: "building-materials",
        description: "Construction materials suppliers"
    },
    {
        name: "Plumbing Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__["Wrench"],
        slug: "plumbing",
        description: "Plumbing installation and repair"
    },
    {
        name: "Electrical Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"],
        slug: "electrical",
        description: "Electrical installation and repair"
    },
    {
        name: "Painting Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"],
        slug: "painting",
        description: "House painting and finishing services"
    },
    {
        name: "Carpentry",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__["Hammer"],
        slug: "carpentry",
        description: "Carpentry and woodworking services"
    },
    {
        name: "Landscaping",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$flower$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Flower$3e$__["Flower"],
        slug: "landscaping",
        description: "Garden and landscape design services"
    },
    // Travel & Hospitality
    {
        name: "Hotels",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bed$3e$__["Bed"],
        slug: "hotels",
        description: "Hotels and accommodations",
        isPopular: true
    },
    {
        name: "Travel Agencies",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plane$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plane$3e$__["Plane"],
        slug: "travel-agencies",
        description: "Travel agencies and tour operators"
    },
    {
        name: "Transportation",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bus$3e$__["Bus"],
        slug: "transportation",
        description: "Transportation services"
    },
    {
        name: "Tourism",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$luggage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Luggage$3e$__["Luggage"],
        slug: "tourism",
        description: "Tourism services and attractions"
    },
    {
        name: "Homestays",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"],
        slug: "homestays",
        description: "Homestays and guest houses"
    },
    {
        name: "Tour Guides",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plane$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plane$3e$__["Plane"],
        slug: "tour-guides",
        description: "Local tour guides and services"
    },
    {
        name: "Adventure Tourism",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plane$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plane$3e$__["Plane"],
        slug: "adventure-tourism",
        description: "Adventure sports and tourism"
    },
    {
        name: "Resorts",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bed$3e$__["Bed"],
        slug: "resorts",
        description: "Resorts and vacation properties"
    },
    {
        name: "Visa Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$stamp$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Stamp$3e$__["Stamp"],
        slug: "visa-services",
        description: "Visa application and processing services"
    },
    // Entertainment & Events
    {
        name: "Entertainment",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$music$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Music$3e$__["Music"],
        slug: "entertainment",
        description: "Entertainment venues and services",
        isPopular: true
    },
    {
        name: "Event Management",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "event-management",
        description: "Event planning and management services"
    },
    {
        name: "Wedding Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$handshake$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Handshake$3e$__["Handshake"],
        slug: "wedding",
        description: "Wedding planning and related services"
    },
    {
        name: "Photography",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$camera$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Camera$3e$__["Camera"],
        slug: "photography",
        description: "Photography and videography services"
    },
    {
        name: "Cinema Halls",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$film$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Film$3e$__["Film"],
        slug: "cinema",
        description: "Movie theaters and cinemas"
    },
    {
        name: "Gaming Zones",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gamepad$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gamepad2$3e$__["Gamepad2"],
        slug: "gaming",
        description: "Gaming arcades and entertainment centers"
    },
    {
        name: "Party Venues",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$music$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Music$3e$__["Music"],
        slug: "party-venues",
        description: "Party and event venues"
    },
    {
        name: "DJs & Musicians",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$music$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Music$3e$__["Music"],
        slug: "djs-musicians",
        description: "DJs and live music performers"
    },
    {
        name: "Amusement Parks",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "amusement-parks",
        description: "Amusement and theme parks"
    },
    // Freelancers & Creative Professionals
    {
        name: "Freelance Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"],
        slug: "freelance",
        description: "Independent professionals and freelancers",
        isPopular: true
    },
    {
        name: "Graphic Design",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2d$tool$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PenTool$3e$__["PenTool"],
        slug: "graphic-design",
        description: "Graphic design services"
    },
    {
        name: "Content Creation",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pen$3e$__["Pen"],
        slug: "content-creation",
        description: "Content writing and creation services"
    },
    {
        name: "Art & Crafts",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"],
        slug: "art-crafts",
        description: "Artists and craftspeople"
    },
    {
        name: "Music & Performance",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mic$3e$__["Mic"],
        slug: "music-performance",
        description: "Musicians and performers"
    },
    {
        name: "Videography",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$film$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Film$3e$__["Film"],
        slug: "videography",
        description: "Video production and editing services"
    },
    {
        name: "Voice Over Artists",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mic$3e$__["Mic"],
        slug: "voice-over",
        description: "Voice over and narration services"
    },
    {
        name: "Translators",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scroll$3e$__["Scroll"],
        slug: "translators",
        description: "Language translation services"
    },
    {
        name: "Tutors",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"],
        slug: "tutors",
        description: "Private tutors and educators"
    },
    {
        name: "Consultants",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"],
        slug: "consultants",
        description: "Independent consultants and advisors"
    },
    {
        name: "Astrologers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "astrologers",
        description: "Astrology and horoscope services"
    },
    // Manufacturing & Industry
    {
        name: "Manufacturing",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$factory$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Factory$3e$__["Factory"],
        slug: "manufacturing",
        description: "Manufacturing businesses"
    },
    {
        name: "Wholesale",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$warehouse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Warehouse$3e$__["Warehouse"],
        slug: "wholesale",
        description: "Wholesale suppliers and distributors"
    },
    {
        name: "Textiles",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shirt$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shirt$3e$__["Shirt"],
        slug: "textiles",
        description: "Textile manufacturing and supplies"
    },
    {
        name: "Printing",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__["Printer"],
        slug: "printing",
        description: "Printing services and press"
    },
    {
        name: "Packaging",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$boxes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Boxes$3e$__["Boxes"],
        slug: "packaging",
        description: "Packaging materials and services"
    },
    {
        name: "Metal Works",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__["Hammer"],
        slug: "metal-works",
        description: "Metal fabrication and works"
    },
    {
        name: "Plastic Products",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$factory$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Factory$3e$__["Factory"],
        slug: "plastic-products",
        description: "Plastic manufacturing and products"
    },
    {
        name: "Handicrafts",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"],
        slug: "handicrafts",
        description: "Handmade crafts and products"
    },
    {
        name: "Furniture Making",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sofa$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sofa$3e$__["Sofa"],
        slug: "furniture",
        description: "Furniture manufacturing and carpentry"
    },
    // Agriculture
    {
        name: "Agriculture",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tractor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Tractor$3e$__["Tractor"],
        slug: "agriculture",
        description: "Farming and agricultural services"
    },
    {
        name: "Dairy",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$droplets$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Droplets$3e$__["Droplets"],
        slug: "dairy",
        description: "Dairy farms and products"
    },
    {
        name: "Organic Products",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$leaf$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Leaf$3e$__["Leaf"],
        slug: "organic",
        description: "Organic farming and products"
    },
    {
        name: "Poultry",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$leaf$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Leaf$3e$__["Leaf"],
        slug: "poultry",
        description: "Poultry farming and products"
    },
    {
        name: "Fisheries",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$droplets$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Droplets$3e$__["Droplets"],
        slug: "fisheries",
        description: "Fish farming and aquaculture"
    },
    {
        name: "Nurseries",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$leaf$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Leaf$3e$__["Leaf"],
        slug: "nurseries",
        description: "Plant nurseries and gardening supplies"
    },
    {
        name: "Farm Equipment",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tractor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Tractor$3e$__["Tractor"],
        slug: "farm-equipment",
        description: "Agricultural equipment and supplies"
    },
    {
        name: "Seed Suppliers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$leaf$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Leaf$3e$__["Leaf"],
        slug: "seed-suppliers",
        description: "Seeds and agricultural inputs"
    },
    {
        name: "Floriculture",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$flower$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Flower$3e$__["Flower"],
        slug: "floriculture",
        description: "Flower growing and selling"
    },
    // Utilities & Services
    {
        name: "Utilities",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"],
        slug: "utilities",
        description: "Utility services"
    },
    {
        name: "Cleaning Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "cleaning",
        description: "Cleaning and maintenance services"
    },
    {
        name: "Waste Management",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$recycle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Recycle$3e$__["Recycle"],
        slug: "waste-management",
        description: "Waste collection and recycling services"
    },
    {
        name: "Courier & Logistics",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__["Truck"],
        slug: "logistics",
        description: "Courier, delivery, and logistics services"
    },
    {
        name: "Home Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"],
        slug: "home-services",
        description: "Home repair and maintenance services"
    },
    {
        name: "Pest Control",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__["Bug"],
        slug: "pest-control",
        description: "Pest control and extermination services"
    },
    {
        name: "Security Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"],
        slug: "security",
        description: "Security guards and services"
    },
    {
        name: "Laundry Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "laundry",
        description: "Laundry and dry cleaning services"
    },
    {
        name: "Water Supply",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$droplets$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Droplets$3e$__["Droplets"],
        slug: "water-supply",
        description: "Water delivery and supply services"
    },
    {
        name: "Rental Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$boxes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Boxes$3e$__["Boxes"],
        slug: "rental",
        description: "Equipment and item rental services"
    },
    // Other Categories
    {
        name: "Religious Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$landmark$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Landmark$3e$__["Landmark"],
        slug: "religious",
        description: "Religious institutions and services"
    },
    {
        name: "NGOs & Charities",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$handshake$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Handshake$3e$__["Handshake"],
        slug: "ngo",
        description: "Non-profit organizations and charities"
    },
    {
        name: "Government Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"],
        slug: "government",
        description: "Government offices and services"
    },
    {
        name: "Repair Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__["Wrench"],
        slug: "repair",
        description: "General repair and maintenance services"
    },
    {
        name: "Tailoring",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scissors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scissors$3e$__["Scissors"],
        slug: "tailoring",
        description: "Tailoring and alteration services"
    },
    {
        name: "Printing & Copying",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__["Printer"],
        slug: "printing-copying",
        description: "Printing, copying, and document services"
    },
    {
        name: "Astrology",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        slug: "astrology",
        description: "Astrology and spiritual services"
    },
    {
        name: "Funeral Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$landmark$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Landmark$3e$__["Landmark"],
        slug: "funeral",
        description: "Funeral homes and memorial services"
    },
    {
        name: "Daycare",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$baby$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Baby$3e$__["Baby"],
        slug: "daycare",
        description: "Childcare and daycare services"
    },
    {
        name: "Pet Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2d$pulse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartPulse$3e$__["HeartPulse"],
        slug: "pet-services",
        description: "Pet grooming, boarding, and care"
    },
    {
        name: "Other Services",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"],
        slug: "other",
        description: "Other business services not listed elsewhere"
    }
];
function getCategories(count) {
    if (count && count > 0 && count < BUSINESS_CATEGORIES.length) {
        return BUSINESS_CATEGORIES.slice(0, count);
    }
    return BUSINESS_CATEGORIES;
}
function getPopularCategories(count) {
    const popularCategories = BUSINESS_CATEGORIES.filter((category)=>category.isPopular);
    if (count && count > 0 && count < popularCategories.length) {
        return popularCategories.slice(0, count);
    }
    return popularCategories;
}
function getCategoryBySlug(slug) {
    return BUSINESS_CATEGORIES.find((category)=>category.slug === slug);
}
function getCategoryByName(name) {
    return BUSINESS_CATEGORIES.find((category)=>category.name.toLowerCase() === name.toLowerCase());
}
const CATEGORY_GROUPS = [
    {
        name: "Food & Dining",
        categories: BUSINESS_CATEGORIES.slice(0, 8)
    },
    {
        name: "Retail & Shopping",
        categories: BUSINESS_CATEGORIES.slice(8, 19)
    },
    {
        name: "Professional Services",
        categories: BUSINESS_CATEGORIES.slice(19, 28)
    },
    {
        name: "Healthcare",
        categories: BUSINESS_CATEGORIES.slice(28, 38)
    },
    {
        name: "Beauty & Wellness",
        categories: BUSINESS_CATEGORIES.slice(38, 46)
    },
    {
        name: "Education & Training",
        categories: BUSINESS_CATEGORIES.slice(46, 57)
    },
    {
        name: "Technology",
        categories: BUSINESS_CATEGORIES.slice(57, 66)
    },
    {
        name: "Automotive",
        categories: BUSINESS_CATEGORIES.slice(66, 75)
    },
    {
        name: "Real Estate & Construction",
        categories: BUSINESS_CATEGORIES.slice(75, 86)
    },
    {
        name: "Travel & Hospitality",
        categories: BUSINESS_CATEGORIES.slice(86, 95)
    },
    {
        name: "Entertainment & Events",
        categories: BUSINESS_CATEGORIES.slice(95, 104)
    },
    {
        name: "Freelancers & Creative Professionals",
        categories: BUSINESS_CATEGORIES.slice(104, 115)
    },
    {
        name: "Manufacturing & Industry",
        categories: BUSINESS_CATEGORIES.slice(115, 124)
    },
    {
        name: "Agriculture",
        categories: BUSINESS_CATEGORIES.slice(124, 133)
    },
    {
        name: "Utilities & Services",
        categories: BUSINESS_CATEGORIES.slice(133, 143)
    },
    {
        name: "Other Categories",
        categories: BUSINESS_CATEGORIES.slice(143)
    }
];
}}),
"[project]/lib/qrCodeGenerator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions to generate and download QR codes in different formats:
 * 1. Enhanced QR code with business information in A4 format for better printing options
 * 2. Raw QR image for digital use
 */ __turbopack_context__.s({
    "downloadRawQRImage": (()=>downloadRawQRImage),
    "generateAndDownloadQRCode": (()=>generateAndDownloadQRCode)
});
async function downloadRawQRImage(svgElement, slug) {
    const svgData = new XMLSerializer().serializeToString(svgElement);
    return new Promise((resolve, reject)=>{
        const img = new Image();
        img.onload = ()=>{
            // Create a canvas with padding around the QR code
            // Increase size for higher quality output
            const padding = 50; // Increased padding for better appearance
            const canvas = document.createElement("canvas");
            // Set a larger fixed size for better quality (1000x1000 pixels)
            const canvasSize = 1000;
            canvas.width = canvasSize;
            canvas.height = canvasSize;
            const ctx = canvas.getContext("2d");
            if (!ctx) {
                reject(new Error("Could not get canvas context"));
                return;
            }
            // Fill with white background
            ctx.fillStyle = "#FFFFFF";
            ctx.fillRect(0, 0, canvasSize, canvasSize);
            // Enable high-quality image rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = "high";
            // Calculate QR code size (canvas size minus padding on all sides)
            const qrSize = canvasSize - padding * 2;
            // Draw QR code centered with higher quality
            ctx.drawImage(img, padding, padding, qrSize, qrSize);
            // Add a subtle border around the QR code for better definition
            ctx.strokeStyle = "#EEEEEE";
            ctx.lineWidth = 2;
            ctx.strokeRect(padding - 2, padding - 2, qrSize + 4, qrSize + 4);
            // Convert to PNG with maximum quality and trigger download
            const pngFile = canvas.toDataURL("image/png", 1.0);
            const link = document.createElement("a");
            link.download = `${slug}-qr-code.png`;
            link.href = pngFile;
            link.click();
            resolve();
        };
        img.onerror = ()=>{
            reject(new Error("Could not load QR code SVG"));
        };
        img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
    });
}
async function generateAndDownloadQRCode(svgElement, businessInfo) {
    const { businessName, ownerName, address, slug, themeColor = "#F59E0B" } = businessInfo;
    // A4 dimensions in pixels at 300 DPI
    // A4 is 210mm × 297mm, which is approximately 2480 × 3508 pixels at 300 DPI
    const width = 2480;
    const height = 3508;
    // Create canvas
    const canvas = document.createElement("canvas");
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext("2d");
    if (!ctx) {
        throw new Error("Could not get canvas context");
    }
    // Fill background with white
    ctx.fillStyle = "#FFFFFF";
    ctx.fillRect(0, 0, width, height);
    // Create a subtle gradient background
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, "#FFFFFF");
    gradient.addColorStop(1, "#F8F8F8");
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    // Add a subtle pattern overlay for texture
    createSubtlePattern(ctx, width, height);
    // Draw a more sophisticated frame with enhanced visual design
    drawSophisticatedFrame(ctx, width, height, themeColor);
    // Add decorative elements
    drawDecorativeElements(ctx, width, height, themeColor);
    // Add header with business name - handle long business names
    ctx.fillStyle = "#333333";
    ctx.textAlign = "center";
    // Start with large font size and reduce if needed
    let businessNameFontSize = 160;
    ctx.font = `bold ${businessNameFontSize}px 'Arial'`;
    // Check if business name is too long and reduce font size if needed
    let businessNameWidth = ctx.measureText(businessName).width;
    while(businessNameWidth > width * 0.75 && businessNameFontSize > 80){
        businessNameFontSize -= 10;
        ctx.font = `bold ${businessNameFontSize}px 'Arial'`;
        businessNameWidth = ctx.measureText(businessName).width;
    }
    // If still too long, split into multiple lines
    if (businessNameWidth > width * 0.75) {
        const businessNameLines = splitTextIntoLines(businessName, 30);
        let yPos = height * 0.12;
        businessNameLines.forEach((line)=>{
            ctx.fillText(line, width / 2, yPos, width * 0.8);
            yPos += businessNameFontSize * 0.8; // Add spacing between lines
        });
    } else {
        ctx.fillText(businessName, width / 2, height * 0.15, width * 0.8);
    }
    // Add a subtle underline below the business name
    const textWidth = ctx.measureText(businessName).width;
    const underlineWidth = Math.min(textWidth, width * 0.6);
    ctx.beginPath();
    ctx.moveTo(width / 2 - underlineWidth / 2, height * 0.17);
    ctx.lineTo(width / 2 + underlineWidth / 2, height * 0.17);
    ctx.strokeStyle = themeColor;
    ctx.lineWidth = 6;
    ctx.stroke();
    // Add "Scan to view our digital card" text with better styling
    ctx.font = "80px 'Arial'";
    ctx.fillStyle = "#555555";
    ctx.fillText("Scan to view our digital card", width / 2, height * 0.22, width * 0.8);
    // QR code size and positioning - adjust based on business name length
    // Use smaller QR code if business name is very long (multiple lines)
    const businessNameLines = splitTextIntoLines(businessName, 30);
    const qrSizeMultiplier = businessNameLines.length > 1 ? 0.3 : 0.35;
    const qrSize = Math.min(width, height) * qrSizeMultiplier;
    const qrX = (width - qrSize) / 2;
    const qrY = businessNameLines.length > 1 ? height * 0.35 : height * 0.3;
    // Draw an elegant container for the QR code
    drawQRCodeContainer(ctx, qrX, qrY, qrSize, themeColor);
    // Draw QR code
    const svgData = new XMLSerializer().serializeToString(svgElement);
    return new Promise((resolve, reject)=>{
        const img = new Image();
        img.onload = ()=>{
            // Draw QR code centered
            ctx.drawImage(img, qrX, qrY, qrSize, qrSize);
            // Add URL text below QR code with better styling
            // Handle potentially long slugs by reducing font size if needed
            const urlText = `dukancard.in/${slug}`;
            let urlFontSize = 70;
            ctx.font = `bold ${urlFontSize}px 'Arial'`;
            // Check if URL is too long and reduce font size if needed
            let urlWidth = ctx.measureText(urlText).width;
            while(urlWidth > width * 0.7 && urlFontSize > 40){
                urlFontSize -= 5;
                ctx.font = `bold ${urlFontSize}px 'Arial'`;
                urlWidth = ctx.measureText(urlText).width;
            }
            // Position URL with sufficient distance from QR code
            ctx.fillStyle = "#333333";
            ctx.fillText(urlText, width / 2, qrY + qrSize + 180, width * 0.8);
            // Add a divider line - position based on URL position
            const dividerY = qrY + qrSize + 240; // Position after URL
            drawDivider(ctx, width, dividerY, width * 0.7, themeColor);
            // Add owner name with better styling - handle long names
            let ownerNameFontSize = 100;
            ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;
            ctx.fillStyle = "#333333";
            // Check if owner name is too long and reduce font size if needed
            let ownerNameWidth = ctx.measureText(ownerName).width;
            while(ownerNameWidth > width * 0.75 && ownerNameFontSize > 60){
                ownerNameFontSize -= 5;
                ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;
                ownerNameWidth = ctx.measureText(ownerName).width;
            }
            // If still too long, split into multiple lines
            if (ownerNameWidth > width * 0.75) {
                const ownerNameLines = splitTextIntoLines(ownerName, 25);
                let yPos = height * 0.75;
                ownerNameLines.forEach((line)=>{
                    ctx.fillText(line, width / 2, yPos, width * 0.8);
                    yPos += ownerNameFontSize * 0.7; // Add spacing between lines
                });
            } else {
                ctx.fillText(ownerName, width / 2, height * 0.75, width * 0.8);
            }
            // We're removing the location icon as it's causing visual issues
            // No need to draw the location icon anymore
            // Add address with better styling
            let addressFontSize = 70;
            ctx.font = `${addressFontSize}px 'Arial'`;
            ctx.fillStyle = "#555555";
            // Calculate starting position based on owner name position and length
            let yPosition;
            if (ownerNameWidth > width * 0.75) {
                // If owner name was split into multiple lines, position address accordingly
                const ownerNameLines = splitTextIntoLines(ownerName, 25);
                yPosition = height * 0.75 + ownerNameLines.length * ownerNameFontSize * 0.7 + 50;
            } else {
                yPosition = height * 0.8;
            }
            // Split address into multiple lines
            const addressLines = splitTextIntoLines(address, 50);
            // If address is very long, reduce font size
            if (addressLines.length > 3) {
                addressFontSize = 60;
                ctx.font = `${addressFontSize}px 'Arial'`;
            }
            // Draw each line of the address
            addressLines.forEach((line)=>{
                ctx.fillText(line, width / 2, yPosition, width * 0.8);
                yPosition += addressFontSize + 20; // Line height with spacing
            });
            // Add a footer with powered by text - position dynamically based on content
            ctx.font = "50px 'Arial'";
            ctx.fillStyle = "#888888";
            // Calculate footer position based on address length
            const footerY = Math.min(height - 100, yPosition + 150);
            ctx.fillText("Powered by Dukancard", width / 2, footerY, width * 0.8);
            // Convert to JPG and trigger download
            const jpgFile = canvas.toDataURL("image/jpeg", 0.95);
            const link = document.createElement("a");
            link.download = `${slug}-qrcode.jpg`;
            link.href = jpgFile;
            link.click();
            resolve();
        };
        img.onerror = ()=>{
            reject(new Error("Could not load QR code SVG"));
        };
        img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
    });
}
/**
 * Creates a subtle pattern overlay for texture
 */ function createSubtlePattern(ctx, width, height) {
    ctx.save();
    ctx.globalAlpha = 0.03;
    // Create a pattern of small dots
    const patternSize = 20;
    for(let x = 0; x < width; x += patternSize){
        for(let y = 0; y < height; y += patternSize){
            ctx.beginPath();
            ctx.arc(x, y, 1, 0, Math.PI * 2);
            ctx.fillStyle = "#000000";
            ctx.fill();
        }
    }
    ctx.restore();
}
/**
 * Draws a sophisticated frame with enhanced visual design
 */ function drawSophisticatedFrame(ctx, width, height, themeColor) {
    // Create a more elegant border with gradient
    const borderGradient = ctx.createLinearGradient(0, 0, width, height);
    borderGradient.addColorStop(0, themeColor);
    borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));
    borderGradient.addColorStop(1, themeColor);
    // Draw outer border with gradient
    ctx.strokeStyle = borderGradient;
    ctx.lineWidth = 3;
    ctx.strokeRect(40, 40, width - 80, height - 80);
    // Draw inner border with gradient and shadow
    ctx.shadowColor = "rgba(0, 0, 0, 0.1)";
    ctx.shadowBlur = 15;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    ctx.strokeStyle = borderGradient;
    ctx.lineWidth = 8;
    ctx.strokeRect(80, 80, width - 160, height - 160);
    // Reset shadow
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    // Add corner decorations with enhanced design
    const cornerSize = 120;
    // Top-left corner
    drawCornerDecoration(ctx, 80, 80, cornerSize, themeColor, "top-left");
    // Top-right corner
    drawCornerDecoration(ctx, width - 80, 80, cornerSize, themeColor, "top-right");
    // Bottom-left corner
    drawCornerDecoration(ctx, 80, height - 80, cornerSize, themeColor, "bottom-left");
    // Bottom-right corner
    drawCornerDecoration(ctx, width - 80, height - 80, cornerSize, themeColor, "bottom-right");
    // Add subtle decorative patterns along the borders
    drawBorderPatterns(ctx, width, height, themeColor);
}
/**
 * Draws enhanced corner decorations
 */ function drawCornerDecoration(ctx, x, y, size, color, position) {
    ctx.save();
    // Create gradient for more elegant corners
    const cornerGradient = ctx.createLinearGradient(position.includes("left") ? x : x - size, position.includes("top") ? y : y - size, position.includes("left") ? x + size : x, position.includes("top") ? y + size : y);
    cornerGradient.addColorStop(0, color);
    cornerGradient.addColorStop(1, adjustColor(color, 20));
    ctx.strokeStyle = cornerGradient;
    ctx.lineWidth = 8;
    ctx.lineCap = "round";
    ctx.beginPath();
    if (position === "top-left") {
        ctx.moveTo(x, y + size);
        ctx.lineTo(x, y);
        ctx.lineTo(x + size, y);
        // Add decorative dot
        ctx.moveTo(x + 30, y + 30);
        ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);
    } else if (position === "top-right") {
        ctx.moveTo(x - size, y);
        ctx.lineTo(x, y);
        ctx.lineTo(x, y + size);
        // Add decorative dot
        ctx.moveTo(x - 30, y + 30);
        ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);
    } else if (position === "bottom-left") {
        ctx.moveTo(x, y - size);
        ctx.lineTo(x, y);
        ctx.lineTo(x + size, y);
        // Add decorative dot
        ctx.moveTo(x + 30, y - 30);
        ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);
    } else if (position === "bottom-right") {
        ctx.moveTo(x - size, y);
        ctx.lineTo(x, y);
        ctx.lineTo(x, y - size);
        // Add decorative dot
        ctx.moveTo(x - 30, y - 30);
        ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);
    }
    ctx.stroke();
    // Fill the decorative dots
    ctx.fillStyle = color;
    if (position === "top-left") {
        ctx.beginPath();
        ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);
        ctx.fill();
    } else if (position === "top-right") {
        ctx.beginPath();
        ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);
        ctx.fill();
    } else if (position === "bottom-left") {
        ctx.beginPath();
        ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);
        ctx.fill();
    } else if (position === "bottom-right") {
        ctx.beginPath();
        ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);
        ctx.fill();
    }
    ctx.restore();
}
/**
 * Draws enhanced decorative elements
 */ function drawDecorativeElements(ctx, width, height, themeColor) {
    // Add subtle decorative elements with enhanced design
    ctx.save();
    // Create gradient for decorative elements
    const gradientTopLeft = ctx.createRadialGradient(0, 0, 0, 0, 0, 400);
    gradientTopLeft.addColorStop(0, themeColor);
    gradientTopLeft.addColorStop(1, "rgba(255, 255, 255, 0)");
    const gradientBottomRight = ctx.createRadialGradient(width, height, 0, width, height, 400);
    gradientBottomRight.addColorStop(0, themeColor);
    gradientBottomRight.addColorStop(1, "rgba(255, 255, 255, 0)");
    // Adjust opacity for subtlety
    ctx.globalAlpha = 0.08;
    // Draw decorative gradient in top-left
    ctx.beginPath();
    ctx.arc(0, 0, 400, 0, Math.PI * 2);
    ctx.fillStyle = gradientTopLeft;
    ctx.fill();
    // Draw decorative gradient in bottom-right
    ctx.beginPath();
    ctx.arc(width, height, 400, 0, Math.PI * 2);
    ctx.fillStyle = gradientBottomRight;
    ctx.fill();
    // Add decorative patterns
    ctx.globalAlpha = 0.05;
    // Draw decorative pattern in center
    const patternSize = 60;
    const patternRows = Math.ceil(height / patternSize);
    const patternCols = Math.ceil(width / patternSize);
    for(let row = 0; row < patternRows; row++){
        for(let col = 0; col < patternCols; col++){
            // Only draw pattern in a diamond shape in the center
            const distanceFromCenter = Math.abs(row - patternRows / 2) + Math.abs(col - patternCols / 2);
            if (distanceFromCenter < patternRows / 3) {
                const x = col * patternSize;
                const y = row * patternSize;
                // Draw subtle pattern element
                if ((row + col) % 2 === 0) {
                    ctx.beginPath();
                    ctx.arc(x + patternSize / 2, y + patternSize / 2, 2, 0, Math.PI * 2);
                    ctx.fillStyle = themeColor;
                    ctx.fill();
                }
            }
        }
    }
    ctx.restore();
}
/**
 * Draws an elegant container for the QR code with enhanced visual design
 */ function drawQRCodeContainer(ctx, x, y, size, themeColor) {
    const padding = 100;
    const containerWidth = size + padding * 2;
    const containerHeight = size + padding * 2;
    const containerX = x - padding;
    const containerY = y - padding;
    // Draw white background with rounded corners
    ctx.fillStyle = "#FFFFFF";
    roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);
    ctx.fill();
    // Draw subtle shadow
    ctx.shadowColor = "rgba(0, 0, 0, 0.15)";
    ctx.shadowBlur = 40;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 15;
    ctx.fillStyle = "#FFFFFF";
    roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);
    ctx.fill();
    // Reset shadow
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    // Draw elegant border with gradient
    const borderGradient = ctx.createLinearGradient(containerX, containerY, containerX + containerWidth, containerY + containerHeight);
    borderGradient.addColorStop(0, themeColor);
    borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));
    borderGradient.addColorStop(1, themeColor);
    ctx.strokeStyle = borderGradient;
    ctx.lineWidth = 3;
    roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);
    ctx.stroke();
    // Add decorative corner elements to the QR container
    const cornerSize = 30;
    // Top-left corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX, containerY + cornerSize);
    ctx.lineTo(containerX, containerY);
    ctx.lineTo(containerX + cornerSize, containerY);
    ctx.strokeStyle = themeColor;
    ctx.lineWidth = 5;
    ctx.stroke();
    // Top-right corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX + containerWidth - cornerSize, containerY);
    ctx.lineTo(containerX + containerWidth, containerY);
    ctx.lineTo(containerX + containerWidth, containerY + cornerSize);
    ctx.stroke();
    // Bottom-left corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX, containerY + containerHeight - cornerSize);
    ctx.lineTo(containerX, containerY + containerHeight);
    ctx.lineTo(containerX + cornerSize, containerY + containerHeight);
    ctx.stroke();
    // Bottom-right corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX + containerWidth - cornerSize, containerY + containerHeight);
    ctx.lineTo(containerX + containerWidth, containerY + containerHeight);
    ctx.lineTo(containerX + containerWidth, containerY + containerHeight - cornerSize);
    ctx.stroke();
}
/**
 * Draws an enhanced divider line with decorative elements
 */ function drawDivider(ctx, x, y, width, color) {
    ctx.save();
    // Create gradient for divider
    const dividerGradient = ctx.createLinearGradient(x / 2 - width / 2, y, x / 2 + width / 2, y);
    dividerGradient.addColorStop(0, "rgba(255, 255, 255, 0)");
    dividerGradient.addColorStop(0.1, color);
    dividerGradient.addColorStop(0.5, adjustColor(color, 20));
    dividerGradient.addColorStop(0.9, color);
    dividerGradient.addColorStop(1, "rgba(255, 255, 255, 0)");
    // Draw main line with gradient
    ctx.beginPath();
    ctx.moveTo(x / 2 - width / 2, y);
    ctx.lineTo(x / 2 + width / 2, y);
    ctx.strokeStyle = dividerGradient;
    ctx.lineWidth = 3;
    ctx.stroke();
    // Draw decorative element in the middle
    ctx.beginPath();
    ctx.arc(x / 2, y, 15, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
    // Add outer ring to the decorative element
    ctx.beginPath();
    ctx.arc(x / 2, y, 20, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.stroke();
    // Add small decorative elements along the line
    const numDots = 6;
    const dotSpacing = width / 4 / numDots;
    // Left side dots
    for(let i = 1; i <= numDots; i++){
        const dotX = x / 2 - width / 8 - i * dotSpacing;
        ctx.beginPath();
        ctx.arc(dotX, y, 3, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();
    }
    // Right side dots
    for(let i = 1; i <= numDots; i++){
        const dotX = x / 2 + width / 8 + i * dotSpacing;
        ctx.beginPath();
        ctx.arc(dotX, y, 3, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();
    }
    ctx.restore();
}
/**
 * Helper function to draw a rectangle with rounded corners
 */ function roundRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
}
/**
 * Helper function to adjust color brightness
 */ function adjustColor(color, amount) {
    // Convert hex to RGB
    let r, g, b;
    if (color.startsWith("#")) {
        r = parseInt(color.slice(1, 3), 16);
        g = parseInt(color.slice(3, 5), 16);
        b = parseInt(color.slice(5, 7), 16);
    } else {
        // Default fallback color
        r = 245;
        g = 158;
        b = 11; // Default to brand gold
    }
    // Adjust brightness
    r = Math.max(0, Math.min(255, r + amount));
    g = Math.max(0, Math.min(255, g + amount));
    b = Math.max(0, Math.min(255, b + amount));
    // Convert back to hex
    return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}
/**
 * Draws decorative patterns along the borders
 */ function drawBorderPatterns(ctx, width, height, themeColor) {
    ctx.save();
    ctx.globalAlpha = 0.2;
    // Top border pattern
    for(let x = 120; x < width - 120; x += 40){
        ctx.beginPath();
        ctx.arc(x, 80, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    // Bottom border pattern
    for(let x = 120; x < width - 120; x += 40){
        ctx.beginPath();
        ctx.arc(x, height - 80, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    // Left border pattern
    for(let y = 120; y < height - 120; y += 40){
        ctx.beginPath();
        ctx.arc(80, y, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    // Right border pattern
    for(let y = 120; y < height - 120; y += 40){
        ctx.beginPath();
        ctx.arc(width - 80, y, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    ctx.restore();
}
/**
 * Helper function to split text into multiple lines
 */ function splitTextIntoLines(text, maxCharsPerLine) {
    const words = text.split(" ");
    const lines = [];
    let currentLine = "";
    words.forEach((word)=>{
        if ((currentLine + word).length <= maxCharsPerLine) {
            currentLine += (currentLine ? " " : "") + word;
        } else {
            lines.push(currentLine);
            currentLine = word;
        }
    });
    if (currentLine) {
        lines.push(currentLine);
    }
    return lines;
}
}}),
"[project]/lib/cardDownloader.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions to download business cards as PNG images
 * Uses modern-screenshot for HTML to image conversion with proper rounded corners
 */ __turbopack_context__.s({
    "downloadBusinessCard": (()=>downloadBusinessCard),
    "downloadBusinessCardAsPNG": (()=>downloadBusinessCardAsPNG),
    "findBusinessCardElement": (()=>findBusinessCardElement),
    "prepareCardForScreenshot": (()=>prepareCardForScreenshot)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$modern$2d$screenshot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/modern-screenshot/dist/index.mjs [app-ssr] (ecmascript)");
;
async function downloadBusinessCardAsPNG(cardElement, options) {
    const { businessSlug, quality = 1, scale = 3, preserveRoundedCorners = true } = options;
    try {
        // Get the actual dimensions of the card element
        const rect = cardElement.getBoundingClientRect();
        console.log('Card element dimensions:', {
            width: rect.width,
            height: rect.height,
            offsetWidth: cardElement.offsetWidth,
            offsetHeight: cardElement.offsetHeight,
            className: cardElement.className,
            tagName: cardElement.tagName
        });
        // Ensure we have valid dimensions
        if (rect.width === 0 || rect.height === 0) {
            throw new Error('Card element has invalid dimensions');
        }
        // Generate high-quality PNG using modern-screenshot with proper settings for rounded corners
        const dataUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$modern$2d$screenshot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["domToPng"])(cardElement, {
            quality,
            scale,
            backgroundColor: 'transparent',
            style: {
                // Ensure consistent rendering and preserve rounded corners
                transform: 'scale(1)',
                transformOrigin: 'top left',
                borderRadius: preserveRoundedCorners ? 'inherit' : '0',
                // Ensure the element is properly sized
                width: `${rect.width}px`,
                height: `${rect.height}px`,
                maxWidth: `${rect.width}px`,
                maxHeight: `${rect.height}px`,
                overflow: 'hidden'
            },
            // Use the actual rendered dimensions
            width: rect.width,
            height: rect.height
        });
        // Create download link
        const link = document.createElement('a');
        link.download = `${businessSlug}-digital-card.png`;
        link.href = dataUrl;
        link.click();
    } catch (error) {
        console.error('Error downloading business card as PNG:', error);
        throw new Error('Failed to download business card as PNG');
    }
}
async function downloadBusinessCard(cardElement, options) {
    return downloadBusinessCardAsPNG(cardElement, options);
}
function findBusinessCardElement(containerRef) {
    // Try to find the card element using various selectors, prioritizing the most specific
    const selectors = [
        '[data-card-element]',
        '.business-card-preview',
        '.business-card',
        '#business-card',
        '.card-preview'
    ];
    const container = containerRef?.current || document;
    const candidates = [];
    // Collect all potential card elements
    for (const selector of selectors){
        const elements = container.querySelectorAll(selector);
        elements.forEach((element)=>{
            const rect = element.getBoundingClientRect();
            // Business card should have reasonable dimensions (not too large)
            // Max width should be around 384px (max-w-sm) and height should be proportional
            if (rect.width > 0 && rect.height > 0 && rect.width <= 500) {
                candidates.push(element);
            }
        });
    }
    if (candidates.length === 0) {
        return null;
    }
    if (candidates.length === 1) {
        return candidates[0];
    }
    // If multiple candidates, prioritize based on context
    // 1. Prefer cards that are not in demo mode (check for unmasked data)
    // 2. Prefer cards that are visible and not hidden
    // 3. Prefer cards that are in the main content area (not in headers/footers)
    const scoredCandidates = candidates.map((element)=>{
        let score = 0;
        // Check if the card has unmasked phone/email (indicates authenticated context)
        const phoneElement = element.querySelector('a[href^="tel:"]');
        const emailElement = element.querySelector('a[href^="mailto:"]');
        const phoneText = phoneElement?.textContent || '';
        const emailText = emailElement?.textContent || '';
        // If phone/email don't contain asterisks, it's likely unmasked (authenticated)
        if (phoneText && !phoneText.includes('*')) score += 10;
        if (emailText && !emailText.includes('*')) score += 10;
        // Prefer visible elements
        const rect = element.getBoundingClientRect();
        if (rect.top >= 0 && rect.left >= 0) score += 5;
        // Prefer elements in main content areas (not in navigation or footer)
        const isInNav = element.closest('nav, header, footer');
        if (!isInNav) score += 5;
        // Prefer larger cards (main content vs thumbnails)
        if (rect.width > 300) score += 3;
        return {
            element,
            score
        };
    });
    // Sort by score (highest first) and return the best candidate
    scoredCandidates.sort((a, b)=>b.score - a.score);
    return scoredCandidates[0].element;
}
function prepareCardForScreenshot(cardElement) {
    const originalStyles = {
        transform: cardElement.style.transform,
        transformOrigin: cardElement.style.transformOrigin,
        position: cardElement.style.position,
        zIndex: cardElement.style.zIndex
    };
    // Apply screenshot-friendly styles
    cardElement.style.transform = 'scale(1)';
    cardElement.style.transformOrigin = 'top left';
    cardElement.style.position = 'relative';
    cardElement.style.zIndex = '1';
    // Return cleanup function
    return ()=>{
        cardElement.style.transform = originalStyles.transform;
        cardElement.style.transformOrigin = originalStyles.transformOrigin;
        cardElement.style.position = originalStyles.position;
        cardElement.style.zIndex = originalStyles.zIndex;
    };
}
}}),

};

//# sourceMappingURL=lib_be011817._.js.map