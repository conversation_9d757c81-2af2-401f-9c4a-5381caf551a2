{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/ErrorDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AlertCircle } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface ErrorDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (_open: boolean) => void;\r\n  errorMessage: string;\r\n}\r\n\r\nexport default function ErrorDialog({\r\n  open,\r\n  onOpenChange,\r\n  errorMessage,\r\n}: ErrorDialogProps) {\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\r\n      <AlertDialogContent className=\"bg-card border-border\">\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle className=\"text-foreground flex items-center gap-2\">\r\n            <AlertCircle className=\"w-6 h-6 text-[var(--brand-gold)]\" />\r\n            Error\r\n          </AlertDialogTitle>\r\n          <AlertDialogDescription className=\"text-muted-foreground\">\r\n            {errorMessage}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <Button\r\n            onClick={() => {\r\n              onOpenChange(false);\r\n              router.push(\"/?view=home\");\r\n            }}\r\n            className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] cursor-pointer\"\r\n          >\r\n            Okay\r\n          </Button>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;;;AAZA;;;;;AAoBe,SAAS,YAAY,EAClC,IAAI,EACJ,YAAY,EACZ,YAAY,EACK;;IACjB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,6LAAC,uIAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,uIAAA,CAAA,qBAAkB;YAAC,WAAU;;8BAC5B,6LAAC,uIAAA,CAAA,oBAAiB;;sCAChB,6LAAC,uIAAA,CAAA,mBAAgB;4BAAC,WAAU;;8CAC1B,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAqC;;;;;;;sCAG9D,6LAAC,uIAAA,CAAA,yBAAsB;4BAAC,WAAU;sCAC/B;;;;;;;;;;;;8BAGL,6LAAC,uIAAA,CAAA,oBAAiB;8BAChB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS;4BACP,aAAa;4BACb,OAAO,IAAI,CAAC;wBACd;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;GAjCwB;;QAKP,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/SectionBackground.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface SectionBackgroundProps {\r\n  className?: string;\r\n  variant?: \"gold\" | \"blue\" | \"purple\" | \"gradient\" | \"subtle\";\r\n  intensity?: \"low\" | \"medium\" | \"high\";\r\n  animate?: boolean;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function SectionBackground({\r\n  className = \"\",\r\n  variant = \"gold\",\r\n  intensity = \"medium\",\r\n  animate = true,\r\n  children,\r\n}: SectionBackgroundProps) {\r\n  const [_isClient, setIsClient] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Only render on client side and detect mobile\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n    setIsMobile(window.innerWidth < 768);\r\n\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Get background colors based on variant and intensity\r\n  const getBgColors = () => {\r\n    const intensityMap = {\r\n      low: { light: \"5\", dark: \"10\" },\r\n      medium: { light: \"10\", dark: \"15\" },\r\n      high: { light: \"15\", dark: \"20\" },\r\n    };\r\n\r\n    const intensityValue = intensityMap[intensity];\r\n\r\n    switch (variant) {\r\n      case \"gold\":\r\n        return {\r\n          primary: `bg-[var(--brand-gold)]/${intensityValue.light} dark:bg-[var(--brand-gold)]/${intensityValue.dark}`,\r\n          secondary: \"bg-blue-500/5 dark:bg-blue-500/10\",\r\n        };\r\n      case \"blue\":\r\n        return {\r\n          primary: `bg-blue-500/${intensityValue.light} dark:bg-blue-500/${intensityValue.dark}`,\r\n          secondary: \"bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10\",\r\n        };\r\n      case \"purple\":\r\n        return {\r\n          primary: `bg-purple-500/${intensityValue.light} dark:bg-purple-500/${intensityValue.dark}`,\r\n          secondary: \"bg-blue-500/5 dark:bg-blue-500/10\",\r\n        };\r\n      case \"gradient\":\r\n        return {\r\n          primary: `bg-gradient-to-br from-[var(--brand-gold)]/${intensityValue.light} to-blue-500/${intensityValue.light} dark:from-[var(--brand-gold)]/${intensityValue.dark} dark:to-blue-500/${intensityValue.dark}`,\r\n          secondary: \"bg-transparent\",\r\n        };\r\n      case \"subtle\":\r\n      default:\r\n        return {\r\n          primary: `bg-neutral-200/${intensityValue.light} dark:bg-neutral-800/${intensityValue.dark}`,\r\n          secondary: \"bg-neutral-300/5 dark:bg-neutral-600/10\",\r\n        };\r\n    }\r\n  };\r\n\r\n  const { primary, secondary } = getBgColors();\r\n\r\n  return (\r\n    <div className={`absolute inset-0 overflow-hidden -z-10 ${className}`}>\r\n      {/* Primary blob */}\r\n      <motion.div\r\n        className={`absolute rounded-full blur-3xl opacity-70 ${primary}`}\r\n        style={{\r\n          width: isMobile ? \"70%\" : \"50%\",\r\n          height: isMobile ? \"70%\" : \"50%\",\r\n          top: \"10%\",\r\n          right: \"5%\",\r\n        }}\r\n        animate={animate ? { opacity: 0.65 } : {}}\r\n        transition={animate ? { duration: 4, repeat: Infinity, repeatType: \"reverse\" } : {}}\r\n      />\r\n\r\n      {/* Secondary blob */}\r\n      <motion.div\r\n        className={`absolute rounded-full blur-3xl opacity-60 ${secondary}`}\r\n        style={{\r\n          width: isMobile ? \"60%\" : \"40%\",\r\n          height: isMobile ? \"60%\" : \"40%\",\r\n          bottom: \"10%\",\r\n          left: \"5%\",\r\n        }}\r\n        animate={animate ? { opacity: 0.55 } : {}}\r\n        transition={animate ? { duration: 5, repeat: Infinity, repeatType: \"reverse\", delay: 1 } : {}}\r\n      />\r\n\r\n      {/* Render children if provided */}\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAae,SAAS,kBAAkB,EACxC,YAAY,EAAE,EACd,UAAU,MAAM,EAChB,YAAY,QAAQ,EACpB,UAAU,IAAI,EACd,QAAQ,EACe;;IACvB,MAAM,CAAC,WAAW,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,YAAY;YACZ,YAAY,OAAO,UAAU,GAAG;YAEhC,MAAM;4DAAe;oBACnB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;+CAA<PERSON>,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;sCAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,cAAc;QAClB,MAAM,eAAe;YACnB,KAAK;gBAAE,OAAO;gBAAK,MAAM;YAAK;YAC9B,QAAQ;gBAAE,OAAO;gBAAM,MAAM;YAAK;YAClC,MAAM;gBAAE,OAAO;gBAAM,MAAM;YAAK;QAClC;QAEA,MAAM,iBAAiB,YAAY,CAAC,UAAU;QAE9C,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC,uBAAuB,EAAE,eAAe,KAAK,CAAC,6BAA6B,EAAE,eAAe,IAAI,EAAE;oBAC5G,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC,YAAY,EAAE,eAAe,KAAK,CAAC,kBAAkB,EAAE,eAAe,IAAI,EAAE;oBACtF,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC,cAAc,EAAE,eAAe,KAAK,CAAC,oBAAoB,EAAE,eAAe,IAAI,EAAE;oBAC1F,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC,2CAA2C,EAAE,eAAe,KAAK,CAAC,aAAa,EAAE,eAAe,KAAK,CAAC,+BAA+B,EAAE,eAAe,IAAI,CAAC,kBAAkB,EAAE,eAAe,IAAI,EAAE;oBAC9M,WAAW;gBACb;YACF,KAAK;YACL;gBACE,OAAO;oBACL,SAAS,CAAC,eAAe,EAAE,eAAe,KAAK,CAAC,qBAAqB,EAAE,eAAe,IAAI,EAAE;oBAC5F,WAAW;gBACb;QACJ;IACF;IAEA,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW;;0BAEnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,0CAA0C,EAAE,SAAS;gBACjE,OAAO;oBACL,OAAO,WAAW,QAAQ;oBAC1B,QAAQ,WAAW,QAAQ;oBAC3B,KAAK;oBACL,OAAO;gBACT;gBACA,SAAS,UAAU;oBAAE,SAAS;gBAAK,IAAI,CAAC;gBACxC,YAAY,UAAU;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,YAAY;gBAAU,IAAI,CAAC;;;;;;0BAIpF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,0CAA0C,EAAE,WAAW;gBACnE,OAAO;oBACL,OAAO,WAAW,QAAQ;oBAC1B,QAAQ,WAAW,QAAQ;oBAC3B,QAAQ;oBACR,MAAM;gBACR;gBACA,SAAS,UAAU;oBAAE,SAAS;gBAAK,IAAI,CAAC;gBACxC,YAAY,UAAU;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,YAAY;oBAAW,OAAO;gBAAE,IAAI,CAAC;;;;;;YAI7F;;;;;;;AAGP;GAjGwB;KAAA", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/FeatureCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { LucideIcon } from \"lucide-react\";\r\n\r\ninterface FeatureCardProps {\r\n  icon: LucideIcon;\r\n  title: string;\r\n  description: string;\r\n  index: number;\r\n}\r\n\r\nexport default function FeatureCard({\r\n  icon: Icon,\r\n  title,\r\n  description,\r\n  index: _index, // Renamed to _index to indicate it's intentionally unused\r\n}: FeatureCardProps) {\r\n  return (\r\n    <div className=\"h-full\">\r\n      <Card className=\"bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 p-6 h-full hover:border-[var(--brand-gold)]/70 transition-all duration-300 hover:shadow-lg dark:hover:shadow-[var(--brand-gold)]/10 rounded-xl group relative overflow-hidden\">\r\n        {/* Subtle gradient overlay */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-br from-white/0 to-[var(--brand-gold)]/5 dark:from-transparent dark:to-[var(--brand-gold)]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\r\n\r\n        {/* Icon container */}\r\n        <div className=\"mb-4 relative\">\r\n          <div className=\"w-14 h-14 rounded-full bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 flex items-center justify-center relative overflow-hidden group-hover:bg-[var(--brand-gold)]/20 dark:group-hover:bg-[var(--brand-gold)]/30 transition-colors duration-300\">\r\n            <Icon className=\"w-7 h-7 text-[var(--brand-gold)]\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Title */}\r\n        <div className=\"relative inline-block mb-2\">\r\n          <h3 className=\"text-xl font-semibold text-foreground\">{title}</h3>\r\n        </div>\r\n\r\n        <p className=\"text-neutral-600 dark:text-neutral-400 relative z-10\">\r\n          {description}\r\n        </p>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,YAAY,EAClC,MAAM,IAAI,EACV,KAAK,EACL,WAAW,EACX,OAAO,MAAM,EACI;IACjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;;8BAEd,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;8BAKpB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;;;;;;8BAGzD,6LAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAKX;KA9BwB", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/MobileFeatureCarousel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { ChevronLeft, ChevronRight, LucideIcon } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface Feature {\r\n  icon: LucideIcon;\r\n  title: string;\r\n  description: string;\r\n}\r\n\r\ninterface MobileFeatureCarouselProps {\r\n  features: Feature[];\r\n}\r\n\r\nexport default function MobileFeatureCarousel({ features }: MobileFeatureCarouselProps) {\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Simple auto-play functionality with reduced frequency\r\n  useEffect(() => {\r\n    autoPlayTimerRef.current = setTimeout(() => {\r\n      setCurrentIndex((prevIndex) => (prevIndex + 1) % features.length);\r\n    }, 8000); // Longer interval to reduce CPU usage\r\n\r\n    return () => {\r\n      if (autoPlayTimerRef.current) {\r\n        clearTimeout(autoPlayTimerRef.current);\r\n      }\r\n    };\r\n  }, [currentIndex, features.length]);\r\n\r\n  const handleManualNavigation = (newIndex: number) => {\r\n    if (autoPlayTimerRef.current) {\r\n      clearTimeout(autoPlayTimerRef.current);\r\n    }\r\n    setCurrentIndex(newIndex);\r\n  };\r\n\r\n  const nextSlide = () => {\r\n    const newIndex = (currentIndex + 1) % features.length;\r\n    handleManualNavigation(newIndex);\r\n  };\r\n\r\n  const prevSlide = () => {\r\n    const newIndex = (currentIndex - 1 + features.length) % features.length;\r\n    handleManualNavigation(newIndex);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative w-full overflow-hidden py-4\">\r\n      {/* Carousel container */}\r\n      <div className=\"relative w-full h-[280px] overflow-hidden rounded-xl\">\r\n        <div className=\"absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"w-full max-w-xs mx-auto p-6 bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-md\">\r\n            <div className=\"flex flex-col items-center text-center\">\r\n              {/* Icon */}\r\n              <div className=\"p-4 bg-[var(--brand-gold)]/10 rounded-full mb-4 relative\">\r\n                {React.createElement(features[currentIndex].icon, {\r\n                  className: \"h-8 w-8 text-[var(--brand-gold)]\",\r\n                })}\r\n              </div>\r\n\r\n              {/* Title */}\r\n              <h3 className=\"text-xl font-bold mb-2\">\r\n                {features[currentIndex].title}\r\n              </h3>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-neutral-600 dark:text-neutral-400\">\r\n                {features[currentIndex].description}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation buttons */}\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10\"\r\n          onClick={prevSlide}\r\n        >\r\n          <ChevronLeft className=\"h-5 w-5\" />\r\n        </Button>\r\n\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10\"\r\n          onClick={nextSlide}\r\n        >\r\n          <ChevronRight className=\"h-5 w-5\" />\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Dots indicator */}\r\n      <div className=\"flex justify-center mt-4 gap-1.5\">\r\n        {features.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            className={`h-2 rounded-full transition-all duration-300 ${\r\n              index === currentIndex\r\n                ? \"w-6 bg-[var(--brand-gold)]\"\r\n                : \"w-2 bg-neutral-300 dark:bg-neutral-700\"\r\n            }`}\r\n            onClick={() => handleManualNavigation(index)}\r\n            aria-label={`Go to slide ${index + 1}`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAgBe,SAAS,sBAAsB,EAAE,QAAQ,EAA8B;;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,iBAAiB,OAAO,GAAG;mDAAW;oBACpC;2DAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,SAAS,MAAM;;gBAClE;kDAAG,OAAO,sCAAsC;YAEhD;mDAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;0CAAG;QAAC;QAAc,SAAS,MAAM;KAAC;IAElC,MAAM,yBAAyB,CAAC;QAC9B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QACA,gBAAgB;IAClB;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAC,eAAe,CAAC,IAAI,SAAS,MAAM;QACrD,uBAAuB;IACzB;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAC,eAAe,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM;QACvE,uBAAuB;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,cAAA,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE;4CAChD,WAAW;wCACb;;;;;;kDAIF,6LAAC;wCAAG,WAAU;kDACX,QAAQ,CAAC,aAAa,CAAC,KAAK;;;;;;kDAI/B,6LAAC;wCAAE,WAAU;kDACV,QAAQ,CAAC,aAAa,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;kCAO3C,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK5B,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,GAAG,sBAChB,6LAAC;wBAEC,WAAW,CAAC,6CAA6C,EACvD,UAAU,eACN,+BACA,0CACJ;wBACF,SAAS,IAAM,uBAAuB;wBACtC,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;uBAPjC;;;;;;;;;;;;;;;;AAajB;GAlGwB;KAAA", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/FeaturesSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  ArrowRight, CreditCard, Globe, ShieldCheck,\r\n  Store, Users, Smartphone, Bell, BarChart3, Palette\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport SectionBackground from \"../SectionBackground\";\r\nimport FeatureCard from \"./FeatureCard\";\r\nimport MobileFeatureCarousel from \"./MobileFeatureCarousel\";\r\n\r\n// Enhanced feature data with additional details\r\nconst features = [\r\n  {\r\n    icon: CreditCard,\r\n    title: \"Digital Business Cards\",\r\n    description:\r\n      \"Create stunning digital cards that showcase your business information, services, and brand identity.\",\r\n    details:\r\n      \"Your digital business card includes customizable sections for contact information, business hours, location with map integration, social media links, and a professional bio. Upload your logo, cover image, and profile photo to create a cohesive brand experience.\",\r\n  },\r\n  {\r\n    icon: Store,\r\n    title: \"Digital Storefront\",\r\n    description:\r\n      \"Set up a mini digital storefront to display your products and services with pricing and details.\",\r\n    details:\r\n      \"Showcase up to 50 products or services with detailed descriptions, high-quality images, pricing information, and availability status. Organize items into categories for easy browsing and highlight featured offerings to attract customer attention.\",\r\n  },\r\n  {\r\n    icon: Globe,\r\n    title: \"Web Presence\",\r\n    description:\r\n      \"Get a dedicated URL for your digital card that you can share across platforms and print materials.\",\r\n    details:\r\n      \"Your business receives a custom URL (yourbusiness.dukancard.in) that's easy to remember and share. Include it on business cards, brochures, social media profiles, and email signatures to drive traffic to your digital presence.\",\r\n  },\r\n  {\r\n    icon: Users,\r\n    title: \"Customer Engagement\",\r\n    description:\r\n      \"Connect with customers through integrated messaging and feedback collection tools.\",\r\n    details:\r\n      \"Enable direct messaging so customers can inquire about products or services. Collect feedback through customizable forms, and build a subscriber list to send updates about new offerings, promotions, or business news.\",\r\n  },\r\n  {\r\n    icon: Smartphone,\r\n    title: \"Mobile Optimized\",\r\n    description:\r\n      \"Ensure your business looks great on any device with fully responsive design and mobile-first approach.\",\r\n    details:\r\n      \"Our platform automatically optimizes your digital card for viewing on smartphones, tablets, and desktop computers. Fast loading times and touch-friendly interfaces ensure a seamless experience for customers regardless of how they access your business information.\",\r\n  },\r\n  {\r\n    icon: Bell,\r\n    title: \"Notification System\",\r\n    description:\r\n      \"Keep customers informed about updates, new products, or business announcements through push notifications.\",\r\n    details:\r\n      \"Send timely updates to subscribers when you add new products, change business hours, or make important announcements. Schedule notifications in advance or send them instantly to maximize engagement and drive repeat business.\",\r\n  },\r\n  {\r\n    icon: ShieldCheck,\r\n    title: \"Secure Platform\",\r\n    description:\r\n      \"Keep your business information secure with our enterprise-grade security protocols.\",\r\n    details:\r\n      \"We implement industry-leading security measures including end-to-end encryption, secure authentication, regular security audits, and compliance with data protection regulations to ensure your business and customer information remains protected.\",\r\n  },\r\n  {\r\n    icon: BarChart3,\r\n    title: \"Analytics Dashboard\",\r\n    description:\r\n      \"Track engagement, views, and customer interactions with comprehensive analytics.\",\r\n    details:\r\n      \"Gain valuable insights into customer behavior with detailed analytics on page views, click-through rates, popular products, geographic distribution of visitors, and engagement metrics. Use this data to refine your offerings and marketing strategies.\",\r\n  },\r\n  {\r\n    icon: Palette,\r\n    title: \"Customizable Design\",\r\n    description:\r\n      \"Personalize your digital card with custom colors, fonts, and layouts to match your brand.\",\r\n    details:\r\n      \"Choose from multiple design templates and then customize colors, typography, button styles, and layout options to create a unique digital presence that aligns perfectly with your existing brand identity and stands out from competitors.\",\r\n  },\r\n];\r\n\r\nexport default function FeaturesSection() {\r\n  const router = useRouter();\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Detect mobile devices\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    checkMobile();\r\n    window.addEventListener('resize', checkMobile);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', checkMobile);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-10 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative\">\r\n      {/* Simple background */}\r\n      <div className=\"absolute inset-0 -z-10\">\r\n        <SectionBackground variant=\"gold\" intensity=\"low\" />\r\n      </div>\r\n\r\n      <div className=\"text-center mb-12 md:mb-16\">\r\n        <h2 className=\"text-3xl md:text-4xl font-bold text-foreground mb-4\">\r\n          Everything You Need for a{\" \"}\r\n          <span className=\"text-[var(--brand-gold)]\">Digital Presence</span>\r\n        </h2>\r\n        <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\r\n          Dukancard provides powerful tools to help businesses of all sizes\r\n          establish a strong digital footprint.\r\n        </p>\r\n      </div>\r\n\r\n      {/* Mobile Feature Carousel - only visible on mobile */}\r\n      {isMobile && (\r\n        <div className=\"md:hidden mb-8\">\r\n          <MobileFeatureCarousel features={features} />\r\n        </div>\r\n      )}\r\n\r\n      {/* Desktop/Tablet Grid Layout - hidden on mobile */}\r\n      <div className=\"hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8\">\r\n        {features.map((feature, index) => (\r\n          <FeatureCard\r\n            key={index}\r\n            icon={feature.icon}\r\n            title={feature.title}\r\n            description={feature.description}\r\n            index={index}\r\n          />\r\n        ))}\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-center mt-12 md:mt-16\">\r\n        <Button\r\n          className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] cursor-pointer px-8 py-6 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-300 relative z-10\"\r\n          onClick={() => router.push(\"/features\")}\r\n        >\r\n          <span>View All Features</span>\r\n          <ArrowRight className=\"w-5 h-5 ml-2\" />\r\n        </Button>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AACA;AACA;;;AAXA;;;;;;;;AAaA,gDAAgD;AAChD,MAAM,WAAW;IACf;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,iNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,uNAAA,CAAA,cAAW;QACjB,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,qNAAA,CAAA,YAAS;QACf,OAAO;QACP,aACE;QACF,SACE;IACJ;IACA;QACE,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,aACE;QACF,SACE;IACJ;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;yDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;6CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sJAAA,CAAA,UAAiB;oBAAC,SAAQ;oBAAO,WAAU;;;;;;;;;;;0BAG9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAsD;4BACxC;0CAC1B,6LAAC;gCAAK,WAAU;0CAA2B;;;;;;;;;;;;kCAE7C,6LAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;YAOhE,0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qKAAA,CAAA,UAAqB;oBAAC,UAAU;;;;;;;;;;;0BAKrC,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,2JAAA,CAAA,UAAW;wBAEV,MAAM,QAAQ,IAAI;wBAClB,OAAO,QAAQ,KAAK;wBACpB,aAAa,QAAQ,WAAW;wBAChC,OAAO;uBAJF;;;;;;;;;;0BASX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,WAAU;oBACV,SAAS,IAAM,OAAO,IAAI,CAAC;;sCAE3B,6LAAC;sCAAK;;;;;;sCACN,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKhC;GAnEwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/PricingCardContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createContext, useContext, useState, ReactNode } from \"react\";\r\n\r\ninterface PricingCardContextType {\r\n  featuresExpanded: boolean;\r\n  setFeaturesExpanded: (_expanded: boolean) => void;\r\n  toggleFeatures: () => void;\r\n}\r\n\r\nconst PricingCardContext = createContext<PricingCardContextType | undefined>(undefined);\r\n\r\nexport function PricingCardProvider({ children }: { children: ReactNode }) {\r\n  const [featuresExpanded, setFeaturesExpanded] = useState(false);\r\n\r\n  const toggleFeatures = () => {\r\n    setFeaturesExpanded(!featuresExpanded);\r\n  };\r\n\r\n  return (\r\n    <PricingCardContext.Provider value={{\r\n      featuresExpanded,\r\n      setFeaturesExpanded,\r\n      toggleFeatures\r\n    }}>\r\n      {children}\r\n    </PricingCardContext.Provider>\r\n  );\r\n}\r\n\r\nexport function usePricingCard() {\r\n  const context = useContext(PricingCardContext);\r\n  if (context === undefined) {\r\n    throw new Error('usePricingCard must be used within a PricingCardProvider');\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUA,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAsC;AAEtE,SAAS,oBAAoB,EAAE,QAAQ,EAA2B;;IACvE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,iBAAiB;QACrB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC,mBAAmB,QAAQ;QAAC,OAAO;YAClC;YACA;YACA;QACF;kBACG;;;;;;AAGP;GAhBgB;KAAA;AAkBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/PricingCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card } from \"@/components/ui/card\"; // Keep Card import for structure\r\nimport {\r\n  Check,\r\n  Clock,\r\n  CreditCard,\r\n  Store,\r\n  Users,\r\n  ShieldCheck,\r\n  AlertCircle,\r\n  Loader2,\r\n  ChevronDown,\r\n  ChevronUp,\r\n  Package,\r\n  Zap,\r\n  Sparkles,\r\n  Gift,\r\n} from \"lucide-react\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { PricingPlan } from \"@/lib/PricingPlans\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePricingCard } from \"./PricingCardContext\";\r\n\r\ninterface PricingCardProps {\r\n  plan: PricingPlan;\r\n  onButtonClick?: (_plan: PricingPlan) => void; // Prefixed 'plan' parameter in type signature\r\n  index: number; // Keep index for animation delay if needed\r\n  isLoading?: boolean; // Keep for button state\r\n  isCurrentPlan?: boolean; // Keep for button state/text\r\n  buttonTextOverride?: string; // Keep for flexibility\r\n}\r\n\r\n// Animation variant matching LandingPageClient\r\nconst itemFadeIn = {\r\n  hidden: { opacity: 0, y: 20 },\r\n  visible: (i: number) => ({\r\n    opacity: 1,\r\n    y: 0,\r\n    transition: { duration: 0.5, delay: i * 0.1, ease: \"easeOut\" },\r\n  }),\r\n};\r\n\r\n// Helper function to get the correct icon based on plan name\r\nconst getPlanIcon = (planName: string, featured: boolean) => {\r\n  const isEnterprise = planName === \"Enterprise Plan\";\r\n  const iconProps = {\r\n    className: cn(\r\n      \"w-5 h-5\",\r\n      featured\r\n        ? \"text-[var(--brand-gold)]\"\r\n        : isEnterprise\r\n        ? \"text-purple-600\"\r\n        : \"text-muted-foreground\"\r\n    ),\r\n  };\r\n  switch (planName) {\r\n    case \"Free Plan\":\r\n      return <Gift {...iconProps} />;\r\n    case \"Basic Plan\":\r\n      return <CreditCard {...iconProps} />;\r\n    case \"Growth Plan\":\r\n      return <Store {...iconProps} />;\r\n    case \"Pro Plan\":\r\n      return <ShieldCheck {...iconProps} />;\r\n    case \"Enterprise Plan\":\r\n      return <Users {...iconProps} />;\r\n    default:\r\n      return null;\r\n  }\r\n};\r\n\r\n// Helper function to get previous plan name\r\nconst getPreviousPlanName = (planName: string): string | null => {\r\n  switch (planName) {\r\n    case \"Growth Plan\":\r\n      return \"Basic\";\r\n    case \"Pro Plan\":\r\n      return \"Growth\";\r\n    case \"Enterprise Plan\":\r\n      return \"Pro\";\r\n    default:\r\n      return null;\r\n  }\r\n};\r\n\r\n// Helper function to filter features to only show available ones\r\nconst getAvailableFeatures = (features: string[]): string[] => {\r\n  return features.filter(feature => !feature.includes(\"❌\"));\r\n};\r\n\r\n// Helper function to get feature icon\r\nconst getFeatureIcon = (feature: string, featured: boolean) => {\r\n  const iconProps = {\r\n    className: cn(\r\n      \"w-4 h-4 mr-2 flex-shrink-0\",\r\n      featured ? \"text-[var(--brand-gold)]\" : \"text-green-500\"\r\n    ),\r\n  };\r\n\r\n  if (feature.includes(\"Product\")) return <Package {...iconProps} />;\r\n  if (feature.includes(\"Analytics\")) return <Zap {...iconProps} />;\r\n  return <Check {...iconProps} />;\r\n};\r\n\r\nexport default function PricingCard({\r\n  plan,\r\n  onButtonClick,\r\n  index,\r\n  isLoading = false,\r\n  isCurrentPlan = false,\r\n  buttonTextOverride,\r\n}: PricingCardProps) {\r\n  // Use global context for features expansion\r\n  const { featuresExpanded, toggleFeatures } = usePricingCard();\r\n\r\n  // Logic for button state\r\n  const isEnterprise = plan.id === \"enterprise\";\r\n  const isDisabled = (!plan.available && !isEnterprise) || isCurrentPlan || isLoading;\r\n  let buttonText = plan.button; // Default from plan data\r\n\r\n  if (buttonTextOverride) {\r\n    buttonText = buttonTextOverride;\r\n  } else if (isCurrentPlan) {\r\n    buttonText = \"Current Plan\";\r\n  }\r\n\r\n  // Get previous plan name (for \"Everything in X\" feature)\r\n  const previousPlanName = getPreviousPlanName(plan.name);\r\n\r\n  // Get only available features\r\n  const availableFeatures = getAvailableFeatures(plan.features);\r\n\r\n  return (\r\n    // Structure and classes with standardized width and centering\r\n    <motion.div\r\n      key={plan.id} // Use plan.id for key\r\n      custom={index} // Use index prop for stagger delay\r\n      variants={itemFadeIn}\r\n      className=\"w-full mx-auto\" // Full width with centering\r\n    >\r\n      <Card\r\n        className={cn(\r\n          \"bg-card border flex flex-col h-full w-full relative transition-all duration-300 rounded-xl p-3 md:p-4\", // Full width, minimal padding\r\n          plan.featured\r\n            ? \"border-[var(--brand-gold)] shadow-lg dark:shadow-[var(--brand-gold)]/15 pt-6 md:pt-8\" // Add extra top padding if featured for badge space\r\n            : isEnterprise\r\n            ? \"border-purple-500 shadow-lg dark:shadow-purple-500/15 pt-3 md:pt-4\" // Enterprise styling without extra padding\r\n            : \"border-border pt-3 md:pt-4\", // Keep original padding if not featured\r\n          !plan.available && \"opacity-70\" // Keep disabled visual cue\r\n        )}\r\n      >\r\n        {plan.featured && plan.mostPopular && (\r\n          // Adjusted positioning slightly, ensure z-index if needed\r\n          <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] px-4 py-1 rounded-full text-xs font-semibold shadow-md z-10\">\r\n            Most Popular\r\n          </div>\r\n        )}\r\n\r\n        {/* Header section matching LandingPageClient */}\r\n        {/* Removed CardHeader component, applying padding directly to Card */}\r\n        <div className=\"flex items-center mb-3\">\r\n          <div\r\n            className={cn(\r\n              \"p-1.5 rounded-full mr-2\",\r\n              plan.featured\r\n                ? \"bg-[var(--brand-gold)]/15\"\r\n                : isEnterprise\r\n                ? \"bg-purple-500/15\"\r\n                : \"bg-muted\"\r\n            )}\r\n          >\r\n            {getPlanIcon(plan.name, plan.featured || isEnterprise)}\r\n          </div>\r\n          <h3 className=\"text-lg md:text-xl font-semibold text-foreground\">{plan.name}</h3>\r\n        </div>\r\n\r\n        {/* Price section matching LandingPageClient */}\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex items-baseline gap-1\">\r\n            <span className=\"text-2xl md:text-3xl font-bold text-foreground\">\r\n              {plan.price}\r\n            </span>\r\n            <span className=\"text-muted-foreground text-sm\">{plan.period}</span>\r\n          </div>\r\n          {plan.savings && (\r\n            <span className=\"text-green-600 text-sm block mt-1 font-medium\">\r\n              <Sparkles className=\"inline-block w-3 h-3 mr-1\" />\r\n              {plan.savings}\r\n            </span>\r\n          )}\r\n          <p className=\"text-muted-foreground mt-2 text-sm\">\r\n            {plan.description}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Button moved above features section */}\r\n        <Button\r\n          onClick={() => !isDisabled && onButtonClick?.(plan)} // Use optional chaining for onButtonClick\r\n          size=\"lg\"\r\n          className={cn(\r\n            \"w-full mb-4 rounded-lg font-semibold flex items-center justify-center gap-2 transition-colors duration-200\",\r\n            isDisabled\r\n              ? \"bg-muted text-muted-foreground cursor-not-allowed\" // Disabled style from LandingPageClient\r\n              : isEnterprise\r\n              ? \"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white\" // Enterprise style\r\n              : plan.featured\r\n              ? \"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]\" // Featured available\r\n              : \"bg-primary hover:bg-primary/90 text-primary-foreground\" // Non-featured available\r\n          )}\r\n          disabled={isDisabled}\r\n        >\r\n          {isLoading ? (\r\n            <Loader2 className=\"w-5 h-5 animate-spin\" />\r\n          ) : (\r\n            <>\r\n              {/* Icons for button state */}\r\n              {!plan.available && !isCurrentPlan && (\r\n                <Clock className=\"w-4 h-4\" />\r\n              )}\r\n              {isCurrentPlan && <ShieldCheck className=\"w-4 h-4\" />}\r\n              {buttonText}\r\n            </>\r\n          )}\r\n        </Button>\r\n\r\n        {/* Features section with expand/collapse functionality */}\r\n        <div className=\"flex-1\">\r\n          {/* Features toggle button */}\r\n          <button\r\n            onClick={toggleFeatures}\r\n            className={cn(\r\n              \"flex items-center justify-center w-full mb-4 py-1 px-2 rounded-md text-sm font-medium transition-all\",\r\n              \"border border-transparent hover:border-border\",\r\n              featuresExpanded\r\n                ? \"text-foreground bg-muted/50\"\r\n                : \"text-muted-foreground hover:text-foreground\"\r\n            )}\r\n          >\r\n            <span className=\"mr-1\">\r\n              {featuresExpanded ? \"Hide\" : \"Show\"} features\r\n            </span>\r\n            {featuresExpanded ? (\r\n              <ChevronUp className=\"w-4 h-4\" />\r\n            ) : (\r\n              <ChevronDown className=\"w-4 h-4\" />\r\n            )}\r\n          </button>\r\n\r\n          {/* Preview of key features when collapsed */}\r\n          {!featuresExpanded && (\r\n            <ul className=\"space-y-3\">\r\n              {/* If this is not the Basic plan, show \"Everything in [Previous Plan]\" */}\r\n              {previousPlanName && (\r\n                <li className=\"flex items-start\">\r\n                  <div className=\"flex items-center text-sm\">\r\n                    <div className={cn(\r\n                      \"p-1 rounded-full mr-2 flex-shrink-0\",\r\n                      plan.featured ? \"bg-[var(--brand-gold)]/15\" : \"bg-muted\"\r\n                    )}>\r\n                      <Package className={cn(\r\n                        \"w-3 h-3\",\r\n                        plan.featured ? \"text-[var(--brand-gold)]\" : \"text-foreground\"\r\n                      )} />\r\n                    </div>\r\n                    <span className=\"font-medium\">Everything in {previousPlanName}</span>\r\n                  </div>\r\n                </li>\r\n              )}\r\n\r\n              {/* Show 2-3 key features specific to this plan */}\r\n              {availableFeatures.slice(0, previousPlanName ? 2 : 3).map((feature, i) => (\r\n                <li key={i} className=\"flex items-center text-sm\">\r\n                  {getFeatureIcon(feature, plan.featured)}\r\n                  <span>{feature}</span>\r\n                </li>\r\n              ))}\r\n\r\n              {availableFeatures.length > (previousPlanName ? 2 : 3) && (\r\n                <li className=\"text-sm text-muted-foreground text-center italic\">\r\n                  {availableFeatures.length - (previousPlanName ? 2 : 3)} more features...\r\n                </li>\r\n              )}\r\n            </ul>\r\n          )}\r\n\r\n          {/* Expanded features list with animation */}\r\n          <AnimatePresence>\r\n            {featuresExpanded && (\r\n              <motion.div\r\n                initial={{ opacity: 0, height: 0 }}\r\n                animate={{ opacity: 1, height: \"auto\" }}\r\n                exit={{ opacity: 0, height: 0 }}\r\n                transition={{ duration: 0.3 }}\r\n                className=\"overflow-hidden\"\r\n              >\r\n                <ul className=\"space-y-3\">\r\n                  {/* If this is not the Basic plan, show \"Everything in [Previous Plan]\" */}\r\n                  {previousPlanName && (\r\n                    <li className=\"flex items-start bg-muted/30 p-2 rounded-md\">\r\n                      <div className=\"flex items-center text-sm font-medium\">\r\n                        <div className={cn(\r\n                          \"p-1 rounded-full mr-2 flex-shrink-0\",\r\n                          plan.featured ? \"bg-[var(--brand-gold)]/15\" : \"bg-muted\"\r\n                        )}>\r\n                          <Package className={cn(\r\n                            \"w-3 h-3\",\r\n                            plan.featured ? \"text-[var(--brand-gold)]\" : \"text-foreground\"\r\n                          )} />\r\n                        </div>\r\n                        <span>Everything in {previousPlanName}</span>\r\n                      </div>\r\n                    </li>\r\n                  )}\r\n\r\n                  {/* Show all available features specific to this plan */}\r\n                  {availableFeatures.map((feature, i) => (\r\n                    <li key={i} className=\"flex items-center text-sm\">\r\n                      {getFeatureIcon(feature, plan.featured)}\r\n                      <span>{feature}</span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n        </div>\r\n\r\n        {/* Tooltip matching LandingPageClient */}\r\n        {!plan.available && !isCurrentPlan && !isEnterprise && (\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                {/* Adjusted tooltip trigger position */}\r\n                <div className=\"absolute top-3 right-3 cursor-help\">\r\n                  <AlertCircle className=\"w-5 h-5 text-muted-foreground hover:text-foreground\" />\r\n                </div>\r\n              </TooltipTrigger>\r\n              <TooltipContent className=\"bg-popover border-border text-popover-foreground\">\r\n                <p>{plan.id === \"free\" ? \"Free plan is automatically managed by the system.\" : \"This plan is coming soon.\"}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        )}\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA,0NAA6C,iCAAiC;AAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAOA;AACA;;;AA9BA;;;;;;;;AAyCA,+CAA+C;AAC/C,MAAM,aAAa;IACjB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS,CAAC,IAAc,CAAC;YACvB,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,OAAO,IAAI;gBAAK,MAAM;YAAU;QAC/D,CAAC;AACH;AAEA,6DAA6D;AAC7D,MAAM,cAAc,CAAC,UAAkB;IACrC,MAAM,eAAe,aAAa;IAClC,MAAM,YAAY;QAChB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,WACA,WACI,6BACA,eACA,oBACA;IAER;IACA,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;gBAAE,GAAG,SAAS;;;;;;QAC5B,KAAK;YACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;gBAAE,GAAG,SAAS;;;;;;QAClC,KAAK;YACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gBAAE,GAAG,SAAS;;;;;;QAC7B,KAAK;YACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;gBAAE,GAAG,SAAS;;;;;;QACnC,KAAK;YACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gBAAE,GAAG,SAAS;;;;;;QAC7B;YACE,OAAO;IACX;AACF;AAEA,4CAA4C;AAC5C,MAAM,sBAAsB,CAAC;IAC3B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,iEAAiE;AACjE,MAAM,uBAAuB,CAAC;IAC5B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ,CAAC;AACtD;AAEA,sCAAsC;AACtC,MAAM,iBAAiB,CAAC,SAAiB;IACvC,MAAM,YAAY;QAChB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,6BAA6B;IAE5C;IAEA,IAAI,QAAQ,QAAQ,CAAC,YAAY,qBAAO,6LAAC,2MAAA,CAAA,UAAO;QAAE,GAAG,SAAS;;;;;;IAC9D,IAAI,QAAQ,QAAQ,CAAC,cAAc,qBAAO,6LAAC,mMAAA,CAAA,MAAG;QAAE,GAAG,SAAS;;;;;;IAC5D,qBAAO,6LAAC,uMAAA,CAAA,QAAK;QAAE,GAAG,SAAS;;;;;;AAC7B;AAEe,SAAS,YAAY,EAClC,IAAI,EACJ,aAAa,EACb,KAAK,EACL,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,kBAAkB,EACD;;IACjB,4CAA4C;IAC5C,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD;IAE1D,yBAAyB;IACzB,MAAM,eAAe,KAAK,EAAE,KAAK;IACjC,MAAM,aAAa,AAAC,CAAC,KAAK,SAAS,IAAI,CAAC,gBAAiB,iBAAiB;IAC1E,IAAI,aAAa,KAAK,MAAM,EAAE,yBAAyB;IAEvD,IAAI,oBAAoB;QACtB,aAAa;IACf,OAAO,IAAI,eAAe;QACxB,aAAa;IACf;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,oBAAoB,KAAK,IAAI;IAEtD,8BAA8B;IAC9B,MAAM,oBAAoB,qBAAqB,KAAK,QAAQ;IAE5D,OACE,8DAA8D;kBAC9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAET,QAAQ;QACR,UAAU;QACV,WAAU,iBAAiB,4BAA4B;;kBAEvD,cAAA,6LAAC,4HAAA,CAAA,OAAI;YACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yGACA,KAAK,QAAQ,GACT,uFAAuF,oDAAoD;eAC3I,eACA,qEAAqE,2CAA2C;eAChH,8BACJ,CAAC,KAAK,SAAS,IAAI,aAAa,2BAA2B;;;gBAG5D,KAAK,QAAQ,IAAI,KAAK,WAAW,IAChC,0DAA0D;8BAC1D,6LAAC;oBAAI,WAAU;8BAA6L;;;;;;8BAO9M,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2BACA,KAAK,QAAQ,GACT,8BACA,eACA,qBACA;sCAGL,YAAY,KAAK,IAAI,EAAE,KAAK,QAAQ,IAAI;;;;;;sCAE3C,6LAAC;4BAAG,WAAU;sCAAoD,KAAK,IAAI;;;;;;;;;;;;8BAI7E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAK,WAAU;8CAAiC,KAAK,MAAM;;;;;;;;;;;;wBAE7D,KAAK,OAAO,kBACX,6LAAC;4BAAK,WAAU;;8CACd,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,KAAK,OAAO;;;;;;;sCAGjB,6LAAC;4BAAE,WAAU;sCACV,KAAK,WAAW;;;;;;;;;;;;8BAKrB,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAS,IAAM,CAAC,cAAc,gBAAgB;oBAC9C,MAAK;oBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8GACA,aACI,oDAAoD,wCAAwC;uBAC5F,eACA,kGAAkG,mBAAmB;uBACrH,KAAK,QAAQ,GACb,gGAAgG,qBAAqB;uBACrH,yDAAyD,yBAAyB;;oBAExF,UAAU;8BAET,0BACC,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;6CAEnB;;4BAEG,CAAC,KAAK,SAAS,IAAI,CAAC,+BACnB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAElB,+BAAiB,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACxC;;;;;;;;8BAMP,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS;4BACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wGACA,iDACA,mBACI,gCACA;;8CAGN,6LAAC;oCAAK,WAAU;;wCACb,mBAAmB,SAAS;wCAAO;;;;;;;gCAErC,iCACC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;wBAK1B,CAAC,kCACA,6LAAC;4BAAG,WAAU;;gCAEX,kCACC,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,uCACA,KAAK,QAAQ,GAAG,8BAA8B;0DAE9C,cAAA,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACnB,WACA,KAAK,QAAQ,GAAG,6BAA6B;;;;;;;;;;;0DAGjD,6LAAC;gDAAK,WAAU;;oDAAc;oDAAe;;;;;;;;;;;;;;;;;;gCAMlD,kBAAkB,KAAK,CAAC,GAAG,mBAAmB,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,kBAClE,6LAAC;wCAAW,WAAU;;4CACnB,eAAe,SAAS,KAAK,QAAQ;0DACtC,6LAAC;0DAAM;;;;;;;uCAFA;;;;;gCAMV,kBAAkB,MAAM,GAAG,CAAC,mBAAmB,IAAI,CAAC,mBACnD,6LAAC;oCAAG,WAAU;;wCACX,kBAAkB,MAAM,GAAG,CAAC,mBAAmB,IAAI,CAAC;wCAAE;;;;;;;;;;;;;sCAO/D,6LAAC,4LAAA,CAAA,kBAAe;sCACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAG,WAAU;;wCAEX,kCACC,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,uCACA,KAAK,QAAQ,GAAG,8BAA8B;kEAE9C,cAAA,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACnB,WACA,KAAK,QAAQ,GAAG,6BAA6B;;;;;;;;;;;kEAGjD,6LAAC;;4DAAK;4DAAe;;;;;;;;;;;;;;;;;;wCAM1B,kBAAkB,GAAG,CAAC,CAAC,SAAS,kBAC/B,6LAAC;gDAAW,WAAU;;oDACnB,eAAe,SAAS,KAAK,QAAQ;kEACtC,6LAAC;kEAAM;;;;;;;+CAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAYpB,CAAC,KAAK,SAAS,IAAI,CAAC,iBAAiB,CAAC,8BACrC,6LAAC,+HAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0CACN,6LAAC,+HAAA,CAAA,iBAAc;gCAAC,OAAO;0CAErB,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG3B,6LAAC,+HAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,6LAAC;8CAAG,KAAK,EAAE,KAAK,SAAS,sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA3MpF,KAAK,EAAE;;;;;AAmNlB;GAlPwB;;QASuB,2IAAA,CAAA,iBAAc;;;KATrC", "debugId": null}}, {"offset": {"line": 1430, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/BillingToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface BillingToggleProps {\r\n  billingCycle: \"monthly\" | \"yearly\";\r\n  setBillingCycle: (_cycle: \"monthly\" | \"yearly\") => void;\r\n}\r\n\r\nexport default function BillingToggle({\r\n  billingCycle,\r\n  setBillingCycle,\r\n}: BillingToggleProps) {\r\n  return (\r\n    <div className=\"border border-[var(--brand-gold)] rounded-full relative inline-flex\">\r\n      {/* Static background */}\r\n      <div className=\"absolute -inset-2 bg-gradient-to-r from-blue-500/20 via-[var(--brand-gold)]/30 to-blue-500/20 rounded-full blur-lg opacity-60\" />\r\n\r\n      <div className=\"space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10\">\r\n        <Button\r\n          onClick={() => setBillingCycle(\"monthly\")}\r\n          variant={billingCycle === \"monthly\" ? \"default\" : \"ghost\"}\r\n          size=\"sm\"\r\n          className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${\r\n            billingCycle === \"monthly\"\r\n              ? \"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md\"\r\n              : \"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50\"\r\n          }`}\r\n        >\r\n          <span>Monthly</span>\r\n        </Button>\r\n        <Button\r\n          onClick={() => setBillingCycle(\"yearly\")}\r\n          variant={billingCycle === \"yearly\" ? \"default\" : \"ghost\"}\r\n          size=\"sm\"\r\n          className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ${\r\n            billingCycle === \"yearly\"\r\n              ? \"bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md\"\r\n              : \"text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50\"\r\n          }`}\r\n        >\r\n          <span>Yearly</span>\r\n          <span className=\"bg-gradient-to-r from-green-600 to-green-500 text-white text-xs px-2 py-0.5 rounded-full shadow-sm\">\r\n            Save 20%\r\n          </span>\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,cAAc,EACpC,YAAY,EACZ,eAAe,EACI;IACnB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,SAAS,iBAAiB,YAAY,YAAY;wBAClD,MAAK;wBACL,WAAW,CAAC,sFAAsF,EAChG,iBAAiB,YACb,yHACA,qHACJ;kCAEF,cAAA,6LAAC;sCAAK;;;;;;;;;;;kCAER,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,SAAS,iBAAiB,WAAW,YAAY;wBACjD,MAAK;wBACL,WAAW,CAAC,8GAA8G,EACxH,iBAAiB,WACb,yHACA,qHACJ;;0CAEF,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAqG;;;;;;;;;;;;;;;;;;;;;;;;AAO/H;KAxCwB", "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/FloatingPricingElements.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { \r\n  CreditCard, \r\n  Star, \r\n  CheckCircle, \r\n  DollarSign, \r\n  Percent, \r\n  Calendar, \r\n  Clock, \r\n  Award,\r\n  Zap\r\n} from \"lucide-react\";\r\n\r\ninterface FloatingPricingElementsProps {\r\n  minimal?: boolean;\r\n}\r\n\r\nexport default function FloatingPricingElements({ minimal = false }: FloatingPricingElementsProps) {\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n    \r\n    checkMobile();\r\n    window.addEventListener('resize', checkMobile);\r\n    \r\n    return () => {\r\n      window.removeEventListener('resize', checkMobile);\r\n    };\r\n  }, []);\r\n\r\n  // Define floating elements with responsive positioning\r\n  const elements = [\r\n    {\r\n      id: 1,\r\n      icon: CreditCard,\r\n      top: \"10%\",\r\n      left: \"5%\",\r\n      size: 24,\r\n      color: \"var(--brand-gold)\",\r\n      duration: 8,\r\n      delay: 0,\r\n    },\r\n    {\r\n      id: 2,\r\n      icon: Star,\r\n      top: \"15%\",\r\n      right: \"8%\",\r\n      size: 20,\r\n      color: \"var(--brand-gold)\",\r\n      duration: 7,\r\n      delay: 0.5,\r\n    },\r\n    {\r\n      id: 3,\r\n      icon: CheckCircle,\r\n      bottom: \"20%\",\r\n      left: \"7%\",\r\n      size: 22,\r\n      color: \"rgb(59, 130, 246)\", // blue\r\n      duration: 9,\r\n      delay: 1,\r\n    },\r\n    {\r\n      id: 4,\r\n      icon: DollarSign,\r\n      bottom: \"25%\",\r\n      right: \"10%\",\r\n      size: 26,\r\n      color: \"var(--brand-gold)\",\r\n      duration: 8.5,\r\n      delay: 1.5,\r\n    },\r\n    {\r\n      id: 5,\r\n      icon: Percent,\r\n      top: \"40%\",\r\n      left: \"3%\",\r\n      size: 18,\r\n      color: \"rgb(59, 130, 246)\", // blue\r\n      duration: 7.5,\r\n      delay: 2,\r\n    },\r\n    {\r\n      id: 6,\r\n      icon: Calendar,\r\n      top: \"45%\",\r\n      right: \"4%\",\r\n      size: 20,\r\n      color: \"rgb(139, 92, 246)\", // purple\r\n      duration: 9.5,\r\n      delay: 2.5,\r\n    },\r\n    {\r\n      id: 7,\r\n      icon: Clock,\r\n      bottom: \"10%\",\r\n      left: \"15%\",\r\n      size: 16,\r\n      color: \"rgb(139, 92, 246)\", // purple\r\n      duration: 8,\r\n      delay: 3,\r\n    },\r\n    {\r\n      id: 8,\r\n      icon: Award,\r\n      bottom: \"15%\",\r\n      right: \"15%\",\r\n      size: 22,\r\n      color: \"var(--brand-gold)\",\r\n      duration: 9,\r\n      delay: 3.5,\r\n    },\r\n    {\r\n      id: 9,\r\n      icon: Zap,\r\n      top: \"30%\",\r\n      left: \"12%\",\r\n      size: 18,\r\n      color: \"var(--brand-gold)\",\r\n      duration: 7,\r\n      delay: 4,\r\n    },\r\n  ];\r\n\r\n  // For minimal mode, show fewer elements\r\n  const displayElements = minimal ? elements.filter(e => [1, 2, 4, 6, 8].includes(e.id)) : elements;\r\n\r\n  if (!isClient) return null;\r\n  if (isMobile && minimal) return null;\r\n\r\n  return (\r\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n      {displayElements.map((element) => {\r\n        const Icon = element.icon;\r\n        return (\r\n          <motion.div\r\n            key={element.id}\r\n            className=\"absolute\"\r\n            style={{\r\n              top: element.top,\r\n              left: element.left,\r\n              right: element.right,\r\n              bottom: element.bottom,\r\n              color: element.color,\r\n              opacity: 0.2,\r\n            }}\r\n            animate={{\r\n              y: [0, -15, 0],\r\n              x: [0, 10, 0],\r\n              rotate: [0, 10, 0],\r\n              scale: [1, 1.1, 1],\r\n              opacity: [0.15, 0.25, 0.15],\r\n            }}\r\n            transition={{\r\n              duration: element.duration,\r\n              delay: element.delay,\r\n              repeat: Infinity,\r\n              repeatType: \"mirror\",\r\n              ease: \"easeInOut\",\r\n            }}\r\n          >\r\n            <Icon size={element.size} />\r\n          </motion.div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAoBe,SAAS,wBAAwB,EAAE,UAAU,KAAK,EAAgC;;IAC/F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,YAAY;YACZ,MAAM;iEAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;qDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;4CAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM,qNAAA,CAAA,aAAU;YAChB,KAAK;YACL,MAAM;YACN,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,qMAAA,CAAA,OAAI;YACV,KAAK;YACL,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,8NAAA,CAAA,cAAW;YACjB,QAAQ;YACR,MAAM;YACN,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,qNAAA,CAAA,aAAU;YAChB,QAAQ;YACR,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,2MAAA,CAAA,UAAO;YACb,KAAK;YACL,MAAM;YACN,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,6MAAA,CAAA,WAAQ;YACd,KAAK;YACL,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,uMAAA,CAAA,QAAK;YACX,QAAQ;YACR,MAAM;YACN,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,uMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,mMAAA,CAAA,MAAG;YACT,KAAK;YACL,MAAM;YACN,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;QACT;KACD;IAED,wCAAwC;IACxC,MAAM,kBAAkB,UAAU,SAAS,MAAM,CAAC,CAAA,IAAK;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK;IAEzF,IAAI,CAAC,UAAU,OAAO;IACtB,IAAI,YAAY,SAAS,OAAO;IAEhC,qBACE,6LAAC;QAAI,WAAU;kBACZ,gBAAgB,GAAG,CAAC,CAAC;YACpB,MAAM,OAAO,QAAQ,IAAI;YACzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,KAAK,QAAQ,GAAG;oBAChB,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,QAAQ,QAAQ,MAAM;oBACtB,OAAO,QAAQ,KAAK;oBACpB,SAAS;gBACX;gBACA,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,QAAQ;wBAAC;wBAAG;wBAAI;qBAAE;oBAClB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAM;wBAAM;qBAAK;gBAC7B;gBACA,YAAY;oBACV,UAAU,QAAQ,QAAQ;oBAC1B,OAAO,QAAQ,KAAK;oBACpB,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;0BAEA,cAAA,6LAAC;oBAAK,MAAM,QAAQ,IAAI;;;;;;eAzBnB,QAAQ,EAAE;;;;;QA4BrB;;;;;;AAGN;GA3JwB;KAAA", "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/animations.ts"], "sourcesContent": ["\"use client\";\r\n\r\n// Simple animation variants for sections\r\nexport const sectionFadeIn = {\r\n  hidden: { opacity: 0 },\r\n  visible: {\r\n    opacity: 1,\r\n    transition: { duration: 0.2 }, // Faster animation\r\n  },\r\n};\r\n\r\n// Simple animation variants for items\r\nexport const itemFadeIn = {\r\n  hidden: { opacity: 0 },\r\n  visible: {\r\n    opacity: 1,\r\n    transition: { duration: 0.2 }, // Faster animation\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAGO,MAAM,gBAAgB;IAC3B,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 1782, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/PricingSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useState } from \"react\";\r\nimport PricingCard from \"@/app/components/PricingCard\";\r\nimport { PricingCardProvider } from \"@/app/components/PricingCardContext\";\r\nimport { pricingPlans, PricingPlan } from \"@/lib/PricingPlans\";\r\nimport SectionBackground from \"../SectionBackground\";\r\nimport BillingToggle from \"./BillingToggle\";\r\nimport FloatingPricingElements from \"./FloatingPricingElements\";\r\nimport { sectionFadeIn, itemFadeIn } from \"./animations\";\r\n\r\nexport default function PricingSection() {\r\n  const [billingCycle, setBillingCycle] = useState<\"monthly\" | \"yearly\">(\"monthly\");\r\n  const [, setIsHovering] = useState(false);\r\n  const router = useRouter();\r\n  const plans = pricingPlans(billingCycle);\r\n\r\n  const handleGetStarted = () => {\r\n    router.push(`/register`);\r\n  };\r\n\r\n  const handlePlanClick = (plan: PricingPlan) => {\r\n    if (plan.id === \"enterprise\") {\r\n      // For Enterprise, redirect to contact page\r\n      router.push(\"/contact\");\r\n    } else if (plan.available) {\r\n      handleGetStarted();\r\n    }\r\n  };\r\n\r\n  // Add hover state for enhanced animations\r\n  const handleMouseEnter = () => {\r\n    setIsHovering(true);\r\n  };\r\n\r\n  const handleMouseLeave = () => {\r\n    setIsHovering(false);\r\n  };\r\n\r\n  return (\r\n    <PricingCardProvider>\r\n      <motion.section\r\n        variants={sectionFadeIn}\r\n        initial=\"hidden\"\r\n        whileInView=\"visible\"\r\n        viewport={{ once: true, amount: 0.05 }} // Reveal when just 5% is in view\r\n        className=\"py-10 px-2 md:px-4 max-w-full mx-auto relative\"\r\n        onMouseEnter={handleMouseEnter}\r\n        onMouseLeave={handleMouseLeave}\r\n      >\r\n      {/* Enhanced background with SectionBackground component */}\r\n      <div className=\"absolute inset-0 -z-10\">\r\n        <SectionBackground variant=\"blue\" intensity=\"low\" />\r\n      </div>\r\n\r\n      {/* Floating pricing elements */}\r\n      <FloatingPricingElements />\r\n      <motion.div\r\n        variants={itemFadeIn}\r\n        custom={0}\r\n        className=\"text-center max-w-3xl mx-auto mb-12\"\r\n      >\r\n        <div className=\"relative inline-block\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-foreground mb-6\">\r\n            Simple,{\" \"}\r\n            <span className=\"text-[var(--brand-gold)] relative\">\r\n              Transparent\r\n              {/* Animated underline */}\r\n              <motion.div\r\n                className=\"absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full\"\r\n                initial={{ width: 0, left: \"50%\" }}\r\n                animate={{ width: \"100%\", left: 0 }}\r\n                transition={{ duration: 1, delay: 0.5 }}\r\n              />\r\n              {/* Subtle glow effect */}\r\n              <motion.div\r\n                className=\"absolute -inset-2 bg-[var(--brand-gold)]/20 rounded-full blur-xl\"\r\n                animate={{ opacity: 0.8 }}\r\n                initial={{ opacity: 0.4 }}\r\n                transition={{\r\n                  duration: 1,\r\n                  repeat: Infinity,\r\n                  repeatType: \"reverse\",\r\n                  ease: \"easeInOut\"\r\n                }}\r\n              />\r\n            </span>{\" \"}\r\n            Pricing\r\n          </h2>\r\n        </div>\r\n        <p className=\"text-lg text-neutral-600 dark:text-neutral-300 mb-8 max-w-2xl mx-auto\">\r\n          Choose the perfect plan for your business needs and elevate your\r\n          digital presence with Dukancard&apos;s premium digital card\r\n          solutions.\r\n        </p>\r\n      </motion.div>\r\n\r\n      {/* Billing Toggle */}\r\n      <motion.div\r\n        variants={itemFadeIn}\r\n        custom={1}\r\n        className=\"flex justify-center items-center mb-16\"\r\n      >\r\n        <BillingToggle\r\n          billingCycle={billingCycle}\r\n          setBillingCycle={setBillingCycle}\r\n        />\r\n      </motion.div>\r\n\r\n      {/* Pricing Cards with enhanced animations */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-1 md:gap-2 items-stretch px-1 md:px-2\">\r\n        {plans.map((plan, index) => (\r\n          <motion.div\r\n            key={plan.id}\r\n            custom={index + 2} // Stagger delay\r\n            variants={itemFadeIn}\r\n            className=\"w-full relative\" // Added relative for positioning\r\n          >\r\n            {/* Highlight glow for popular plan */}\r\n            {plan.mostPopular && (\r\n              <motion.div\r\n                className=\"absolute -inset-2 bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/30 rounded-xl blur-lg -z-10\"\r\n                initial={{ opacity: 0.5, scale: 0.95 }}\r\n                animate={{\r\n                  opacity: [0.5, 0.8, 0.5],\r\n                  scale: [0.95, 1, 0.95]\r\n                }}\r\n                transition={{\r\n                  duration: 3,\r\n                  repeat: Infinity,\r\n                  repeatType: \"mirror\"\r\n                }}\r\n              />\r\n            )}\r\n\r\n            {/* Enhanced card without hover effect */}\r\n            <div>\r\n              <PricingCard\r\n                plan={plan}\r\n                index={index}\r\n                onButtonClick={() => handlePlanClick(plan)}\r\n              />\r\n            </div>\r\n\r\n            {/* Removed redundant \"Most Popular\" label */}\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n    </motion.section>\r\n    </PricingCardProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,GAAG,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;IAE3B,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,CAAC,SAAS,CAAC;IACzB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,EAAE,KAAK,cAAc;YAC5B,2CAA2C;YAC3C,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,KAAK,SAAS,EAAE;YACzB;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,mBAAmB;QACvB,cAAc;IAChB;IAEA,MAAM,mBAAmB;QACvB,cAAc;IAChB;IAEA,qBACE,6LAAC,2IAAA,CAAA,sBAAmB;kBAClB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;YACb,UAAU,yJAAA,CAAA,gBAAa;YACvB,SAAQ;YACR,aAAY;YACZ,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAK;YACrC,WAAU;YACV,cAAc;YACd,cAAc;;8BAGhB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,sJAAA,CAAA,UAAiB;wBAAC,SAAQ;wBAAO,WAAU;;;;;;;;;;;8BAI9C,6LAAC,uKAAA,CAAA,UAAuB;;;;;8BACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU,yJAAA,CAAA,aAAU;oBACpB,QAAQ;oBACR,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;oCAAsD;oCAC1D;kDACR,6LAAC;wCAAK,WAAU;;4CAAoC;0DAGlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;oDAAG,MAAM;gDAAM;gDACjC,SAAS;oDAAE,OAAO;oDAAQ,MAAM;gDAAE;gDAClC,YAAY;oDAAE,UAAU;oDAAG,OAAO;gDAAI;;;;;;0DAGxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAI;gDACxB,SAAS;oDAAE,SAAS;gDAAI;gDACxB,YAAY;oDACV,UAAU;oDACV,QAAQ;oDACR,YAAY;oDACZ,MAAM;gDACR;;;;;;;;;;;;oCAEI;oCAAI;;;;;;;;;;;;sCAIhB,6LAAC;4BAAE,WAAU;sCAAwE;;;;;;;;;;;;8BAQvF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU,yJAAA,CAAA,aAAU;oBACpB,QAAQ;oBACR,WAAU;8BAEV,cAAA,6LAAC,6JAAA,CAAA,UAAa;wBACZ,cAAc;wBACd,iBAAiB;;;;;;;;;;;8BAKrB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,QAAQ,QAAQ;4BAChB,UAAU,yJAAA,CAAA,aAAU;4BACpB,WAAU,kBAAkB,iCAAiC;;;gCAG5D,KAAK,WAAW,kBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAK,OAAO;oCAAK;oCACrC,SAAS;wCACP,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;wCACxB,OAAO;4CAAC;4CAAM;4CAAG;yCAAK;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,YAAY;oCACd;;;;;;8CAKJ,6LAAC;8CACC,cAAA,6LAAC,oIAAA,CAAA,UAAW;wCACV,MAAM;wCACN,OAAO;wCACP,eAAe,IAAM,gBAAgB;;;;;;;;;;;;2BA3BpC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAsCxB;GA5IwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/TestimonialCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { CheckCircle, Star } from \"lucide-react\";\r\n\r\ninterface TestimonialCardProps {\r\n  name: string;\r\n  business: string;\r\n  quote: string;\r\n  _index: number;\r\n  rating?: number;\r\n}\r\n\r\nexport default function TestimonialCard({\r\n  name,\r\n  business,\r\n  quote,\r\n  _index,\r\n  rating = 5,\r\n}: TestimonialCardProps) {\r\n  return (\r\n    <div className=\"h-full\">\r\n      <Card className=\"bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 p-6 h-full hover:shadow-lg transition-all duration-300 hover:border-[var(--brand-gold)]/70 dark:hover:shadow-[var(--brand-gold)]/10 rounded-xl group relative overflow-hidden\">\r\n        {/* Subtle gradient overlay */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-br from-white/0 to-purple-500/5 dark:from-transparent dark:to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\r\n\r\n        {/* Avatar and user info with enhanced styling */}\r\n        <div className=\"flex items-center mb-6 relative z-10\">\r\n          <div className=\"relative\">\r\n            <div className=\"w-14 h-14 rounded-full bg-gradient-to-br from-[var(--brand-gold)] via-amber-500 to-purple-500 flex items-center justify-center text-white font-bold text-lg shadow-md relative z-10\">\r\n              {name.charAt(0)}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"ml-4\">\r\n            <h3 className=\"text-lg font-semibold text-foreground\">\r\n              {name}\r\n            </h3>\r\n            <div className=\"flex items-center\">\r\n              <p className=\"text-purple-500 dark:text-purple-400 text-sm font-medium\">\r\n                {business}\r\n              </p>\r\n              {/* Verified badge */}\r\n              <div className=\"ml-2 bg-green-100 dark:bg-green-900/30 rounded-full px-2 py-0.5 text-[10px] text-green-700 dark:text-green-400 font-medium flex items-center hover:scale-110 transition-transform duration-200\">\r\n                <CheckCircle className=\"w-3 h-3 mr-1\" />\r\n                Verified\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quote with enhanced styling */}\r\n        <div className=\"relative mb-6 z-10\">\r\n          {/* Quote marks */}\r\n          <div className=\"absolute -top-2 -left-1 text-purple-200 dark:text-purple-900 text-4xl opacity-50\">&ldquo;</div>\r\n          <p className=\"text-neutral-600 dark:text-neutral-300 italic pl-3 pr-3 relative\">\r\n            {quote}\r\n          </p>\r\n          <div className=\"absolute -bottom-4 -right-1 text-purple-200 dark:text-purple-900 text-4xl opacity-50\">&rdquo;</div>\r\n        </div>\r\n\r\n        {/* Simple star rating */}\r\n        <div className=\"flex text-[var(--brand-gold)] relative z-10\">\r\n          {Array(5)\r\n            .fill(0)\r\n            .map((_, i) => (\r\n              <Star\r\n                key={i}\r\n                size={18}\r\n                className={i < rating\r\n                  ? \"fill-[var(--brand-gold)] text-[var(--brand-gold)]\"\r\n                  : \"text-neutral-300 dark:text-neutral-600\"\r\n                }\r\n              />\r\n            ))}\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAae,SAAS,gBAAgB,EACtC,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,MAAM,EACN,SAAS,CAAC,EACW;IACrB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;;8BAEd,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,KAAK,MAAM,CAAC;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV;;;;;;sDAGH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCAAmF;;;;;;sCAClG,6LAAC;4BAAE,WAAU;sCACV;;;;;;sCAEH,6LAAC;4BAAI,WAAU;sCAAuF;;;;;;;;;;;;8BAIxG,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GACJ,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,6LAAC,qMAAA,CAAA,OAAI;4BAEH,MAAM;4BACN,WAAW,IAAI,SACX,sDACA;2BAJC;;;;;;;;;;;;;;;;;;;;;AAYrB;KAlEwB", "debugId": null}}, {"offset": {"line": 2232, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/TestimonialCarousel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { ChevronLeft, ChevronRight, Star } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface Testimonial {\r\n  name: string;\r\n  business: string;\r\n  quote: string;\r\n  rating?: number;\r\n}\r\n\r\ninterface TestimonialCarouselProps {\r\n  testimonials: Testimonial[];\r\n  autoScroll?: boolean;\r\n  interval?: number;\r\n}\r\n\r\nexport default function TestimonialCarousel({\r\n  testimonials,\r\n  autoScroll = true,\r\n  interval = 8000, // Increased interval for better performance\r\n}: TestimonialCarouselProps) {\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [isPaused, setIsPaused] = useState(false);\r\n  const autoScrollTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Auto-scroll functionality with longer interval\r\n  useEffect(() => {\r\n    if (autoScroll && !isPaused) {\r\n      autoScrollTimerRef.current = setTimeout(() => {\r\n        setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);\r\n      }, interval);\r\n    }\r\n\r\n    return () => {\r\n      if (autoScrollTimerRef.current) {\r\n        clearTimeout(autoScrollTimerRef.current);\r\n      }\r\n    };\r\n  }, [currentIndex, autoScroll, interval, isPaused, testimonials.length]);\r\n\r\n  // Navigation functions\r\n  const goToNext = () => {\r\n    if (autoScrollTimerRef.current) {\r\n      clearTimeout(autoScrollTimerRef.current);\r\n    }\r\n    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);\r\n    setIsPaused(true);\r\n\r\n    // Resume auto-scroll after 10 seconds of inactivity\r\n    setTimeout(() => {\r\n      setIsPaused(false);\r\n    }, 10000);\r\n  };\r\n\r\n  const goToPrevious = () => {\r\n    if (autoScrollTimerRef.current) {\r\n      clearTimeout(autoScrollTimerRef.current);\r\n    }\r\n    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);\r\n    setIsPaused(true);\r\n\r\n    // Resume auto-scroll after 10 seconds of inactivity\r\n    setTimeout(() => {\r\n      setIsPaused(false);\r\n    }, 10000);\r\n  };\r\n\r\n  const goToIndex = (index: number) => {\r\n    if (autoScrollTimerRef.current) {\r\n      clearTimeout(autoScrollTimerRef.current);\r\n    }\r\n    setCurrentIndex(index);\r\n    setIsPaused(true);\r\n\r\n    // Resume auto-scroll after 10 seconds of inactivity\r\n    setTimeout(() => {\r\n      setIsPaused(false);\r\n    }, 10000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative w-full overflow-hidden py-4\">\r\n      {/* Carousel container */}\r\n      <div className=\"relative w-full min-h-[280px] overflow-hidden rounded-xl\">\r\n        <div className=\"absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"w-full max-w-2xl mx-auto p-8 bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-md\">\r\n            {/* Quote mark */}\r\n            <div className=\"text-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)]/30 text-6xl font-serif absolute top-4 left-6\">\r\n              &quot;\r\n            </div>\r\n\r\n            {/* Testimonial content */}\r\n            <div className=\"flex flex-col items-center text-center relative z-10\">\r\n              {/* Quote */}\r\n              <p className=\"text-lg text-neutral-700 dark:text-neutral-300 mb-6 relative z-10\">\r\n                &quot;{testimonials[currentIndex].quote}&quot;\r\n              </p>\r\n\r\n              {/* Rating stars if available */}\r\n              {testimonials[currentIndex].rating && (\r\n                <div className=\"flex items-center gap-1 mb-4\">\r\n                  {Array.from({ length: 5 }).map((_, i) => (\r\n                    <Star\r\n                      key={i}\r\n                      size={16}\r\n                      className={`${\r\n                        i < (testimonials[currentIndex].rating || 0)\r\n                          ? \"text-[var(--brand-gold)] fill-[var(--brand-gold)]\"\r\n                          : \"text-neutral-300 dark:text-neutral-600\"\r\n                      }`}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              )}\r\n\r\n              {/* Author info */}\r\n              <div className=\"flex flex-col items-center\">\r\n                <h4 className=\"font-semibold text-lg\">\r\n                  {testimonials[currentIndex].name}\r\n                </h4>\r\n                <p className=\"text-neutral-500 dark:text-neutral-400 text-sm\">\r\n                  {testimonials[currentIndex].business}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation buttons */}\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10\"\r\n          onClick={goToPrevious}\r\n        >\r\n          <ChevronLeft className=\"h-5 w-5\" />\r\n        </Button>\r\n\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10\"\r\n          onClick={goToNext}\r\n        >\r\n          <ChevronRight className=\"h-5 w-5\" />\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Dots indicator */}\r\n      <div className=\"flex justify-center mt-4 gap-1.5\">\r\n        {testimonials.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            className={`h-2 rounded-full transition-all duration-300 ${\r\n              index === currentIndex\r\n                ? \"w-6 bg-[var(--brand-gold)]\"\r\n                : \"w-2 bg-neutral-300 dark:bg-neutral-700\"\r\n            }`}\r\n            onClick={() => goToIndex(index)}\r\n            aria-label={`Go to testimonial ${index + 1}`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAmBe,SAAS,oBAAoB,EAC1C,YAAY,EACZ,aAAa,IAAI,EACjB,WAAW,IAAI,EACU;;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,cAAc,CAAC,UAAU;gBAC3B,mBAAmB,OAAO,GAAG;qDAAW;wBACtC;6DAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;;oBACtE;oDAAG;YACL;YAEA;iDAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;gBACF;;QACF;wCAAG;QAAC;QAAc;QAAY;QAAU;QAAU,aAAa,MAAM;KAAC;IAEtE,uBAAuB;IACvB,MAAM,WAAW;QACf,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QACA,gBAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;QACpE,YAAY;QAEZ,oDAAoD;QACpD,WAAW;YACT,YAAY;QACd,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QACA,gBAAgB,CAAC,YAAc,CAAC,YAAY,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;QAC1F,YAAY;QAEZ,oDAAoD;QACpD,WAAW;YACT,YAAY;QACd,GAAG;IACL;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QACA,gBAAgB;QAChB,YAAY;QAEZ,oDAAoD;QACpD,WAAW;YACT,YAAY;QACd,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CAAyG;;;;;;8CAKxH,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAE,WAAU;;gDAAoE;gDACxE,YAAY,CAAC,aAAa,CAAC,KAAK;gDAAC;;;;;;;wCAIzC,YAAY,CAAC,aAAa,CAAC,MAAM,kBAChC,6LAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI,CAAC;gDAAE,QAAQ;4CAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,qMAAA,CAAA,OAAI;oDAEH,MAAM;oDACN,WAAW,GACT,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,IACvC,sDACA,0CACJ;mDANG;;;;;;;;;;sDAab,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;8DAElC,6LAAC;oDAAE,WAAU;8DACV,YAAY,CAAC,aAAa,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9C,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK5B,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;wBAEC,WAAW,CAAC,6CAA6C,EACvD,UAAU,eACN,+BACA,0CACJ;wBACF,SAAS,IAAM,UAAU;wBACzB,cAAY,CAAC,kBAAkB,EAAE,QAAQ,GAAG;uBAPvC;;;;;;;;;;;;;;;;AAajB;GArJwB;KAAA", "debugId": null}}, {"offset": {"line": 2482, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/TestimonialsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport SectionBackground from \"../SectionBackground\";\r\nimport TestimonialCard from \"./TestimonialCard\";\r\nimport TestimonialCarousel from \"./TestimonialCarousel\";\r\n\r\n// Enhanced testimonial data with ratings\r\nconst testimonials = [\r\n  {\r\n    name: \"<PERSON><PERSON>\",\r\n    business: \"Kumar Electronics\",\r\n    quote:\r\n      \"Dukancard has transformed my business. My customers can now easily find my store and contact me directly. The digital card looks professional and represents my brand perfectly.\",\r\n    rating: 5\r\n  },\r\n  {\r\n    name: \"<PERSON><PERSON>\",\r\n    business: \"Sharma Boutique\",\r\n    quote:\r\n      \"I've seen a 40% increase in customer engagement since using Dukancard. The analytics feature helps me understand what my customers are interested in.\",\r\n    rating: 5\r\n  },\r\n  {\r\n    name: \"<PERSON><PERSON>\",\r\n    business: \"Patel Grocery\",\r\n    quote:\r\n      \"Setting up my digital card was incredibly easy. Now I can showcase my products and services online without the hassle of building a website.\",\r\n    rating: 4\r\n  },\r\n  {\r\n    name: \"<PERSON><PERSON>\",\r\n    business: \"Verma Jewellers\",\r\n    quote:\r\n      \"The subscription feature has helped me build a loyal customer base. My customers get updates about new products and promotions directly.\",\r\n    rating: 5\r\n  },\r\n  {\r\n    name: \"<PERSON><PERSON><PERSON>\",\r\n    business: \"Singh Auto Parts\",\r\n    quote:\r\n      \"The QR code feature has been a game-changer for my business. Customers can scan it from my physical store and instantly access my full product catalog online.\",\r\n    rating: 5\r\n  },\r\n  {\r\n    name: \"Meera <PERSON>i\",\r\n    business: \"<PERSON>i Tailoring\",\r\n    quote:\r\n      \"I love how easy it is to update my business information and showcase my latest designs. My customers appreciate being able to browse my work before visiting my shop.\",\r\n    rating: 4\r\n  },\r\n];\r\n\r\nexport default function TestimonialsSection() {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Detect mobile devices\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    checkMobile();\r\n    window.addEventListener('resize', checkMobile);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', checkMobile);\r\n    };\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <section className=\"py-10 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative\">\r\n      {/* Simple background */}\r\n      <div className=\"absolute inset-0 -z-10\">\r\n        <SectionBackground variant=\"purple\" intensity=\"low\" />\r\n      </div>\r\n\r\n      <div className=\"text-center mb-12\">\r\n        <div className=\"relative inline-block\">\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-foreground mb-4\">\r\n            Trusted by{\" \"}\r\n            <span className=\"text-[var(--brand-gold)] relative\">\r\n              Businesses\r\n            </span>{\" \"}\r\n            Everywhere\r\n          </h2>\r\n        </div>\r\n        <p className=\"text-lg text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto\">\r\n          See what our customers are saying about their Dukancard experience.\r\n        </p>\r\n      </div>\r\n\r\n      {/* Responsive testimonial display */}\r\n      {isMobile ? (\r\n        // Mobile: Auto-scrolling carousel\r\n        <div className=\"w-full max-w-md mx-auto\">\r\n          <TestimonialCarousel testimonials={testimonials} />\r\n        </div>\r\n      ) : (\r\n        // Desktop: Grid layout with cards\r\n        <div className=\"relative\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8\">\r\n            {testimonials.slice(0, 4).map((testimonial, index) => (\r\n              <TestimonialCard\r\n                key={index}\r\n                name={testimonial.name}\r\n                business={testimonial.business}\r\n                quote={testimonial.quote}\r\n                _index={index}\r\n                rating={testimonial.rating}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,yCAAyC;AACzC,MAAM,eAAe;IACnB;QACE,MAAM;QACN,UAAU;QACV,OACE;QACF,QAAQ;IACV;IACA;QACE,MAAM;QACN,UAAU;QACV,OACE;QACF,QAAQ;IACV;IACA;QACE,MAAM;QACN,UAAU;QACV,OACE;QACF,QAAQ;IACV;IACA;QACE,MAAM;QACN,UAAU;QACV,OACE;QACF,QAAQ;IACV;IACA;QACE,MAAM;QACN,UAAU;QACV,OACE;QACF,QAAQ;IACV;IACA;QACE,MAAM;QACN,UAAU;QACV,OACE;QACF,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;6DAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;iDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;wCAAG,EAAE;IAIL,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sJAAA,CAAA,UAAiB;oBAAC,SAAQ;oBAAS,WAAU;;;;;;;;;;;0BAGhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsD;gCACvD;8CACX,6LAAC;oCAAK,WAAU;8CAAoC;;;;;;gCAE5C;gCAAI;;;;;;;;;;;;kCAIhB,6LAAC;wBAAE,WAAU;kCAAmE;;;;;;;;;;;;YAMjF,WACC,kCAAkC;0BAClC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mKAAA,CAAA,UAAmB;oBAAC,cAAc;;;;;;;;;;uBAGrC,kCAAkC;0BAClC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,aAAa,sBAC1C,6LAAC,+JAAA,CAAA,UAAe;4BAEd,MAAM,YAAY,IAAI;4BACtB,UAAU,YAAY,QAAQ;4BAC9B,OAAO,YAAY,KAAK;4BACxB,QAAQ;4BACR,QAAQ,YAAY,MAAM;2BALrB;;;;;;;;;;;;;;;;;;;;;AAarB;GAlEwB;KAAA", "debugId": null}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/CTASection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight, Store } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport SectionBackground from \"../SectionBackground\";\r\n\r\nexport default function CTASection() {\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <section className=\"py-10 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative\">\r\n      {/* Simple background */}\r\n      <div className=\"absolute inset-0 -z-10\">\r\n        <SectionBackground variant=\"gradient\" intensity=\"medium\" />\r\n      </div>\r\n\r\n      <div className=\"bg-white/90 dark:bg-black/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 rounded-2xl p-8 md:p-12 relative overflow-hidden shadow-xl\">\r\n        {/* Static background gradients */}\r\n        <div className=\"absolute -right-20 -top-20 w-64 h-64 bg-gradient-to-br from-[var(--brand-gold)]/20 to-amber-500/10 dark:from-[var(--brand-gold)]/30 dark:to-amber-500/20 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute -left-20 -bottom-20 w-64 h-64 bg-gradient-to-br from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 rounded-full blur-3xl\"></div>\r\n\r\n        <div className=\"relative z-10 max-w-3xl mx-auto text-center\">\r\n          <h2 className=\"text-3xl md:text-5xl font-bold text-foreground mb-6 relative\">\r\n            Ready to{\" \"}\r\n            <span className=\"text-[var(--brand-gold)] relative inline-block\">\r\n              Elevate\r\n              {/* Static underline */}\r\n              <div className=\"absolute -bottom-1 left-0 h-1 w-full bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full\"></div>\r\n            </span>{\" \"}\r\n            Your Digital Presence?\r\n          </h2>\r\n\r\n          {/* Paragraph */}\r\n          <div className=\"relative\">\r\n            <p className=\"text-base sm:text-lg text-neutral-600 dark:text-neutral-300 mb-8 sm:mb-10 relative z-10 max-w-2xl mx-auto\">\r\n              Join thousands of businesses that have transformed their digital\r\n              identity with Dukancard. Get started today and receive a 30-day\r\n              premium trial.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mb-6 sm:mb-8\">\r\n            <div className=\"w-full sm:w-auto relative group\">\r\n              {/* Button glow effect */}\r\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/60 to-[var(--brand-gold)]/80 rounded-full opacity-70 blur-md group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n\r\n              <Button\r\n                size=\"lg\"\r\n                className=\"cursor-pointer bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] hover:from-[var(--brand-gold-light)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] px-6 sm:px-8 py-6 rounded-full font-semibold flex gap-2 items-center shadow-md hover:shadow-lg transition-all duration-300 w-full sm:w-auto relative z-10\"\r\n                onClick={() => router.push(\"/login\")}\r\n              >\r\n                <span>Create Your Card</span>\r\n                <ArrowRight className=\"w-5 h-5\" />\r\n              </Button>\r\n            </div>\r\n\r\n            <div className=\"w-full sm:w-auto relative group\">\r\n              {/* Button subtle glow effect */}\r\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full opacity-0 blur-md group-hover:opacity-70 transition-opacity duration-300\"></div>\r\n\r\n              <Button\r\n                size=\"lg\"\r\n                variant=\"outline\"\r\n                className=\"cursor-pointer border-2 border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 px-6 sm:px-8 py-6 rounded-full font-semibold flex gap-2 items-center w-full sm:w-auto backdrop-blur-sm relative z-10\"\r\n                onClick={() => router.push(\"/discover\")}\r\n              >\r\n                <span>Discover Stores</span>\r\n                <Store className=\"w-5 h-5\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-neutral-500 dark:text-neutral-400 text-sm relative inline-block\">\r\n            <div className=\"relative\">\r\n              <span>Start building your digital presence in minutes.</span>\r\n              {/* Static underline */}\r\n              <div className=\"absolute bottom-0 left-0 right-0 h-px w-full bg-gradient-to-r from-transparent via-[var(--brand-gold)]/30 to-transparent\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sJAAA,CAAA,UAAiB;oBAAC,SAAQ;oBAAW,WAAU;;;;;;;;;;;0BAGlD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAA+D;oCAClE;kDACT,6LAAC;wCAAK,WAAU;;4CAAiD;0DAG/D,6LAAC;gDAAI,WAAU;;;;;;;;;;;;oCACT;oCAAI;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAA4G;;;;;;;;;;;0CAQ3H,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;;;;;0DAEf,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAE3B,6LAAC;kEAAK;;;;;;kEACN,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAI1B,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;;;;;0DAEf,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAE3B,6LAAC;kEAAK;;;;;;kEACN,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAKvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDAEN,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;GA9EwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 2939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/productActions.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\n\n\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\nimport { NearbyProduct, getSortingColumn, getSortingDirection } from \"./types\";\nimport { searchDiscoverCombined } from \"./combinedActions\";\n\n// Define a type for the product result from Supabase\n// Unused type kept for reference\n/* type ProductResult = {\n  id: string;\n  business_id: string | null;\n  name: string | null;\n  description: string | null;\n  base_price: number | null;\n  discounted_price: number | null;\n  product_type: \"physical\" | \"service\" | null;\n  is_available: boolean | null;\n  image_url: string | null;\n  created_at: string | null;\n  updated_at: string | null;\n  business_profiles: {\n    business_slug: string | null;\n  } | null;\n}; */\n\n// Helper function to convert any product result to NearbyProduct\nfunction convertToNearbyProduct(\n  product: Record<string, unknown>\n): NearbyProduct {\n  // Extract business_slug from the joined business_profiles\n  let business_slug = null;\n  if (product.business_profiles) {\n    // Handle both object and array formats\n    if (Array.isArray(product.business_profiles)) {\n      business_slug = product.business_profiles[0]?.business_slug || null;\n    } else if (\n      product.business_profiles &&\n      typeof product.business_profiles === \"object\"\n    ) {\n      business_slug =\n        ((product.business_profiles as Record<string, unknown>)\n          .business_slug as string) || null;\n    }\n  }\n\n  return {\n    id: product.id as string,\n    business_id: product.business_id as string | undefined,\n    name: (product.name as string) || \"\",\n    description: (product.description as string) || \"\",\n    base_price: Number(product.base_price) || 0,\n    discounted_price: product.discounted_price\n      ? Number(product.discounted_price)\n      : undefined,\n    product_type:\n      (product.product_type as \"physical\" | \"service\") || \"physical\",\n    is_available: Boolean(product.is_available) || false,\n    image_url: product.image_url as string | undefined,\n    created_at: product.created_at as string | undefined,\n    updated_at: product.updated_at as string | undefined,\n    slug: product.slug as string | undefined,\n    business_slug: business_slug,\n    featured_image_index: 0, // Default value for NearbyProduct\n    images: [], // Default empty array for images\n  };\n}\n\n// Function to fetch more products with combined search for infinite scroll\nexport async function fetchMoreProductsCombined(params: {\n  businessName?: string | null;\n  productName?: string | null;\n  pincode?: string | null;\n  locality?: string | null;\n  page: number;\n  limit?: number;\n  sortBy?: BusinessSortBy;\n  productSort?: string;\n  productType?: \"physical\" | \"service\" | null;\n}): Promise<{\n  data?: {\n    products: NearbyProduct[];\n    totalCount: number;\n    hasMore: boolean;\n    nextPage: number | null;\n  };\n  error?: string;\n}> {\n  // Reuse the searchDiscoverCombined function with viewType set to \"products\"\n  const result = await searchDiscoverCombined({\n    ...params,\n    viewType: \"products\",\n  });\n\n  if (result.error) {\n    return { error: result.error };\n  }\n\n  if (!result.data?.products) {\n    return { error: \"No product data found\" };\n  }\n\n  return {\n    data: {\n      products: result.data.products,\n      totalCount: result.data.totalCount,\n      hasMore: result.data.hasMore,\n      nextPage: result.data.nextPage,\n    },\n  };\n}\n\n// Function to fetch all products\nexport async function fetchAllProducts(params: {\n  page?: number;\n  limit?: number;\n  sortBy?: BusinessSortBy;\n  productType?: \"physical\" | \"service\" | null;\n  pincode?: string | null;\n  locality?: string | null;\n  productName?: string | null;\n  category?: string | null;\n}): Promise<{\n  data?: {\n    products: NearbyProduct[];\n    isAuthenticated: boolean;\n    totalCount: number;\n    hasMore: boolean;\n    nextPage: number | null;\n  };\n  error?: string;\n}> {\n  const {\n    page = 1,\n    limit = 20,\n    sortBy = \"created_desc\",\n    productType = null,\n    pincode = null,\n    locality = null,\n    productName = null,\n    category = null,\n  } = params;\n\n  // Check Authentication\n  const supabase = await createClient();\n  const {\n    data: { user },\n  } = await supabase.auth.getUser();\n  const isAuthenticated = !!user;\n\n  try {\n    const supabase = await createClient();\n    const offset = (page - 1) * limit;\n\n    // Get all online business IDs without checking subscription status\n    let businessQuery = supabase\n      .from(\"business_profiles\")\n      .select(\"id\")\n      .eq(\"status\", \"online\");\n\n    // Add pincode filter if provided\n    if (pincode) {\n      businessQuery = businessQuery.eq(\"pincode\", pincode);\n    }\n\n    // Add locality filter if provided\n    if (locality) {\n      businessQuery = businessQuery.eq(\"locality\", locality);\n    }\n\n    // Add category filter if provided\n    if (category && category.trim()) {\n      businessQuery = businessQuery.eq(\"business_category\", category.trim());\n    }\n\n    const { data: validBusinesses, error: businessError } = await businessQuery;\n\n    if (businessError) {\n      console.error(\"Error fetching valid businesses:\", businessError);\n      return { error: \"Failed to fetch valid businesses\" };\n    }\n\n    if (!validBusinesses || validBusinesses.length === 0) {\n      return {\n        data: {\n          products: [],\n          isAuthenticated,\n          totalCount: 0,\n          hasMore: false,\n          nextPage: null,\n        },\n      };\n    }\n\n    const validBusinessIds = validBusinesses.map((b: { id: string }) => b.id);\n\n    // Build the query for counting products - count products from valid businesses\n    let countQuery = supabase\n      .from(\"products_services\")\n      .select(\"id\", { count: \"exact\" })\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      countQuery = countQuery.eq(\"product_type\", productType);\n    }\n\n    // Add product name filter if provided\n    if (productName && productName.trim().length > 0) {\n      countQuery = countQuery.ilike(\"name\", `%${productName.trim()}%`);\n    }\n\n    // Get total count\n    const { count, error: countError } = await countQuery;\n\n    if (countError) {\n      console.error(\"Error counting products:\", countError);\n      return { error: \"Failed to count products\" };\n    }\n\n    // Build the query for fetching products from valid businesses\n    let productsQuery = supabase\n      .from(\"products_services\")\n      .select(\n        `\n        id, business_id, name, description, base_price, discounted_price, product_type,\n        is_available, image_url, created_at, updated_at, slug,\n        business_profiles!business_id(business_slug)\n      `\n      )\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      productsQuery = productsQuery.eq(\"product_type\", productType);\n    }\n\n    // Add product name filter if provided\n    if (productName && productName.trim().length > 0) {\n      productsQuery = productsQuery.ilike(\"name\", `%${productName.trim()}%`);\n    }\n\n    // Add sorting\n    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view\n    const sortAscending = getSortingDirection(sortBy);\n\n    // Special handling for price sorting to use discounted_price when available, otherwise base_price\n    if (sortColumn === \"price\") {\n      if (sortAscending) {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: true, nullsFirst: false })\n          .order(\"base_price\", { ascending: true, nullsFirst: false });\n      } else {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: false, nullsFirst: false })\n          .order(\"base_price\", { ascending: false, nullsFirst: false });\n      }\n    } else {\n      productsQuery = productsQuery.order(sortColumn, {\n        ascending: sortAscending,\n      });\n    }\n\n    // Add pagination\n    productsQuery = productsQuery.range(offset, offset + limit - 1);\n\n    // Execute the query\n    const { data: productsData, error: productsError } = await productsQuery;\n\n    if (productsError) {\n      console.error(\"Error fetching products:\", productsError);\n      return { error: \"Failed to fetch products\" };\n    }\n\n    // Process the products data to include business_slug\n    const products = productsData.map(convertToNearbyProduct);\n\n    // Calculate pagination info\n    const totalCount = count || 0;\n    const hasMore = totalCount > offset + products.length;\n    const nextPage = hasMore ? page + 1 : null;\n\n    return {\n      data: {\n        products,\n        isAuthenticated,\n        totalCount,\n        hasMore,\n        nextPage,\n      },\n    };\n  } catch (error) {\n    console.error(\"Unexpected error in fetchAllProducts:\", error);\n    return { error: \"An unexpected error occurred\" };\n  }\n}\n\n// Function to fetch products by business IDs\nexport async function fetchProductsByBusinessIds(params: {\n  businessIds: string[];\n  page?: number;\n  limit?: number;\n  sortBy?: BusinessSortBy;\n  productType?: \"physical\" | \"service\" | null;\n}): Promise<{\n  data?: {\n    products: NearbyProduct[];\n    isAuthenticated: boolean;\n    totalCount: number;\n    hasMore: boolean;\n    nextPage: number | null;\n  };\n  error?: string;\n}> {\n  const {\n    businessIds,\n    page = 1,\n    limit = 20,\n    sortBy = \"created_desc\",\n    productType = null,\n  } = params;\n\n  if (!businessIds || businessIds.length === 0) {\n    return {\n      error: \"No business IDs provided\",\n    };\n  }\n\n  // Check Authentication\n  const supabase = await createClient();\n  const {\n    data: { user },\n  } = await supabase.auth.getUser();\n  const isAuthenticated = !!user;\n\n  try {\n    const offset = (page - 1) * limit;\n    \n\n    // Filter the business IDs to only include online ones\n    const { data: validBusinesses, error: businessError } = await supabase\n      .from(\"business_profiles\")\n      .select(\"id\")\n      .in(\"id\", businessIds)\n      .eq(\"status\", \"online\");\n\n    if (businessError) {\n      console.error(\"Error filtering valid businesses:\", businessError);\n      return { error: \"Failed to filter valid businesses\" };\n    }\n\n    // If no valid businesses found, return empty result\n    if (!validBusinesses || validBusinesses.length === 0) {\n      return {\n        data: {\n          products: [],\n          isAuthenticated,\n          totalCount: 0,\n          hasMore: false,\n          nextPage: null,\n        },\n      };\n    }\n\n    // Get the IDs of valid businesses\n    const validBusinessIds = validBusinesses.map((b: { id: string; }) => b.id);\n\n    // Build the query for counting products\n    let countQuery = supabase\n      .from(\"products_services\")\n      .select(\"id\", { count: \"exact\" })\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      countQuery = countQuery.eq(\"product_type\", productType);\n    }\n\n    // Get total count\n    const { count, error: countError } = await countQuery;\n\n    if (countError) {\n      console.error(\"Error counting products:\", countError);\n      return { error: \"Failed to count products\" };\n    }\n\n    // Build the query for fetching products\n    let productsQuery = supabase\n      .from(\"products_services\")\n      .select(\n        `\n        id, business_id, name, description, base_price, discounted_price, product_type,\n        is_available, image_url, created_at, updated_at, slug,\n        business_profiles!business_id(business_slug)\n      `\n      )\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      productsQuery = productsQuery.eq(\"product_type\", productType);\n    }\n\n    // Add sorting\n    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view\n    const sortAscending = getSortingDirection(sortBy);\n\n    // Special handling for price sorting to use discounted_price when available, otherwise base_price\n    if (sortColumn === \"price\") {\n      if (sortAscending) {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: true, nullsFirst: false })\n          .order(\"base_price\", { ascending: true, nullsFirst: false });\n      } else {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: false, nullsFirst: false })\n          .order(\"base_price\", { ascending: false, nullsFirst: false });\n      }\n    } else {\n      productsQuery = productsQuery.order(sortColumn, {\n        ascending: sortAscending,\n      });\n    }\n\n    // Add pagination\n    productsQuery = productsQuery.range(offset, offset + limit - 1);\n\n    // Execute the query\n    const { data: productsData, error: productsError } = await productsQuery;\n\n    if (productsError) {\n      console.error(\"Error fetching products:\", productsError);\n      return { error: \"Failed to fetch products\" };\n    }\n\n    // Process the products data to include business_slug\n    const products = productsData.map(convertToNearbyProduct);\n\n    // Calculate pagination info\n    const totalCount = count || 0;\n    const hasMore = totalCount > offset + products.length;\n    const nextPage = hasMore ? page + 1 : null;\n\n    return {\n      data: {\n        products,\n        isAuthenticated,\n        totalCount,\n        hasMore,\n        nextPage,\n      },\n    };\n  } catch (error) {\n    console.error(\"Unexpected error in fetchProductsByBusinessIds:\", error);\n    return { error: \"An unexpected error occurred\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAmHsB,mBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2955, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ProductListItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NearbyProduct } from \"@/app/(main)/discover/actions/types\";\r\n\r\ninterface ProductListItemProps {\r\n  product: ProductServiceData | NearbyProduct;\r\n  isLink?: boolean;\r\n}\r\n\r\n// Helper to format currency\r\nconst formatCurrency = (amount: number | null | undefined) => {\r\n  if (amount === null || amount === undefined) return null;\r\n  return amount.toLocaleString(\"en-IN\", {\r\n    style: \"currency\",\r\n    currency: \"INR\",\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 2,\r\n  });\r\n};\r\n\r\n// Animation variants\r\nconst cardContainerVariants = {\r\n  hidden: { opacity: 0, y: 10 },\r\n  show: { opacity: 1, y: 0 },\r\n};\r\n\r\nconst discountBadgeVariants = {\r\n  initial: { scale: 0.9, opacity: 0 },\r\n  animate: {\r\n    scale: 1,\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.5,\r\n      type: \"spring\",\r\n      stiffness: 400,\r\n      damping: 10,\r\n    },\r\n  },\r\n  hover: {\r\n    scale: 1.05,\r\n    rotate: -2,\r\n    transition: { type: \"spring\", stiffness: 500 },\r\n  },\r\n};\r\n\r\nexport default function ProductListItem({\r\n  product,\r\n  isLink = true,\r\n}: ProductListItemProps) {\r\n  // If isLink is true, we'll wrap the content in a link\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const formattedBasePrice = formatCurrency(product.base_price);\r\n  const formattedDiscountedPrice = formatCurrency(product.discounted_price); // Format discounted price\r\n\r\n  // Determine final price and if there's a discount shown\r\n  let finalPrice = formattedBasePrice;\r\n  let priceToShowStrikethrough: string | null = null;\r\n  let discountPercentage = 0;\r\n\r\n  const hasDiscountedPrice =\r\n    typeof product.discounted_price === \"number\" &&\r\n    product.discounted_price > 0;\r\n  const hasBasePrice =\r\n    typeof product.base_price === \"number\" && product.base_price > 0;\r\n\r\n  if (\r\n    hasDiscountedPrice &&\r\n    hasBasePrice &&\r\n    product.discounted_price! < product.base_price!\r\n  ) {\r\n    // Scenario 1: Discounted price is valid and less than base price\r\n    finalPrice = formattedDiscountedPrice;\r\n    priceToShowStrikethrough = formattedBasePrice; // Strike through base price\r\n    discountPercentage = Math.round(\r\n      ((product.base_price! - product.discounted_price!) /\r\n        product.base_price!) *\r\n        100\r\n    );\r\n  } else {\r\n    // Scenario 2: No discount applicable, show base price\r\n    finalPrice = formattedBasePrice;\r\n    priceToShowStrikethrough = null;\r\n    discountPercentage = 0;\r\n  }\r\n\r\n  // Ensure finalPrice has a fallback if both prices are null/undefined\r\n  if (!finalPrice) {\r\n    finalPrice = \"Price unavailable\";\r\n  }\r\n\r\n  const showDiscountBadge = discountPercentage > 0;\r\n\r\n  // State for lazy loading images\r\n  const [isImageLoaded, setIsImageLoaded] = useState(false);\r\n\r\n  // Check if product is out of stock\r\n  const isOutOfStock = !product.is_available;\r\n\r\n  // Ensure we're not using business_id as a key\r\n  // Use the product's own ID for any keys needed\r\n  const content = (\r\n    <motion.div\r\n      variants={cardContainerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"show\"\r\n      className=\"w-full overflow-hidden\"\r\n    >\r\n      <div className=\"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg\">\r\n        <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n          {/* Image container */}\r\n          <div className=\"relative w-full overflow-hidden rounded-t-xl\">\r\n            {/* Get the featured image from the images array if available, otherwise fall back to image_url */}\r\n            {/* Determine the image URL to use */}\r\n            {(() => {\r\n              // Get the image URL to display\r\n              let imageUrl = product.image_url;\r\n\r\n              // If product has images array and it's not empty, use the featured image\r\n              if (product.images && Array.isArray(product.images) && product.images.length > 0) {\r\n                const featuredIndex = typeof product.featured_image_index === 'number'\r\n                  ? Math.min(product.featured_image_index, product.images.length - 1)\r\n                  : 0;\r\n                imageUrl = product.images[featuredIndex];\r\n              }\r\n\r\n              if (imageUrl && !imageError) {\r\n                return (\r\n                  <div className=\"overflow-hidden\">\r\n                    {!isImageLoaded && (\r\n                      <Skeleton className=\"absolute inset-0 rounded-t-xl\" />\r\n                    )}\r\n                    <motion.div className=\"w-full\">\r\n                      <Image\r\n                        src={imageUrl}\r\n                        alt={product.name ?? \"Product image\"}\r\n                        width={500}\r\n                        height={750}\r\n                        className={`w-full aspect-square object-cover ${\r\n                          isOutOfStock\r\n                            ? \"filter grayscale opacity-70 transition-all duration-500\"\r\n                            : \"\"\r\n                        } ${\r\n                          isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                        } max-w-full`}\r\n                        loading=\"lazy\"\r\n                        onError={() => setImageError(true)}\r\n                        onLoad={() => setIsImageLoaded(true)}\r\n                        quality={80}\r\n                        blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=\"\r\n                        placeholder=\"blur\"\r\n                        style={{ objectFit: \"cover\" }}\r\n                      />\r\n                    </motion.div>\r\n                  </div>\r\n                );\r\n              } else {\r\n                return (\r\n                  <div className=\"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl\">\r\n                    <svg\r\n                      className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={1}\r\n                        d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                );\r\n              }\r\n            })()}\r\n\r\n            {/* Out of Stock Overlay */}\r\n            {isOutOfStock && (\r\n              <div className=\"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40\">\r\n                <div className=\"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground\">\r\n                  <span className=\"font-medium tracking-wide uppercase text-xs sm:text-sm\">\r\n                    Out of Stock\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Discount Badge Overlay */}\r\n            {showDiscountBadge && (\r\n              <AnimatePresence>\r\n                <motion.div\r\n                  key={`discount-badge-${product.id}`}\r\n                  variants={discountBadgeVariants}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  whileHover=\"hover\"\r\n                  className={cn(\r\n                    \"absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg\",\r\n                    \"bg-destructive\",\r\n                    \"text-destructive-foreground border border-destructive-foreground/20\",\r\n                    \"transform-gpu\"\r\n                  )}\r\n                >\r\n                  <div className=\"flex flex-col items-center justify-center\">\r\n                    <span className=\"text-[7px] sm:text-[9px] md:text-[10px] font-medium\">\r\n                      SAVE\r\n                    </span>\r\n                    <span className=\"text-[9px] sm:text-xs md:text-sm leading-none\">\r\n                      {discountPercentage}%\r\n                    </span>\r\n                  </div>\r\n                </motion.div>\r\n              </AnimatePresence>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1\">\r\n            {/* Title */}\r\n            <p className=\"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden\">\r\n              {product.name ?? \"Unnamed Product\"}\r\n            </p>\r\n\r\n            {/* Description (optional) */}\r\n            {product.description && (\r\n              <p className=\"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate\">\r\n                {product.description}\r\n              </p>\r\n            )}\r\n\r\n            {/* Price and Badge Container */}\r\n            <div className=\"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1\">\r\n              {/* Price Group */}\r\n              <div className=\"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full\">\r\n                {finalPrice && (\r\n                  <p className=\"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full\">\r\n                    {finalPrice}\r\n                  </p>\r\n                )}\r\n                {priceToShowStrikethrough && (\r\n                  <p className=\"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500\">\r\n                    {priceToShowStrikethrough}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Product Type Badge removed as per instructions */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n\r\n  // If isLink is true, wrap the content in a link to the product detail page\r\n  if (isLink && \"business_slug\" in product && product.business_slug) {\r\n    return (\r\n      <Link\r\n        href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n        className=\"block h-full\"\r\n      >\r\n        {content}\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  // Otherwise, just return the content\r\n  return content;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;;;AARA;;;;;;;AAgBA,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO;IACpD,OAAO,OAAO,cAAc,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAEA,qBAAqB;AACrB,MAAM,wBAAwB;IAC5B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAE;AAC3B;AAEA,MAAM,wBAAwB;IAC5B,SAAS;QAAE,OAAO;QAAK,SAAS;IAAE;IAClC,SAAS;QACP,OAAO;QACP,SAAS;QACT,YAAY;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ,CAAC;QACT,YAAY;YAAE,MAAM;YAAU,WAAW;QAAI;IAC/C;AACF;AAEe,SAAS,gBAAgB,EACtC,OAAO,EACP,SAAS,IAAI,EACQ;;IACrB,sDAAsD;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,eAAe,QAAQ,UAAU;IAC5D,MAAM,2BAA2B,eAAe,QAAQ,gBAAgB,GAAG,0BAA0B;IAErG,wDAAwD;IACxD,IAAI,aAAa;IACjB,IAAI,2BAA0C;IAC9C,IAAI,qBAAqB;IAEzB,MAAM,qBACJ,OAAO,QAAQ,gBAAgB,KAAK,YACpC,QAAQ,gBAAgB,GAAG;IAC7B,MAAM,eACJ,OAAO,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;IAEjE,IACE,sBACA,gBACA,QAAQ,gBAAgB,GAAI,QAAQ,UAAU,EAC9C;QACA,iEAAiE;QACjE,aAAa;QACb,2BAA2B,oBAAoB,4BAA4B;QAC3E,qBAAqB,KAAK,KAAK,CAC7B,AAAC,CAAC,QAAQ,UAAU,GAAI,QAAQ,gBAAgB,AAAC,IAC/C,QAAQ,UAAU,GAClB;IAEN,OAAO;QACL,sDAAsD;QACtD,aAAa;QACb,2BAA2B;QAC3B,qBAAqB;IACvB;IAEA,qEAAqE;IACrE,IAAI,CAAC,YAAY;QACf,aAAa;IACf;IAEA,MAAM,oBAAoB,qBAAqB;IAE/C,gCAAgC;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,MAAM,eAAe,CAAC,QAAQ,YAAY;IAE1C,8CAA8C;IAC9C,+CAA+C;IAC/C,MAAM,wBACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BAGZ,CAAC;gCACA,+BAA+B;gCAC/B,IAAI,WAAW,QAAQ,SAAS;gCAEhC,yEAAyE;gCACzE,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;oCAChF,MAAM,gBAAgB,OAAO,QAAQ,oBAAoB,KAAK,WAC1D,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,KAC/D;oCACJ,WAAW,QAAQ,MAAM,CAAC,cAAc;gCAC1C;gCAEA,IAAI,YAAY,CAAC,YAAY;oCAC3B,qBACE,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,+BACA,6LAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAEtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,WAAU;0DACpB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,QAAQ,IAAI,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAW,CAAC,kCAAkC,EAC5C,eACI,4DACA,GACL,CAAC,EACA,gBAAgB,gBAAgB,YACjC,WAAW,CAAC;oDACb,SAAQ;oDACR,SAAS,IAAM,cAAc;oDAC7B,QAAQ,IAAM,iBAAiB;oDAC/B,SAAS;oDACT,aAAY;oDACZ,aAAY;oDACZ,OAAO;wDAAE,WAAW;oDAAQ;;;;;;;;;;;;;;;;;gCAKtC,OAAO;oCACL,qBACE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;gCAKZ;4BACF,CAAC;4BAGA,8BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAyD;;;;;;;;;;;;;;;;4BAQ9E,mCACC,6LAAC,4LAAA,CAAA,kBAAe;0CACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qIACA,kBACA,uEACA;8CAGF,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsD;;;;;;0DAGtE,6LAAC;gDAAK,WAAU;;oDACb;oDAAmB;;;;;;;;;;;;;mCAjBnB,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;kCA0B3C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAE,WAAU;0CACV,QAAQ,IAAI,IAAI;;;;;;4BAIlB,QAAQ,WAAW,kBAClB,6LAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAKxB,6LAAC;gCAAI,WAAU;0CAEb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,4BACC,6LAAC;4CAAE,WAAU;sDACV;;;;;;wCAGJ,0CACC,6LAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAanB,2EAA2E;IAC3E,IAAI,UAAU,mBAAmB,WAAW,QAAQ,aAAa,EAAE;QACjE,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;YACvE,WAAU;sBAET;;;;;;IAGP;IAEA,qCAAqC;IACrC,OAAO;AACT;GAjOwB;KAAA", "debugId": null}}, {"offset": {"line": 3324, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ProductGridSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport default function ProductGridSkeleton() {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Product Grid Skeleton */}\r\n      <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4\">\r\n        {Array.from({ length: 6 }).map((_, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"border border-neutral-200/80 dark:border-neutral-800/80 rounded-xl overflow-hidden bg-white dark:bg-neutral-900 transition-all duration-300 w-full\"\r\n          >\r\n            <Skeleton className=\"h-48 w-full\" />\r\n            <div className=\"p-2 sm:p-3 md:p-4 space-y-2 sm:space-y-3\">\r\n              <Skeleton className=\"h-4 sm:h-5 w-3/4\" />\r\n              <Skeleton className=\"h-3 sm:h-4 w-1/2\" />\r\n              <div className=\"flex justify-between items-center pt-1 sm:pt-2\">\r\n                <Skeleton className=\"h-5 sm:h-6 w-16 sm:w-20\" />\r\n                <Skeleton className=\"h-6 sm:h-8 w-6 sm:w-8 rounded-full\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;oBAEC,WAAU;;sCAEV,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;mBATnB;;;;;;;;;;;;;;;AAiBjB;KAxBwB", "debugId": null}}, {"offset": {"line": 3424, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/NewArrivalsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport <PERSON> from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { fetchAllProducts } from \"@/app/(main)/discover/actions/productActions\";\r\nimport { NearbyProduct } from \"@/app/(main)/discover/actions/types\";\r\nimport ProductListItem from \"@/app/components/ProductListItem\";\r\nimport { motion } from \"framer-motion\";\r\nimport ProductGridSkeleton from \"@/app/(main)/discover/components/ProductGridSkeleton\"; // Import ProductGridSkeleton\r\n\r\nexport default function NewArrivalsSection() {\r\n  const [products, setProducts] = useState<NearbyProduct[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const getNewProducts = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        const { data, error } = await fetchAllProducts({\r\n          page: 1,\r\n          limit: 6, // Fetch 6 new arrivals\r\n          sortBy: \"created_desc\",\r\n        });\r\n\r\n        if (error) {\r\n          setError(error);\r\n        } else if (data) {\r\n          setProducts(data.products);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch new arrivals:\", err);\r\n        setError(\"Failed to load new arrivals.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    getNewProducts();\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-8 md:py-12\">\r\n      <div className=\"container px-2 sm:px-4 md:px-6 mx-auto\">\r\n        <div className=\"flex flex-col items-center justify-center space-y-4 text-center\">\r\n          <div className=\"space-y-2\">\r\n            <h2 className=\"text-3xl font-bold tracking-tighter sm:text-5xl\">New Arrivals</h2>\r\n            <p className=\"max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400\">\r\n              Discover the latest products and services added to Dukancard.\r\n            </p>\r\n          </div>\r\n        </div>\r\n        {loading ? (\r\n          <div className=\"mx-auto max-w-7xl py-8\">\r\n            <ProductGridSkeleton />\r\n          </div>\r\n        ) : (\r\n          <div className=\"mx-auto grid max-w-7xl items-start gap-2 sm:gap-3 md:gap-4 py-8 grid-cols-2 md:grid-cols-3 lg:grid-cols-6 justify-items-stretch\">\r\n            {error && <p className=\"text-red-500 col-span-full text-center\">{error}</p>}\r\n            {!error && products.length === 0 && (\r\n              <p className=\"col-span-full text-center text-gray-500\">No new products found.</p>\r\n            )}\r\n            {products.map((product, index) => (\r\n              <motion.div\r\n                key={product.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.4, delay: index * 0.1 }}\r\n                className=\"w-full h-full\"\r\n              >\r\n                <ProductListItem product={product} isLink={true} />\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        )}\r\n        <div className=\"flex justify-center mt-8 w-full\">\r\n          <Link href=\"/discover?tab=products\" passHref>\r\n            <Button size=\"lg\">View All Products</Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA,kSAAwF,6BAA6B;;;AATrH;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;+DAAiB;oBACrB,WAAW;oBACX,SAAS;oBACT,IAAI;wBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,iLAAA,CAAA,mBAAgB,AAAD,EAAE;4BAC7C,MAAM;4BACN,OAAO;4BACP,QAAQ;wBACV;wBAEA,IAAI,OAAO;4BACT,SAAS;wBACX,OAAO,IAAI,MAAM;4BACf,YAAY,KAAK,QAAQ;wBAC3B;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;uCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,6LAAC;gCAAE,WAAU;0CAA4G;;;;;;;;;;;;;;;;;gBAK5H,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oKAAA,CAAA,UAAmB;;;;;;;;;yCAGtB,6LAAC;oBAAI,WAAU;;wBACZ,uBAAS,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;wBAChE,CAAC,SAAS,SAAS,MAAM,KAAK,mBAC7B,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;wBAExD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,WAAU;0CAEV,cAAA,6LAAC,wIAAA,CAAA,UAAe;oCAAC,SAAS;oCAAS,QAAQ;;;;;;+BANtC,QAAQ,EAAE;;;;;;;;;;;8BAWvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAyB,QAAQ;kCAC1C,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GA1EwB;KAAA", "debugId": null}}, {"offset": {"line": 3626, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/businessActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport {\r\n  BusinessSortBy,\r\n  getSecureBusinessProfiles,\r\n} from \"@/lib/actions/businessProfiles\";\r\nimport { searchDiscoverCombined } from \"./combinedActions\";\r\n\r\n// Function to fetch more business cards for infinite scroll\r\nexport async function fetchMoreBusinessCardsCombined(params: {\r\n  businessName?: string | null;\r\n  pincode?: string | null;\r\n  locality?: string | null;\r\n  page: number;\r\n  limit?: number;\r\n  sortBy?: BusinessSortBy;\r\n}): Promise<{\r\n  data?: {\r\n    businesses: BusinessCardData[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    nextPage: number | null;\r\n  };\r\n  error?: string;\r\n}> {\r\n  // Reuse the searchDiscoverCombined function with viewType set to \"cards\"\r\n  const result = await searchDiscoverCombined({\r\n    ...params,\r\n    viewType: \"cards\",\r\n  });\r\n\r\n  if (result.error) {\r\n    return { error: result.error };\r\n  }\r\n\r\n  if (!result.data?.businesses) {\r\n    return { error: \"No business data found\" };\r\n  }\r\n\r\n  return {\r\n    data: {\r\n      businesses: result.data.businesses,\r\n      totalCount: result.data.totalCount,\r\n      hasMore: result.data.hasMore,\r\n      nextPage: result.data.nextPage,\r\n    },\r\n  };\r\n}\r\n\r\n// Function to fetch businesses by search criteria\r\nexport async function fetchBusinessesBySearch(params: {\r\n  businessName?: string | null;\r\n  pincode?: string | null;\r\n  locality?: string | null;\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: BusinessSortBy;\r\n  category?: string | null;\r\n}): Promise<{\r\n  data?: {\r\n    businesses: BusinessCardData[];\r\n    location?: { city: string; state: string } | null;\r\n    isAuthenticated: boolean;\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    nextPage: number | null;\r\n  };\r\n  error?: string;\r\n}> {\r\n  const {\r\n    businessName,\r\n    pincode,\r\n    locality,\r\n    page = 1,\r\n    limit = 20,\r\n    sortBy = \"created_desc\",\r\n    category = null,\r\n  } = params;\r\n\r\n  const supabase = await createClient();\r\n\r\n  // Check Authentication\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  const isAuthenticated = !!user;\r\n\r\n  try {\r\n    // Use the secure method to fetch business profiles\r\n    const {\r\n      data: businessesData,\r\n      count,\r\n      error: businessesError,\r\n    } = await getSecureBusinessProfiles(\r\n      businessName,\r\n      pincode,\r\n      locality,\r\n      page,\r\n      limit,\r\n      sortBy,\r\n      category\r\n    );\r\n\r\n    if (businessesError) {\r\n      console.error(\"Search Businesses By Name Error:\", businessesError);\r\n      return { error: businessesError };\r\n    }\r\n\r\n    const totalCount = count || 0;\r\n    // Calculate if there are more pages\r\n    const hasMore =\r\n      totalCount > (page - 1) * limit + (businessesData?.length || 0);\r\n    const nextPage = hasMore ? page + 1 : null;\r\n\r\n    // Map raw data to BusinessCardData, handling potential nulls\r\n    const businesses: BusinessCardData[] =\r\n      businessesData?.map((data) => {\r\n        // Use the actual data from the database\r\n        return {\r\n          id: data.id,\r\n          business_name: data.business_name ?? \"\",\r\n          contact_email: \"\", // Not included in secure data\r\n          has_active_subscription:\r\n            data.subscription_status === \"active\" ||\r\n            data.subscription_status === \"authenticated\",\r\n          trial_end_date: data.trial_end_date ?? null,\r\n          created_at: data.created_at ?? undefined,\r\n          updated_at: data.updated_at ?? undefined,\r\n          logo_url: data.logo_url ?? \"\",\r\n          member_name: data.member_name ?? \"\",\r\n          title: data.title ?? \"\",\r\n          address_line: data.address_line ?? \"\",\r\n          city: data.city ?? \"\",\r\n          state: data.state ?? \"\",\r\n          pincode: data.pincode ?? \"\",\r\n          locality: data.locality ?? \"\",\r\n          phone: data.phone ?? \"\",\r\n          business_category: data.business_category ?? \"\",\r\n          instagram_url: data.instagram_url ?? \"\",\r\n          facebook_url: data.facebook_url ?? \"\",\r\n          whatsapp_number: data.whatsapp_number ?? \"\",\r\n          about_bio: data.about_bio ?? \"\",\r\n          status: data.status === \"online\" ? \"online\" : \"offline\",\r\n          business_slug: data.business_slug ?? \"\",\r\n\r\n          // Include metrics data\r\n          total_likes: data.total_likes ?? 0,\r\n          total_subscriptions: data.total_subscriptions ?? 0,\r\n          average_rating: data.average_rating ?? 0,\r\n\r\n          // Use actual data if available, otherwise use defaults\r\n          theme_color: data.theme_color ?? \"#D4AF37\",\r\n          delivery_info: data.delivery_info ?? \"\",\r\n          business_hours: data.business_hours,\r\n\r\n          established_year: data.established_year ?? null,\r\n\r\n          // Add default values for fields required by BusinessCardData but not in our query\r\n          website_url: \"\",\r\n          linkedin_url: \"\",\r\n          twitter_url: \"\",\r\n          youtube_url: \"\",\r\n          call_number: \"\", // This field doesn't exist in the database\r\n        };\r\n      }) ?? [];\r\n\r\n    return {\r\n      data: {\r\n        businesses,\r\n        isAuthenticated,\r\n        totalCount,\r\n        hasMore,\r\n        nextPage,\r\n      },\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Search Businesses Exception:\", e);\r\n    return { error: \"An unexpected error occurred during the search.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoDsB,0BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3642, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/AnimatedBusinessCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { Heart, MapPin, Star, UserPlus } from \"lucide-react\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport { useState, useRef } from \"react\";\r\n\r\ninterface AnimatedBusinessCardProps {\r\n  business: BusinessCardData;\r\n  index: number;\r\n}\r\n\r\nexport default function AnimatedBusinessCard({ business, index }: AnimatedBusinessCardProps) {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const cardRef = useRef(null);\r\n  const isInView = useInView(cardRef, { once: false, amount: 0.2 });\r\n\r\n  // Format address components for better display\r\n  const getAddressComponents = () => {\r\n    // First line: address_line\r\n    const addressLine = business.address_line || \"\";\r\n\r\n    // Second line: locality, city\r\n    const localityCity = [business.locality, business.city]\r\n      .filter(Boolean)\r\n      .join(\", \");\r\n\r\n    // Third line: state, pincode\r\n    const statePin = [\r\n      business.state,\r\n      business.pincode ? `PIN: ${business.pincode}` : null\r\n    ]\r\n      .filter(Boolean)\r\n      .join(\", \");\r\n\r\n    return {\r\n      addressLine,\r\n      localityCity,\r\n      statePin,\r\n      hasAddress: !!(addressLine || localityCity || statePin)\r\n    };\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      ref={cardRef}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={isInView ? { opacity: 1, y: 0 } : {}}\r\n      transition={{ duration: 0.4, delay: index * 0.05 }}\r\n      className=\"h-full\"\r\n      onHoverStart={() => setIsHovered(true)}\r\n      onHoverEnd={() => setIsHovered(false)}\r\n    >\r\n      <Link href={`/${business.business_slug}`} className=\"block h-full\">\r\n        <motion.div\r\n          className=\"relative h-full overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm transition-all duration-300\"\r\n          animate={{\r\n            boxShadow: isHovered\r\n              ? \"0 10px 25px -5px rgba(var(--brand-gold-rgb), 0.2), 0 8px 10px -6px rgba(var(--brand-gold-rgb), 0.1)\"\r\n              : \"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)\",\r\n            borderColor: isHovered\r\n              ? \"rgba(var(--brand-gold-rgb), 0.3)\"\r\n              : \"\"\r\n          }}\r\n        >\r\n          {/* Card content */}\r\n          <div className=\"p-3 sm:p-4 flex flex-col h-full\">\r\n            {/* Header with logo and name - centered design */}\r\n            <div className=\"flex flex-col items-center text-center mb-4\">\r\n              <div className=\"relative flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-full overflow-hidden border-2 border-[var(--brand-gold)]/30 mb-2\">\r\n                {business.logo_url && business.logo_url.trim() !== \"\" ? (\r\n                  <Image\r\n                    src={business.logo_url}\r\n                    alt={business.business_name || \"Business logo\"}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                    sizes=\"(max-width: 640px) 48px, 64px\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-full h-full flex items-center justify-center bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] text-xl font-semibold\">\r\n                    {business.business_name?.charAt(0) || \"B\"}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Animated ring on hover */}\r\n                {isHovered && (\r\n                  <motion.div\r\n                    className=\"absolute inset-0 rounded-full border-2 border-[var(--brand-gold)]\"\r\n                    initial={{ opacity: 0, scale: 0.8 }}\r\n                    animate={{ opacity: 1, scale: 1 }}\r\n                    transition={{ duration: 0.3 }}\r\n                  />\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"min-w-0 w-full px-2\">\r\n                <motion.h3\r\n                  className=\"font-semibold text-neutral-900 dark:text-white line-clamp-1 text-sm sm:text-base md:text-lg relative inline-block mx-auto\"\r\n                  animate={{ color: isHovered ? \"var(--brand-gold)\" : \"\" }}\r\n                  transition={{ duration: 0.2 }}\r\n                >\r\n                  {business.business_name}\r\n\r\n                  {/* Animated underline on hover */}\r\n                  {isHovered && (\r\n                    <motion.div\r\n                      className=\"absolute -bottom-1 left-0 h-0.5 bg-[var(--brand-gold)]\"\r\n                      initial={{ width: 0 }}\r\n                      animate={{ width: \"100%\" }}\r\n                      transition={{ duration: 0.3 }}\r\n                    />\r\n                  )}\r\n                </motion.h3>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Business details */}\r\n            <div className=\"space-y-2 mb-3 flex-grow\">\r\n              {/* Location - improved design */}\r\n              <div className=\"flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800\">\r\n                <MapPin className=\"h-4 w-4 text-neutral-500 dark:text-neutral-400 flex-shrink-0 mt-0.5\" />\r\n                <div className=\"flex-1 min-w-0\">\r\n                  {(() => {\r\n                    const { addressLine, localityCity, statePin, hasAddress } = getAddressComponents();\r\n                    return hasAddress ? (\r\n                      <>\r\n                        {addressLine && (\r\n                          <p className=\"text-xs font-medium text-neutral-700 dark:text-neutral-300 line-clamp-1\">\r\n                            {addressLine}\r\n                          </p>\r\n                        )}\r\n                        {localityCity && (\r\n                          <p className=\"text-xs text-neutral-600 dark:text-neutral-400 line-clamp-1 mt-0.5\">\r\n                            {localityCity}\r\n                          </p>\r\n                        )}\r\n                        {statePin && (\r\n                          <p className=\"text-xs text-neutral-500 dark:text-neutral-500 line-clamp-1 mt-0.5\">\r\n                            {statePin}\r\n                          </p>\r\n                        )}\r\n                      </>\r\n                    ) : (\r\n                      <p className=\"text-xs text-neutral-500 dark:text-neutral-500 italic\">\r\n                        Location not specified\r\n                      </p>\r\n                    );\r\n                  })()}\r\n                </div>\r\n              </div>\r\n\r\n\r\n\r\n              {/* Category badge - if available */}\r\n              {/* Removed business_category as it's not in the BusinessCardData type */}\r\n            </div>\r\n\r\n            {/* Stats row */}\r\n            <div className=\"flex items-center justify-between text-xs border-t border-neutral-100 dark:border-neutral-800 pt-2 mt-auto\">\r\n              <div className=\"flex items-center gap-1 text-rose-500 dark:text-rose-400\">\r\n                <Heart className=\"h-3.5 w-3.5\" />\r\n                <span>{formatIndianNumberShort(business.total_likes || 0)}</span>\r\n              </div>\r\n\r\n              <div className=\"flex items-center gap-1 text-blue-500 dark:text-blue-400\">\r\n                <UserPlus className=\"h-3.5 w-3.5\" />\r\n                <span>{formatIndianNumberShort(business.total_subscriptions || 0)}</span>\r\n              </div>\r\n\r\n              <div className=\"flex items-center gap-1 text-amber-500 dark:text-amber-400\">\r\n                <Star className=\"h-3.5 w-3.5\" />\r\n                <span>{(business.average_rating || 0).toFixed(1)}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Hover effect overlay */}\r\n          <motion.div\r\n            className=\"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/5 to-blue-500/5 dark:from-[var(--brand-gold)]/10 dark:to-blue-500/10 pointer-events-none\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: isHovered ? 1 : 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          />\r\n        </motion.div>\r\n      </Link>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;AARA;;;;;;;AAee,SAAS,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAA6B;;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QAAE,MAAM;QAAO,QAAQ;IAAI;IAE/D,+CAA+C;IAC/C,MAAM,uBAAuB;QAC3B,2BAA2B;QAC3B,MAAM,cAAc,SAAS,YAAY,IAAI;QAE7C,8BAA8B;QAC9B,MAAM,eAAe;YAAC,SAAS,QAAQ;YAAE,SAAS,IAAI;SAAC,CACpD,MAAM,CAAC,SACP,IAAI,CAAC;QAER,6BAA6B;QAC7B,MAAM,WAAW;YACf,SAAS,KAAK;YACd,SAAS,OAAO,GAAG,CAAC,KAAK,EAAE,SAAS,OAAO,EAAE,GAAG;SACjD,CACE,MAAM,CAAC,SACP,IAAI,CAAC;QAER,OAAO;YACL;YACA;YACA;YACA,YAAY,CAAC,CAAC,CAAC,eAAe,gBAAgB,QAAQ;QACxD;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI,CAAC;QAC5C,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;QACjD,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;kBAE/B,cAAA,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;YAAE,WAAU;sBAClD,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,WAAW,YACP,wGACA;oBACJ,aAAa,YACT,qCACA;gBACN;;kCAGA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,IAAI,OAAO,mBACjD,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,SAAS,QAAQ;gDACtB,KAAK,SAAS,aAAa,IAAI;gDAC/B,IAAI;gDACJ,WAAU;gDACV,OAAM;;;;;qEAGR,6LAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,EAAE,OAAO,MAAM;;;;;;4CAKzC,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,YAAY;oDAAE,UAAU;gDAAI;;;;;;;;;;;;kDAKlC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,SAAS;gDAAE,OAAO,YAAY,sBAAsB;4CAAG;4CACvD,YAAY;gDAAE,UAAU;4CAAI;;gDAE3B,SAAS,aAAa;gDAGtB,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAO;oDACzB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;;;;;;;;;;;;0CAQtC,6LAAC;gCAAI,WAAU;0CAEb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAI,WAAU;sDACZ,CAAC;gDACA,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;gDAC5D,OAAO,2BACL;;wDACG,6BACC,6LAAC;4DAAE,WAAU;sEACV;;;;;;wDAGJ,8BACC,6LAAC;4DAAE,WAAU;sEACV;;;;;;wDAGJ,0BACC,6LAAC;4DAAE,WAAU;sEACV;;;;;;;iFAKP,6LAAC;oDAAE,WAAU;8DAAwD;;;;;;4CAIzE,CAAC;;;;;;;;;;;;;;;;;0CAWP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAM,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,WAAW,IAAI;;;;;;;;;;;;kDAGzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAM,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,mBAAmB,IAAI;;;;;;;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAM,CAAC,SAAS,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAMpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS,YAAY,IAAI;wBAAE;wBACtC,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;;;;;AAMxC;GA/KwB;;QAGL,gLAAA,CAAA,YAAS;;;KAHJ", "debugId": null}}, {"offset": {"line": 4019, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/AnimatedBusinessGridSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport default function AnimatedBusinessGridSkeleton() {\r\n  return (\r\n    <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4\">\r\n      {Array.from({ length: 6 }).map((_, index) => (\r\n        <SkeletonCard key={index} index={index} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SkeletonCard({ index }: { index: number }) {\r\n  return (\r\n    <motion.div\r\n      className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm p-2 sm:p-3 md:p-4 h-full flex flex-col w-full\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, delay: index * 0.05 }}\r\n    >\r\n      {/* Header with logo and name - centered */}\r\n      <div className=\"flex flex-col items-center text-center mb-4\">\r\n        <Skeleton className=\"h-12 w-12 sm:h-16 sm:w-16 rounded-full flex-shrink-0 mb-2\" />\r\n        <div className=\"space-y-2 w-full px-2\">\r\n          <Skeleton className=\"h-5 w-3/4 mx-auto\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Business details */}\r\n      <div className=\"space-y-2 mb-3 flex-grow\">\r\n        {/* Location with multiple lines */}\r\n        <div className=\"flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800\">\r\n          <Skeleton className=\"h-4 w-4 rounded-full flex-shrink-0 mt-0.5\" />\r\n          <div className=\"flex-1 space-y-1.5\">\r\n            <Skeleton className=\"h-3 w-full\" />\r\n            <Skeleton className=\"h-3 w-4/5\" />\r\n            <Skeleton className=\"h-3 w-3/5\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category badge */}\r\n        <div className=\"flex items-center justify-center mt-2\">\r\n          <Skeleton className=\"h-6 w-24 rounded-full\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats row */}\r\n      <div className=\"flex items-center justify-between pt-2 border-t border-neutral-100 dark:border-neutral-800 mt-auto\">\r\n        <Skeleton className=\"h-4 w-12\" />\r\n        <Skeleton className=\"h-4 w-12\" />\r\n        <Skeleton className=\"h-4 w-12\" />\r\n      </div>\r\n\r\n\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gBAAyB,OAAO;eAAd;;;;;;;;;;AAI3B;KARwB;AAUxB,SAAS,aAAa,EAAE,KAAK,EAAqB;IAChD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;;0BAGjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAKxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAM5B;MA5CS", "debugId": null}}, {"offset": {"line": 4213, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/PopularBusinessesSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport <PERSON> from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { fetchBusinessesBySearch } from \"@/app/(main)/discover/actions/businessActions\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport AnimatedBusinessCard from \"@/app/(main)/discover/components/AnimatedBusinessCard\"; // Import AnimatedBusinessCard\r\nimport { motion } from \"framer-motion\";\r\nimport AnimatedBusinessGridSkeleton from \"@/app/(main)/discover/components/AnimatedBusinessGridSkeleton\";\r\n\r\nexport default function PopularBusinessesSection() {\r\n  const [businesses, setBusinesses] = useState<BusinessCardData[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const getPopularBusinesses = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        const { data, error } = await fetchBusinessesBySearch({\r\n          page: 1,\r\n          limit: 6, // Fetch 6 popular businesses\r\n          sortBy: \"likes_desc\", // Sort by most liked in descending order\r\n        });\r\n\r\n        if (error) {\r\n          setError(error);\r\n        } else if (data) {\r\n          setBusinesses(data.businesses);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch popular businesses:\", err);\r\n        setError(\"Failed to load popular businesses.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    getPopularBusinesses();\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-8 md:py-12\">\r\n      <div className=\"container px-2 sm:px-4 md:px-6 mx-auto\">\r\n        <div className=\"flex flex-col items-center justify-center space-y-4 text-center\">\r\n          <div className=\"space-y-2\">\r\n            <h2 className=\"text-3xl font-bold tracking-tighter sm:text-5xl\">Popular Businesses</h2>\r\n            <p className=\"max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400\">\r\n              Explore the most popular businesses on Dukancard.\r\n            </p>\r\n          </div>\r\n        </div>\r\n        {loading ? (\r\n          <div className=\"mx-auto max-w-7xl py-8\">\r\n            <AnimatedBusinessGridSkeleton />\r\n          </div>\r\n        ) : (\r\n          <div className=\"mx-auto grid max-w-7xl items-start gap-2 sm:gap-3 md:gap-4 py-8 grid-cols-2 md:grid-cols-3 lg:grid-cols-6 justify-items-stretch\">\r\n            {error && <p className=\"text-red-500 col-span-full text-center\">{error}</p>}\r\n            {!error && businesses.length === 0 && (\r\n              <p className=\"col-span-full text-center text-gray-500\">No popular businesses found.</p>\r\n            )}\r\n            {businesses.map((business, index) => (\r\n              <motion.div\r\n                key={business.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.4, delay: index * 0.1 }}\r\n                className=\"w-full h-full\"\r\n              >\r\n                <AnimatedBusinessCard business={business} index={index} />\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        )}\r\n        <div className=\"flex justify-center mt-8 w-full\">\r\n          <Link href=\"/discover?tab=cards\" passHref>\r\n            <Button size=\"lg\">View All Businesses</Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA,oSAA0F,8BAA8B;AACxH;AACA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,MAAM;2EAAuB;oBAC3B,WAAW;oBACX,SAAS;oBACT,IAAI;wBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,iLAAA,CAAA,0BAAuB,AAAD,EAAE;4BACpD,MAAM;4BACN,OAAO;4BACP,QAAQ;wBACV;wBAEA,IAAI,OAAO;4BACT,SAAS;wBACX,OAAO,IAAI,MAAM;4BACf,cAAc,KAAK,UAAU;wBAC/B;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,uCAAuC;wBACrD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;6CAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,6LAAC;gCAAE,WAAU;0CAA4G;;;;;;;;;;;;;;;;;gBAK5H,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6KAAA,CAAA,UAA4B;;;;;;;;;yCAG/B,6LAAC;oBAAI,WAAU;;wBACZ,uBAAS,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;wBAChE,CAAC,SAAS,WAAW,MAAM,KAAK,mBAC/B,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;wBAExD,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,WAAU;0CAEV,cAAA,6LAAC,qKAAA,CAAA,UAAoB;oCAAC,UAAU;oCAAU,OAAO;;;;;;+BAN5C,SAAS,EAAE;;;;;;;;;;;8BAWxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAsB,QAAQ;kCACvC,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GA1EwB;KAAA", "debugId": null}}, {"offset": {"line": 4415, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/ModernSearchSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport {\r\n  pincodeSchema,\r\n  citySchema,\r\n  LocationSearchFormData,\r\n  CitySearchFormData,\r\n} from \"@/lib/schemas/locationSchemas\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  MapPin,\r\n  Loader2,\r\n  Building,\r\n  ArrowRight,\r\n  X\r\n} from \"lucide-react\";\r\nimport {\r\n  Command,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandList\r\n} from \"@/components/ui/command\";\r\nimport { getCitySuggestionsClient } from \"@/lib/client/locationUtils\";\r\n// Removed PopularCategoriesSection import as it's now in a separate component\r\n\r\ninterface ModernSearchSectionProps {\r\n  minimal?: boolean;\r\n}\r\n\r\nexport default function ModernSearchSection({ }: ModernSearchSectionProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isLoadingCities, setIsLoadingCities] = useState(false);\r\n  const [searchType, setSearchType] = useState<\"pincode\" | \"city\">(\"pincode\");\r\n  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);\r\n  const [cityQuery, setCityQuery] = useState(\"\");\r\n  const [_selectedCity, setSelectedCity] = useState(\"\");\r\n  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);\r\n  const cityInputRef = useRef<HTMLInputElement>(null);\r\n  const [_isHovered, setIsHovered] = useState(false);\r\n\r\n  // Create a unique ID for the layout animation to ensure it works on initial render\r\n  const [layoutId] = useState(`homepage-search-background-${Math.random()}`);\r\n\r\n  // Pincode search form using the existing schema\r\n  const pincodeForm = useForm<LocationSearchFormData>({\r\n    resolver: zodResolver(pincodeSchema),\r\n    defaultValues: {\r\n      pincode: \"\",\r\n      locality: null,\r\n    },\r\n    mode: \"onChange\",\r\n  });\r\n\r\n  // City search form using the city schema\r\n  const cityForm = useForm<CitySearchFormData>({\r\n    resolver: zodResolver(citySchema),\r\n    defaultValues: {\r\n      city: \"\",\r\n      locality: null,\r\n    },\r\n    mode: \"onChange\",\r\n  });\r\n\r\n  // Fetch city suggestions when user types\r\n  useEffect(() => {\r\n    const fetchCitySuggestions = async () => {\r\n      if (cityQuery.length < 2) {\r\n        setCitySuggestions([]);\r\n        setIsLoadingCities(false);\r\n        return;\r\n      }\r\n\r\n      setIsLoadingCities(true);\r\n      try {\r\n        const result = await getCitySuggestionsClient(cityQuery);\r\n\r\n        if (result.cities) {\r\n          // Log the city suggestions for debugging\r\n          console.log(\"City suggestions:\", result.cities);\r\n          setCitySuggestions(result.cities);\r\n        } else if (result.error) {\r\n          console.error(\"Error fetching city suggestions:\", result.error);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching city suggestions:\", error);\r\n      } finally {\r\n        setIsLoadingCities(false);\r\n      }\r\n    };\r\n\r\n    fetchCitySuggestions();\r\n  }, [cityQuery]);\r\n\r\n  // Handle pincode form submission\r\n  const onPincodeSubmit = (data: LocationSearchFormData) => {\r\n    setIsLoading(true);\r\n\r\n    // Use a slight delay to ensure the loading state is visible\r\n    setTimeout(() => {\r\n      try {\r\n        // Create the URL with search parameters\r\n        const params = new URLSearchParams();\r\n        params.set(\"pincode\", data.pincode);\r\n\r\n        // Add locality if present\r\n        if (data.locality) {\r\n          params.set(\"locality\", data.locality);\r\n        }\r\n\r\n        const url = `/discover?${params.toString()}`;\r\n\r\n        // Use window.location for a full page navigation\r\n        window.location.href = url;\r\n      } catch (error) {\r\n        console.error(\"Navigation failed:\", error);\r\n        setIsLoading(false);\r\n      }\r\n    }, 500); // Small delay for better UX\r\n  };\r\n\r\n  // Handle city form submission\r\n  const onCitySubmit = (data: CitySearchFormData) => {\r\n    setIsLoading(true);\r\n\r\n    // Use a slight delay to ensure the loading state is visible\r\n    setTimeout(() => {\r\n      try {\r\n        // Create the URL with search parameters\r\n        const params = new URLSearchParams();\r\n        params.set(\"city\", data.city);\r\n\r\n        // Add locality if present\r\n        if (data.locality) {\r\n          params.set(\"locality\", data.locality);\r\n        }\r\n\r\n        const url = `/discover?${params.toString()}`;\r\n\r\n        // Use window.location for a full page navigation\r\n        window.location.href = url;\r\n      } catch (error) {\r\n        console.error(\"Navigation failed:\", error);\r\n        setIsLoading(false);\r\n      }\r\n    }, 500); // Small delay for better UX\r\n  };\r\n\r\n  // Handle city selection from dropdown\r\n  const handleCitySelect = (city: string) => {\r\n    setSelectedCity(city);\r\n    cityForm.setValue(\"city\", city, { shouldValidate: true });\r\n    setCityQuery(city);\r\n    setIsCityDropdownOpen(false);\r\n  };\r\n\r\n  // Animation is handled directly in the components\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {/* Centered header text */}\r\n      <motion.div\r\n        className=\"text-center mb-6\"\r\n        initial={{ opacity: 0, y: -10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.7 }}\r\n      >\r\n        <h2 className=\"text-2xl md:text-3xl font-bold mb-2 relative inline-block\">\r\n          Find <span className=\"text-[var(--brand-gold)]\">Businesses</span> Near You\r\n          <motion.div\r\n            className=\"absolute -bottom-1 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-[var(--brand-gold)]/70 to-transparent\"\r\n            initial={{ width: \"0%\", left: \"50%\" }}\r\n            animate={{ width: \"100%\", left: \"0%\" }}\r\n            transition={{ duration: 1, delay: 0.3 }}\r\n          />\r\n        </h2>\r\n\r\n        <p className=\"text-neutral-600 dark:text-neutral-400\">\r\n          Search across <span className=\"font-medium\">2.8 Lakh+</span> businesses and services\r\n        </p>\r\n      </motion.div>\r\n\r\n      {/* Main search container */}\r\n      <motion.div\r\n        className=\"relative w-full\"\r\n        initial={{ opacity: 1 }}\r\n        animate={{ opacity: 1 }}\r\n        onMouseEnter={() => setIsHovered(true)}\r\n        onMouseLeave={() => setIsHovered(false)}\r\n        key={`homepage-search-container-${searchType}`}\r\n      >\r\n        {/* Background elements */}\r\n        <div className=\"absolute inset-0 -z-10 overflow-hidden\">\r\n          {/* Animated background elements */}\r\n          <motion.div\r\n            className=\"absolute top-1/3 left-1/4 w-32 h-32 rounded-full bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 blur-xl\"\r\n            animate={{\r\n              y: [0, -15, 0],\r\n              scale: [1, 1.1, 1],\r\n              opacity: [0.5, 0.7, 0.5],\r\n            }}\r\n            transition={{\r\n              duration: 8,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\"\r\n            }}\r\n          />\r\n\r\n          <motion.div\r\n            className=\"absolute bottom-1/4 right-1/4 w-40 h-40 rounded-full bg-blue-500/5 dark:bg-blue-400/10 blur-xl\"\r\n            animate={{\r\n              y: [0, 20, 0],\r\n              scale: [1, 1.15, 1],\r\n              opacity: [0.4, 0.6, 0.4],\r\n            }}\r\n            transition={{\r\n              duration: 10,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 1\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Search type selector */}\r\n        <motion.div\r\n          className=\"flex justify-center mb-5\"\r\n          initial={{ opacity: 1, y: 0 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          layoutRoot\r\n        >\r\n          <div className=\"inline-flex rounded-full p-1 bg-white/30 dark:bg-neutral-800/30 backdrop-blur-md border border-neutral-200/50 dark:border-neutral-700/50\">\r\n            <motion.button\r\n              className={`relative px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${\r\n                searchType === \"pincode\"\r\n                  ? \"text-[var(--brand-gold-foreground)]\"\r\n                  : \"text-neutral-600 dark:text-neutral-400\"\r\n              }`}\r\n              onClick={() => setSearchType(\"pincode\")}\r\n              initial={{ opacity: 1 }}\r\n              animate={searchType === \"pincode\" ? { opacity: 1, scale: 1.05 } : { opacity: 0.7, scale: 1 }}\r\n              whileHover={{ scale: searchType === \"pincode\" ? 1.05 : 1.02 }}\r\n              whileTap={{ scale: 0.98 }}\r\n            >\r\n              {/* Always render the background for pincode button */}\r\n              {searchType === \"pincode\" && (\r\n                <motion.div\r\n                  className=\"absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10\"\r\n                  layoutId={layoutId}\r\n                  initial={{ opacity: 1 }}\r\n                  animate={{ scale: 1 }}\r\n                  transition={{ type: \"spring\", duration: 0.5 }}\r\n                />\r\n              )}\r\n              <div className=\"flex items-center gap-1.5\">\r\n                <MapPin className=\"h-3.5 w-3.5\" />\r\n                <span>Pincode</span>\r\n              </div>\r\n            </motion.button>\r\n\r\n            <motion.button\r\n              className={`relative px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${\r\n                searchType === \"city\"\r\n                  ? \"text-[var(--brand-gold-foreground)]\"\r\n                  : \"text-neutral-600 dark:text-neutral-400\"\r\n              }`}\r\n              onClick={() => setSearchType(\"city\")}\r\n              initial={{ opacity: 1 }}\r\n              animate={searchType === \"city\" ? { opacity: 1, scale: 1.05 } : { opacity: 0.7, scale: 1 }}\r\n              whileHover={{ scale: searchType === \"city\" ? 1.05 : 1.02 }}\r\n              whileTap={{ scale: 0.98 }}\r\n            >\r\n              {/* Always render the background for city button */}\r\n              {searchType === \"city\" && (\r\n                <motion.div\r\n                  className=\"absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10\"\r\n                  layoutId={layoutId}\r\n                  initial={{ opacity: 1 }}\r\n                  animate={{ scale: 1 }}\r\n                  transition={{ type: \"spring\", duration: 0.5 }}\r\n                />\r\n              )}\r\n              <div className=\"flex items-center gap-1.5\">\r\n                <Building className=\"h-3.5 w-3.5\" />\r\n                <span>City</span>\r\n              </div>\r\n            </motion.button>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Search input area */}\r\n        <div className=\"w-full\">\r\n          <AnimatePresence mode=\"wait\" initial={false}>\r\n          {searchType === \"pincode\" ? (\r\n            <motion.div\r\n              key=\"homepage-pincode-search-form\"\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -10 }}\r\n              transition={{ duration: 0.2, ease: \"easeInOut\" }}\r\n              className=\"w-full\"\r\n            >\r\n              <form onSubmit={pincodeForm.handleSubmit(onPincodeSubmit)} className=\"w-full\">\r\n                <div className=\"relative w-full\">\r\n                  <div className=\"relative flex flex-col md:flex-row items-center gap-3 w-full\">\r\n                    <div className=\"relative w-full flex-1 group\">\r\n                      <motion.div\r\n                        className=\"absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[var(--brand-gold)]/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur transition-opacity duration-300\"\r\n                        whileHover={{ opacity: 0.8 }}\r\n                      />\r\n\r\n                      <div className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none text-neutral-400 dark:text-neutral-500\">\r\n                        <MapPin className=\"h-5 w-5\" />\r\n                      </div>\r\n\r\n                      <Input\r\n                        id=\"pincode\"\r\n                        type=\"tel\"\r\n                        inputMode=\"numeric\"\r\n                        maxLength={6}\r\n                        placeholder=\"Enter pincode to find nearby businesses\"\r\n                        {...pincodeForm.register(\"pincode\")}\r\n                        onKeyDown={(e) => {\r\n                          // Allow only numbers, backspace, delete, tab, arrow keys, and enter\r\n                          if (\r\n                            !/^\\d$/.test(e.key) && // Allow digits\r\n                            e.key !== 'Backspace' &&\r\n                            e.key !== 'Delete' &&\r\n                            e.key !== 'Tab' &&\r\n                            e.key !== 'Enter' &&\r\n                            !e.key.includes('Arrow')\r\n                          ) {\r\n                            e.preventDefault();\r\n                          }\r\n                        }}\r\n                        className=\"pl-12 pr-4 py-6 h-14 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-800/50 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-base rounded-lg w-full shadow-sm relative z-0\"\r\n                      />\r\n                    </div>\r\n\r\n                    <motion.div\r\n                      className=\"w-full md:w-auto mt-3 md:ml-4 md:mt-0 px-0\"\r\n                    >\r\n                      <Button\r\n                        type=\"submit\"\r\n                        disabled={isLoading || !pincodeForm.getValues(\"pincode\")}\r\n                        className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium h-12 px-4 md:px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden w-full text-sm\"\r\n                      >\r\n                        {/* Shimmer effect */}\r\n                        <motion.div\r\n                          className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent\"\r\n                          initial={{ x: \"-100%\" }}\r\n                          animate={{ x: \"100%\" }}\r\n                          transition={{\r\n                            duration: 1.5,\r\n                            repeat: Infinity,\r\n                            ease: \"linear\",\r\n                            repeatDelay: 0.5\r\n                          }}\r\n                        />\r\n\r\n                        {isLoading ? (\r\n                          <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                        ) : (\r\n                          <div className=\"flex items-center gap-1\">\r\n                            <span>Search</span>\r\n                            <ArrowRight className=\"h-5 w-5\" />\r\n                          </div>\r\n                        )}\r\n                      </Button>\r\n                    </motion.div>\r\n                  </div>\r\n\r\n                  {pincodeForm.formState.errors.pincode && (\r\n                    <motion.p\r\n                      initial={{ opacity: 0, y: -5 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      className=\"text-sm text-red-500 dark:text-red-400 mt-2 ml-1\"\r\n                    >\r\n                      {pincodeForm.formState.errors.pincode.message}\r\n                    </motion.p>\r\n                  )}\r\n                </div>\r\n              </form>\r\n            </motion.div>\r\n          ) : (\r\n            <motion.div\r\n              key=\"homepage-city-search-form\"\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -10 }}\r\n              transition={{ duration: 0.2, ease: \"easeInOut\" }}\r\n              className=\"w-full\"\r\n            >\r\n              <form onSubmit={cityForm.handleSubmit(onCitySubmit)} className=\"w-full\">\r\n                <div className=\"relative w-full\">\r\n                  <div className=\"relative flex flex-col md:flex-row items-center gap-3 w-full\">\r\n                    <div className=\"relative w-full flex-1 group\">\r\n                      <motion.div\r\n                        className=\"absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[var(--brand-gold)]/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur transition-opacity duration-300\"\r\n                        whileHover={{ opacity: 0.8 }}\r\n                      />\r\n\r\n                      <div className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none text-neutral-400 dark:text-neutral-500\">\r\n                        <Building className=\"h-5 w-5\" />\r\n                      </div>\r\n\r\n                      <div className=\"relative w-full\">\r\n                        <Input\r\n                          id=\"city\"\r\n                          placeholder=\"Enter city name\"\r\n                          value={cityQuery}\r\n                          onChange={(e) => {\r\n                            setCityQuery(e.target.value);\r\n                            cityForm.setValue(\"city\", e.target.value, { shouldValidate: true });\r\n                            if (e.target.value.length >= 2) {\r\n                              setIsCityDropdownOpen(true);\r\n                            } else {\r\n                              setIsCityDropdownOpen(false);\r\n                            }\r\n                          }}\r\n                          onFocus={() => {\r\n                            if (cityQuery.length >= 2) {\r\n                              setIsCityDropdownOpen(true);\r\n                            }\r\n                          }}\r\n                          className=\"pl-12 pr-4 py-6 h-14 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-800/50 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-base rounded-lg w-full shadow-sm relative z-0\"\r\n                          ref={cityInputRef}\r\n                        />\r\n\r\n                        {cityQuery && (\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              setCityQuery(\"\");\r\n                              cityForm.setValue(\"city\", \"\", { shouldValidate: true });\r\n                              setIsCityDropdownOpen(false);\r\n                            }}\r\n                            className=\"absolute right-3 top-1/2 transform -translate-y-1/2 z-10 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors\"\r\n                          >\r\n                            <X className=\"h-4 w-4\" />\r\n                          </button>\r\n                        )}\r\n\r\n                        {isCityDropdownOpen && (cityQuery.length >= 2) && (\r\n                          <div className=\"absolute z-50 w-full mt-1 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-md rounded-lg shadow-lg border border-neutral-200/50 dark:border-neutral-700/50 max-h-60 overflow-auto\">\r\n                            <Command>\r\n                              <CommandList>\r\n                                <CommandGroup>\r\n                                  {isLoadingCities ? (\r\n                                    // Skeleton loading UI\r\n                                    <>\r\n                                      {[1, 2, 3, 4, 5].map((index) => (\r\n                                        <div key={index} className=\"p-3 flex items-center animate-pulse\">\r\n                                          <div className=\"h-4 w-4 rounded-full bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/10 mr-2 flex-shrink-0\"></div>\r\n                                          <div className=\"h-4 w-full max-w-[120px] bg-neutral-200 dark:bg-neutral-700 rounded\"></div>\r\n                                        </div>\r\n                                      ))}\r\n                                    </>\r\n                                  ) : citySuggestions.length > 0 ? (\r\n                                    // Results\r\n                                    citySuggestions.map((city) => (\r\n                                      <CommandItem\r\n                                        key={city}\r\n                                        onSelect={() => handleCitySelect(city)}\r\n                                        className=\"cursor-pointer p-3 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors\"\r\n                                      >\r\n                                        <Building className=\"mr-2 h-4 w-4 text-[var(--brand-gold)]\" />\r\n                                        <span>{city}</span>\r\n                                      </CommandItem>\r\n                                    ))\r\n                                  ) : (\r\n                                    // No results\r\n                                    <div className=\"p-3 text-center text-neutral-500 dark:text-neutral-400\">\r\n                                      No cities found\r\n                                    </div>\r\n                                  )}\r\n                                </CommandGroup>\r\n                              </CommandList>\r\n                            </Command>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <motion.div\r\n                      className=\"w-full md:w-auto mt-3 md:ml-4 md:mt-0 px-0\"\r\n                    >\r\n                      <Button\r\n                        type=\"submit\"\r\n                        disabled={isLoading || !cityForm.getValues(\"city\") || cityForm.formState.errors.city !== undefined}\r\n                        className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium h-12 px-4 md:px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden w-full text-sm\"\r\n                      >\r\n                        {/* Shimmer effect */}\r\n                        <motion.div\r\n                          className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent\"\r\n                          initial={{ x: \"-100%\" }}\r\n                          animate={{ x: \"100%\" }}\r\n                          transition={{\r\n                            duration: 1.5,\r\n                            repeat: Infinity,\r\n                            ease: \"linear\",\r\n                            repeatDelay: 0.5\r\n                          }}\r\n                        />\r\n\r\n                        {isLoading ? (\r\n                          <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                        ) : (\r\n                          <div className=\"flex items-center gap-1\">\r\n                            <span>Search</span>\r\n                            <ArrowRight className=\"h-5 w-5\" />\r\n                          </div>\r\n                        )}\r\n                      </Button>\r\n                    </motion.div>\r\n                  </div>\r\n\r\n                  {cityForm.formState.errors.city && (\r\n                    <motion.p\r\n                      initial={{ opacity: 0, y: -5 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      className=\"text-sm text-red-500 dark:text-red-400 mt-2 ml-1\"\r\n                    >\r\n                      {cityForm.formState.errors.city.message}\r\n                    </motion.p>\r\n                  )}\r\n                </div>\r\n              </form>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n\r\n        </div>\r\n        {/* Popular Categories section removed - now in a separate component */}\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAMA;;;AA3BA;;;;;;;;;;;AAkCe,SAAS,oBAAoB,EAA6B;;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,YAAY,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE5C,mFAAmF;IACnF,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,2BAA2B,EAAE,KAAK,MAAM,IAAI;IAEzE,gDAAgD;IAChD,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAA0B;QAClD,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,oIAAA,CAAA,gBAAa;QACnC,eAAe;YACb,SAAS;YACT,UAAU;QACZ;QACA,MAAM;IACR;IAEA,yCAAyC;IACzC,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAsB;QAC3C,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,oIAAA,CAAA,aAAU;QAChC,eAAe;YACb,MAAM;YACN,UAAU;QACZ;QACA,MAAM;IACR;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;sEAAuB;oBAC3B,IAAI,UAAU,MAAM,GAAG,GAAG;wBACxB,mBAAmB,EAAE;wBACrB,mBAAmB;wBACnB;oBACF;oBAEA,mBAAmB;oBACnB,IAAI;wBACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,2BAAwB,AAAD,EAAE;wBAE9C,IAAI,OAAO,MAAM,EAAE;4BACjB,yCAAyC;4BACzC,QAAQ,GAAG,CAAC,qBAAqB,OAAO,MAAM;4BAC9C,mBAAmB,OAAO,MAAM;wBAClC,OAAO,IAAI,OAAO,KAAK,EAAE;4BACvB,QAAQ,KAAK,CAAC,oCAAoC,OAAO,KAAK;wBAChE;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;oBACpD,SAAU;wBACR,mBAAmB;oBACrB;gBACF;;YAEA;QACF;wCAAG;QAAC;KAAU;IAEd,iCAAiC;IACjC,MAAM,kBAAkB,CAAC;QACvB,aAAa;QAEb,4DAA4D;QAC5D,WAAW;YACT,IAAI;gBACF,wCAAwC;gBACxC,MAAM,SAAS,IAAI;gBACnB,OAAO,GAAG,CAAC,WAAW,KAAK,OAAO;gBAElC,0BAA0B;gBAC1B,IAAI,KAAK,QAAQ,EAAE;oBACjB,OAAO,GAAG,CAAC,YAAY,KAAK,QAAQ;gBACtC;gBAEA,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,QAAQ,IAAI;gBAE5C,iDAAiD;gBACjD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,aAAa;YACf;QACF,GAAG,MAAM,4BAA4B;IACvC;IAEA,8BAA8B;IAC9B,MAAM,eAAe,CAAC;QACpB,aAAa;QAEb,4DAA4D;QAC5D,WAAW;YACT,IAAI;gBACF,wCAAwC;gBACxC,MAAM,SAAS,IAAI;gBACnB,OAAO,GAAG,CAAC,QAAQ,KAAK,IAAI;gBAE5B,0BAA0B;gBAC1B,IAAI,KAAK,QAAQ,EAAE;oBACjB,OAAO,GAAG,CAAC,YAAY,KAAK,QAAQ;gBACtC;gBAEA,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,QAAQ,IAAI;gBAE5C,iDAAiD;gBACjD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,aAAa;YACf;QACF,GAAG,MAAM,4BAA4B;IACvC;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,SAAS,QAAQ,CAAC,QAAQ,MAAM;YAAE,gBAAgB;QAAK;QACvD,aAAa;QACb,sBAAsB;IACxB;IAEA,kDAAkD;IAElD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,6LAAC;wBAAG,WAAU;;4BAA4D;0CACnE,6LAAC;gCAAK,WAAU;0CAA2B;;;;;;4BAAiB;0CACjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;oCAAM,MAAM;gCAAM;gCACpC,SAAS;oCAAE,OAAO;oCAAQ,MAAM;gCAAK;gCACrC,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;;;;;;;;;;;;kCAI1C,6LAAC;wBAAE,WAAU;;4BAAyC;0CACtC,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;;;;;;;;;;;;;0BAKhE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;;kCAIjC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAClB,SAAS;wCAAC;wCAAK;wCAAK;qCAAI;gCAC1B;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;;;;;;0CAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;oCACb,OAAO;wCAAC;wCAAG;wCAAM;qCAAE;oCACnB,SAAS;wCAAC;wCAAK;wCAAK;qCAAI;gCAC1B;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;oCACN,OAAO;gCACT;;;;;;;;;;;;kCAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,UAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAW,CAAC,gFAAgF,EAC1F,eAAe,YACX,wCACA,0CACJ;oCACF,SAAS,IAAM,cAAc;oCAC7B,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS,eAAe,YAAY;wCAAE,SAAS;wCAAG,OAAO;oCAAK,IAAI;wCAAE,SAAS;wCAAK,OAAO;oCAAE;oCAC3F,YAAY;wCAAE,OAAO,eAAe,YAAY,OAAO;oCAAK;oCAC5D,UAAU;wCAAE,OAAO;oCAAK;;wCAGvB,eAAe,2BACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAU;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,MAAM;gDAAU,UAAU;4CAAI;;;;;;sDAGhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAW,CAAC,gFAAgF,EAC1F,eAAe,SACX,wCACA,0CACJ;oCACF,SAAS,IAAM,cAAc;oCAC7B,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS,eAAe,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAK,IAAI;wCAAE,SAAS;wCAAK,OAAO;oCAAE;oCACxF,YAAY;wCAAE,OAAO,eAAe,SAAS,OAAO;oCAAK;oCACzD,UAAU;wCAAE,OAAO;oCAAK;;wCAGvB,eAAe,wBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAU;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,MAAM;gDAAU,UAAU;4CAAI;;;;;;sDAGhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;4BAAC,MAAK;4BAAO,SAAS;sCACrC,eAAe,0BACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;gCAC/C,WAAU;0CAEV,cAAA,6LAAC;oCAAK,UAAU,YAAY,YAAY,CAAC;oCAAkB,WAAU;8CACnE,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,YAAY;oEAAE,SAAS;gEAAI;;;;;;0EAG7B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAGpB,6LAAC,6HAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,WAAU;gEACV,WAAW;gEACX,aAAY;gEACX,GAAG,YAAY,QAAQ,CAAC,UAAU;gEACnC,WAAW,CAAC;oEACV,oEAAoE;oEACpE,IACE,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,KAAK,eAAe;oEACtC,EAAE,GAAG,KAAK,eACV,EAAE,GAAG,KAAK,YACV,EAAE,GAAG,KAAK,SACV,EAAE,GAAG,KAAK,WACV,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,UAChB;wEACA,EAAE,cAAc;oEAClB;gEACF;gEACA,WAAU;;;;;;;;;;;;kEAId,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;kEAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,UAAU,aAAa,CAAC,YAAY,SAAS,CAAC;4DAC9C,WAAU;;8EAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,GAAG;oEAAQ;oEACtB,SAAS;wEAAE,GAAG;oEAAO;oEACrB,YAAY;wEACV,UAAU;wEACV,QAAQ;wEACR,MAAM;wEACN,aAAa;oEACf;;;;;;gEAGD,0BACC,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAO/B,YAAY,SAAS,CAAC,MAAM,CAAC,OAAO,kBACnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAE;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DAET,YAAY,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;+BAnFjD;;;;qDA0FN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;gCAC/C,WAAU;0CAEV,cAAA,6LAAC;oCAAK,UAAU,SAAS,YAAY,CAAC;oCAAe,WAAU;8CAC7D,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,YAAY;oEAAE,SAAS;gEAAI;;;;;;0EAG7B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAGtB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6HAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC;4EACT,aAAa,EAAE,MAAM,CAAC,KAAK;4EAC3B,SAAS,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,EAAE;gFAAE,gBAAgB;4EAAK;4EACjE,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG;gFAC9B,sBAAsB;4EACxB,OAAO;gFACL,sBAAsB;4EACxB;wEACF;wEACA,SAAS;4EACP,IAAI,UAAU,MAAM,IAAI,GAAG;gFACzB,sBAAsB;4EACxB;wEACF;wEACA,WAAU;wEACV,KAAK;;;;;;oEAGN,2BACC,6LAAC;wEACC,MAAK;wEACL,SAAS;4EACP,aAAa;4EACb,SAAS,QAAQ,CAAC,QAAQ,IAAI;gFAAE,gBAAgB;4EAAK;4EACrD,sBAAsB;wEACxB;wEACA,WAAU;kFAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;oEAIhB,sBAAuB,UAAU,MAAM,IAAI,mBAC1C,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,+HAAA,CAAA,UAAO;sFACN,cAAA,6LAAC,+HAAA,CAAA,cAAW;0FACV,cAAA,6LAAC,+HAAA,CAAA,eAAY;8FACV,kBACC,sBAAsB;kGACtB;kGACG;4FAAC;4FAAG;4FAAG;4FAAG;4FAAG;yFAAE,CAAC,GAAG,CAAC,CAAC,sBACpB,6LAAC;gGAAgB,WAAU;;kHACzB,6LAAC;wGAAI,WAAU;;;;;;kHACf,6LAAC;wGAAI,WAAU;;;;;;;+FAFP;;;;;wGAMZ,gBAAgB,MAAM,GAAG,IAC3B,UAAU;oFACV,gBAAgB,GAAG,CAAC,CAAC,qBACnB,6LAAC,+HAAA,CAAA,cAAW;4FAEV,UAAU,IAAM,iBAAiB;4FACjC,WAAU;;8GAEV,6LAAC,6MAAA,CAAA,WAAQ;oGAAC,WAAU;;;;;;8GACpB,6LAAC;8GAAM;;;;;;;2FALF;;;;oGAST,aAAa;kGACb,6LAAC;wFAAI,WAAU;kGAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAYxF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;kEAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,UAAU,aAAa,CAAC,SAAS,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK;4DACzF,WAAU;;8EAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,GAAG;oEAAQ;oEACtB,SAAS;wEAAE,GAAG;oEAAO;oEACrB,YAAY;wEACV,UAAU;wEACV,QAAQ;wEACR,MAAM;wEACN,aAAa;oEACf;;;;;;gEAGD,0BACC,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAO/B,SAAS,SAAS,CAAC,MAAM,CAAC,IAAI,kBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAE;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DAET,SAAS,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;+BAzI3C;;;;;;;;;;;;;;;;eArML,CAAC,0BAA0B,EAAE,YAAY;;;;;;;;;;;AA4VtD;GA3fwB;;QAeF,iKAAA,CAAA,UAAO;QAUV,iKAAA,CAAA,UAAO;;;KAzBF", "debugId": null}}, {"offset": {"line": 5422, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/exampleCardData.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\n// Example data for the preview card\r\nexport const exampleCardData: BusinessCardData = {\r\n  id: \"preview-id\", // Add a dummy ID\r\n  member_name: \"<PERSON><PERSON>\",\r\n  title: \"Owner\",\r\n  business_name: \"Mahapatra Kirana & General Store\",\r\n  address_line: \"Shop No. 12, XYZ Nagar\",\r\n  locality: \"Bhubaneswar\",\r\n  city: \"Bhubaneswar\",\r\n  state: \"Odisha\",\r\n  pincode: \"751001\",\r\n  phone: \"+91 700*****89\",\r\n  business_category: \"Retail & General Store\",\r\n  about_bio:\r\n    \"Your friendly neighborhood store for daily needs and essentials. Serving the community since 1985.\",\r\n  business_slug: \"mahapatra-store\",\r\n  logo_url: \"/Dukancard Homepage Card Profile Photo Old Man.webp\", // Profile image for card demo\r\n  instagram_url: \"#\",\r\n  facebook_url: \"#\",\r\n  whatsapp_number: \"7000000089\",\r\n  status: \"online\",\r\n  established_year: 1985, // Example established year matching the bio\r\n  \r\n  // Add other required fields with default/null values if needed by the type\r\n  contact_email: \"<EMAIL>\",\r\n  has_active_subscription: true,\r\n  trial_end_date: new Date(Date.now() + (15 * 24 * 60 * 60 * 1000)).toISOString(), // 15 days from now for demo\r\n  created_at: new Date().toISOString(),\r\n  updated_at: new Date().toISOString(),\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAKO,MAAM,kBAAoC;IAC/C,IAAI;IACJ,aAAa;IACb,OAAO;IACP,eAAe;IACf,cAAc;IACd,UAAU;IACV,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,mBAAmB;IACnB,WACE;IACF,eAAe;IACf,UAAU;IACV,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,QAAQ;IACR,kBAAkB;IAElB,2EAA2E;IAC3E,eAAe;IACf,yBAAyB;IACzB,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAM,KAAK,KAAK,KAAK,KAAK,MAAO,WAAW;IAC7E,YAAY,IAAI,OAAO,WAAW;IAClC,YAAY,IAAI,OAAO,WAAW;AACpC", "debugId": null}}, {"offset": {"line": 5462, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/CardBackground.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\n\r\ninterface CardBackgroundProps {\r\n  className?: string;\r\n}\r\n\r\nexport default function CardBackground({ className = \"\" }: CardBackgroundProps) {\r\n  const [isClient, setIsClient] = useState(false);\r\n  const isMobile = useIsMobile();\r\n\r\n  // Generate floating orbs data\r\n  const [orbs, setOrbs] = useState<Array<{\r\n    id: number;\r\n    x: number;\r\n    y: number;\r\n    size: number;\r\n    delay: number;\r\n    duration: number;\r\n    color: string;\r\n  }>>([]);\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n\r\n    // Generate random orbs\r\n    const orbCount = isMobile ? 4 : 6;\r\n    const newOrbs = Array.from({ length: orbCount }).map((_, index) => {\r\n      return {\r\n        id: index,\r\n        x: Math.random() * 100,\r\n        y: Math.random() * 100,\r\n        size: Math.random() * 100 + 50,\r\n        delay: Math.random() * 2,\r\n        duration: Math.random() * 10 + 15,\r\n        color: index % 2 === 0 ? \"var(--brand-gold)\" : \"#8B5CF6\"\r\n      };\r\n    });\r\n\r\n    setOrbs(newOrbs);\r\n  }, [isMobile]);\r\n\r\n  if (!isClient) return null;\r\n\r\n  return (\r\n    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>\r\n      {/* Premium product advertisement-style background */}\r\n\r\n      {/* Main centered glow - reduced intensity */}\r\n      <motion.div\r\n        className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-[var(--brand-gold)]/8 dark:bg-[var(--brand-gold)]/12 rounded-full blur-[120px]\"\r\n        initial={{ opacity: 0.3, scale: 0.9 }}\r\n        animate={{\r\n          opacity: [0.3, 0.4, 0.3],\r\n          scale: [0.9, 1.05, 0.9]\r\n        }}\r\n        transition={{\r\n          duration: 15,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\"\r\n        }}\r\n      />\r\n\r\n      {/* Secondary glow for depth and dimension - reduced intensity */}\r\n      <motion.div\r\n        className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/15 rounded-full blur-[80px]\"\r\n        initial={{ opacity: 0.2, scale: 0.8 }}\r\n        animate={{\r\n          opacity: [0.2, 0.3, 0.2],\r\n          scale: [0.8, 0.95, 0.8]\r\n        }}\r\n        transition={{\r\n          duration: 12,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n          delay: 2\r\n        }}\r\n      />\r\n\r\n      {/* Professional studio lighting effects - reduced intensity */}\r\n      <div className=\"absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[50%] bg-white/5 dark:bg-white/3 blur-[100px] opacity-40\"></div>\r\n      <div className=\"absolute bottom-0 left-1/2 -translate-x-1/2 w-[70%] h-[40%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 blur-[100px] opacity-40\"></div>\r\n\r\n      {/* Floating orbs - more natural and space-like */}\r\n      {orbs.map((orb) => (\r\n        <motion.div\r\n          key={`orb-${orb.id}`}\r\n          className=\"absolute rounded-full\"\r\n          style={{\r\n            width: `${orb.size}px`,\r\n            height: `${orb.size}px`,\r\n            background: `radial-gradient(circle, ${orb.color}15 0%, transparent 80%)`,\r\n            left: `${orb.x}%`,\r\n            top: `${orb.y}%`,\r\n            filter: \"blur(50px)\",\r\n          }}\r\n          initial={{ opacity: 0.1 }}\r\n          animate={{\r\n            x: [0, orb.id % 2 === 0 ? 20 : -20, 0],\r\n            y: [0, orb.id % 3 === 0 ? -20 : 20, 0],\r\n            opacity: [0.1, 0.2, 0.1],\r\n            scale: [1, 1.05, 1]\r\n          }}\r\n          transition={{\r\n            duration: orb.duration,\r\n            delay: orb.delay,\r\n            repeat: Infinity,\r\n            repeatType: \"reverse\",\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n      ))}\r\n\r\n      {/* Very subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-3 dark:opacity-5\"></div>\r\n\r\n      {/* Premium product highlight effect - reduced intensity */}\r\n      <motion.div\r\n        className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[350px] h-[450px] bg-gradient-to-b from-white/5 via-transparent to-white/5 dark:from-white/8 dark:to-white/8 rounded-full blur-[60px]\"\r\n        animate={{\r\n          opacity: [0.3, 0.4, 0.3],\r\n        }}\r\n        transition={{\r\n          duration: 8,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\"\r\n        }}\r\n      />\r\n\r\n      {/* Subtle vignette effect for depth - reduced intensity */}\r\n      <div className=\"absolute inset-0 bg-radial-gradient-to-transparent from-transparent to-black/10 dark:to-black/15 opacity-30\"></div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,YAAY,EAAE,EAAuB;;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAE3B,8BAA8B;IAC9B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAQ3B,EAAE;IAEN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,YAAY;YAEZ,uBAAuB;YACvB,MAAM,WAAW,WAAW,IAAI;YAChC,MAAM,UAAU,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAS,GAAG,GAAG;oDAAC,CAAC,GAAG;oBACvD,OAAO;wBACL,IAAI;wBACJ,GAAG,KAAK,MAAM,KAAK;wBACnB,GAAG,KAAK,MAAM,KAAK;wBACnB,MAAM,KAAK,MAAM,KAAK,MAAM;wBAC5B,OAAO,KAAK,MAAM,KAAK;wBACvB,UAAU,KAAK,MAAM,KAAK,KAAK;wBAC/B,OAAO,QAAQ,MAAM,IAAI,sBAAsB;oBACjD;gBACF;;YAEA,QAAQ;QACV;mCAAG;QAAC;KAAS;IAEb,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,6LAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BAIjF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAK,OAAO;gBAAI;gBACpC,SAAS;oBACP,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;oBACxB,OAAO;wBAAC;wBAAK;wBAAM;qBAAI;gBACzB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;;;;;;0BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAK,OAAO;gBAAI;gBACpC,SAAS;oBACP,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;oBACxB,OAAO;wBAAC;wBAAK;wBAAM;qBAAI;gBACzB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;oBACN,OAAO;gBACT;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;YAGd,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,OAAO,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;wBACtB,QAAQ,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;wBACvB,YAAY,CAAC,wBAAwB,EAAE,IAAI,KAAK,CAAC,uBAAuB,CAAC;wBACzE,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACjB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBAChB,QAAQ;oBACV;oBACA,SAAS;wBAAE,SAAS;oBAAI;oBACxB,SAAS;wBACP,GAAG;4BAAC;4BAAG,IAAI,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC;4BAAI;yBAAE;wBACtC,GAAG;4BAAC;4BAAG,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,KAAK;4BAAI;yBAAE;wBACtC,SAAS;4BAAC;4BAAK;4BAAK;yBAAI;wBACxB,OAAO;4BAAC;4BAAG;4BAAM;yBAAE;oBACrB;oBACA,YAAY;wBACV,UAAU,IAAI,QAAQ;wBACtB,OAAO,IAAI,KAAK;wBAChB,QAAQ;wBACR,YAAY;wBACZ,MAAM;oBACR;mBAvBK,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;;;;;0BA4BxB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAjIwB;;QAEL,yHAAA,CAAA,cAAW;;;KAFN", "debugId": null}}, {"offset": {"line": 5688, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/EnhancedFeatureElements.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Share2,\r\n  QrCode,\r\n  MapPin,\r\n  ShoppingBag,\r\n  MessageCircle,\r\n  Phone,\r\n  Star,\r\n  Instagram,\r\n  Facebook,\r\n  Globe,\r\n  Heart,\r\n  UserPlus,\r\n  Mail,\r\n  CreditCard,\r\n  Sparkles,\r\n  Clock,\r\n  Palette,\r\n  Image,\r\n  Scan,\r\n} from \"lucide-react\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\ninterface EnhancedFeatureElementsProps {\r\n  minimal?: boolean;\r\n}\r\n\r\nexport default function EnhancedFeatureElements({ minimal = false }: EnhancedFeatureElementsProps) {\r\n  // Client-side only rendering\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Define features with responsive positioning - firefly style\r\n  const features = [\r\n    // Original features with updated positions\r\n    {\r\n      id: 1,\r\n      icon: Share2,\r\n      label: \"Easily Shareable\",\r\n      description: \"Share your digital card via link, QR code, or social media\",\r\n      top: minimal ? \"5%\" : \"10%\",\r\n      left: minimal ? \"-3%\" : \"-5%\",\r\n      delay: 0.1,\r\n      size: \"normal\",\r\n    },\r\n    {\r\n      id: 2,\r\n      icon: QrCode,\r\n      label: \"QR Code\",\r\n      description: \"Scan to instantly view the business card on any device\",\r\n      top: minimal ? \"15%\" : \"25%\",\r\n      right: minimal ? \"-3%\" : \"-5%\",\r\n      delay: 0.2,\r\n      size: \"normal\",\r\n    },\r\n    {\r\n      id: 3,\r\n      icon: MapPin,\r\n      label: \"Location\",\r\n      description: \"Find the business location with integrated maps\",\r\n      bottom: minimal ? \"20%\" : \"30%\",\r\n      left: minimal ? \"-3%\" : \"-5%\",\r\n      delay: 0.3,\r\n      size: \"normal\",\r\n    },\r\n    {\r\n      id: 4,\r\n      icon: ShoppingBag,\r\n      label: \"Products & Services\",\r\n      description: \"Browse available products and services with pricing\",\r\n      bottom: minimal ? \"10%\" : \"15%\",\r\n      right: minimal ? \"-3%\" : \"-5%\",\r\n      delay: 0.4,\r\n      size: \"normal\",\r\n    },\r\n    {\r\n      id: 5,\r\n      icon: MessageCircle,\r\n      label: \"Direct Contact\",\r\n      description: \"Message the business directly through WhatsApp\",\r\n      bottom: minimal ? \"-3%\" : \"-5%\",\r\n      left: minimal ? \"20%\" : \"30%\",\r\n      delay: 0.5,\r\n      size: \"normal\",\r\n    },\r\n    {\r\n      id: 6,\r\n      icon: Phone,\r\n      label: \"One-Click Call\",\r\n      description: \"Call the business with a single tap\",\r\n      top: minimal ? \"-3%\" : \"-5%\",\r\n      right: minimal ? \"20%\" : \"30%\",\r\n      delay: 0.6,\r\n      size: \"normal\",\r\n    },\r\n    {\r\n      id: 7,\r\n      icon: Star,\r\n      label: \"Reviews\",\r\n      description: \"See ratings and reviews from other customers\",\r\n      top: minimal ? \"40%\" : \"50%\",\r\n      right: minimal ? \"-3%\" : \"-5%\",\r\n      delay: 0.7,\r\n      size: \"normal\",\r\n    },\r\n\r\n    // Additional features - firefly style\r\n    {\r\n      id: 8,\r\n      icon: Instagram,\r\n      label: \"Social Media\",\r\n      description: \"Connect with the business on Instagram\",\r\n      top: minimal ? \"25%\" : \"35%\",\r\n      left: minimal ? \"5%\" : \"10%\",\r\n      delay: 0.8,\r\n      size: \"small\",\r\n    },\r\n    {\r\n      id: 9,\r\n      icon: Facebook,\r\n      label: \"Facebook Page\",\r\n      description: \"Visit the business Facebook page\",\r\n      top: minimal ? \"60%\" : \"70%\",\r\n      right: minimal ? \"10%\" : \"15%\",\r\n      delay: 0.9,\r\n      size: \"small\",\r\n    },\r\n    {\r\n      id: 10,\r\n      icon: Globe,\r\n      label: \"Website\",\r\n      description: \"Visit the business website for more information\",\r\n      bottom: minimal ? \"40%\" : \"50%\",\r\n      right: minimal ? \"15%\" : \"20%\",\r\n      delay: 1.0,\r\n      size: \"small\",\r\n    },\r\n    {\r\n      id: 11,\r\n      icon: Heart,\r\n      label: \"Like\",\r\n      description: \"Like the business card to show your appreciation\",\r\n      top: minimal ? \"30%\" : \"40%\",\r\n      left: minimal ? \"15%\" : \"20%\",\r\n      delay: 1.1,\r\n      size: \"tiny\",\r\n    },\r\n    {\r\n      id: 12,\r\n      icon: UserPlus,\r\n      label: \"Subscribe\",\r\n      description: \"Subscribe to receive updates from this business\",\r\n      bottom: minimal ? \"30%\" : \"40%\",\r\n      left: minimal ? \"10%\" : \"15%\",\r\n      delay: 1.2,\r\n      size: \"small\",\r\n    },\r\n    {\r\n      id: 13,\r\n      icon: Mail,\r\n      label: \"Email\",\r\n      description: \"Contact the business via email\",\r\n      top: minimal ? \"70%\" : \"80%\",\r\n      left: minimal ? \"5%\" : \"10%\",\r\n      delay: 1.3,\r\n      size: \"tiny\",\r\n    },\r\n    {\r\n      id: 14,\r\n      icon: CreditCard,\r\n      label: \"Digital Card\",\r\n      description: \"Professional digital business card\",\r\n      top: minimal ? \"50%\" : \"60%\",\r\n      left: minimal ? \"-5%\" : \"-10%\",\r\n      delay: 1.4,\r\n      size: \"small\",\r\n    },\r\n    {\r\n      id: 15,\r\n      icon: Sparkles,\r\n      label: \"Premium Features\",\r\n      description: \"Access premium features with subscription plans\",\r\n      bottom: minimal ? \"50%\" : \"60%\",\r\n      right: minimal ? \"5%\" : \"10%\",\r\n      delay: 1.5,\r\n      size: \"tiny\",\r\n    },\r\n    {\r\n      id: 16,\r\n      icon: Clock,\r\n      label: \"Business Hours\",\r\n      description: \"View the business operating hours\",\r\n      top: minimal ? \"10%\" : \"15%\",\r\n      right: minimal ? \"15%\" : \"20%\",\r\n      delay: 1.6,\r\n      size: \"tiny\",\r\n    },\r\n\r\n    {\r\n      id: 17,\r\n      icon: Palette,\r\n      label: \"Customization\",\r\n      description: \"Customize your card with themes and colors\",\r\n      top: minimal ? \"20%\" : \"30%\",\r\n      right: minimal ? \"25%\" : \"30%\",\r\n      delay: 1.7,\r\n      size: \"tiny\",\r\n    },\r\n    {\r\n      id: 18,\r\n      icon: Image,\r\n      label: \"Gallery\",\r\n      description: \"View business photos and product images\",\r\n      bottom: minimal ? \"25%\" : \"35%\",\r\n      left: minimal ? \"25%\" : \"30%\",\r\n      delay: 1.8,\r\n      size: \"tiny\",\r\n    },\r\n    {\r\n      id: 19,\r\n      icon: Scan,\r\n      label: \"Scan\",\r\n      description: \"Scan QR code to view business details\",\r\n      top: minimal ? \"80%\" : \"85%\",\r\n      right: minimal ? \"10%\" : \"15%\",\r\n      delay: 1.9,\r\n      size: \"tiny\",\r\n    },\r\n  ];\r\n\r\n  // For minimal mode, show a subset of features but still include firefly effect\r\n  const displayFeatures = minimal\r\n    ? features.filter(f => [1, 2, 4, 6, 8, 9, 11, 13, 15, 17, 19].includes(f.id))\r\n    : features;\r\n\r\n  return (\r\n    <div className=\"relative w-full h-full\">\r\n      <TooltipProvider>\r\n        {isClient\r\n          ? displayFeatures.map((feature) => {\r\n              // Calculate unique animation offsets based on feature ID\r\n              const animationOffset = (feature.id * 0.5) % 2;\r\n              const positionStyle: React.CSSProperties = {\r\n                ...(feature.top !== undefined ? { top: feature.top } : {}),\r\n                ...(feature.right !== undefined ? { right: feature.right } : {}),\r\n                ...(feature.bottom !== undefined\r\n                  ? { bottom: feature.bottom }\r\n                  : {}),\r\n                ...(feature.left !== undefined ? { left: feature.left } : {}),\r\n              };\r\n\r\n              return (\r\n                <Tooltip key={feature.id}>\r\n                  <TooltipTrigger asChild>\r\n                    <motion.div\r\n                      className=\"absolute z-10\"\r\n                      style={positionStyle}\r\n                      initial={{ opacity: 0, scale: 0 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      transition={{\r\n                        type: \"spring\",\r\n                        stiffness: 260,\r\n                        damping: 20,\r\n                        delay: feature.delay,\r\n                      }}\r\n                      whileHover={{ scale: 1.1 }}\r\n                    >\r\n                      <motion.div\r\n                        className={`flex items-center justify-center rounded-full shadow-md ${\r\n                          feature.size === \"tiny\"\r\n                            ? \"w-6 h-6 md:w-7 md:h-7\"\r\n                            : feature.size === \"small\"\r\n                              ? \"w-7 h-7 md:w-8 md:h-8\"\r\n                              : \"w-8 h-8 md:w-10 md:h-10\"\r\n                        } ${feature.size === \"tiny\" ? \"bg-white/80 dark:bg-neutral-800/80\" : \"bg-white dark:bg-neutral-800\"}`}\r\n                        style={{\r\n                          boxShadow: \"0 0 10px rgba(var(--brand-gold-rgb), 0.3), 0 0 20px rgba(var(--brand-gold-rgb), 0.1)\"\r\n                        }}\r\n                        whileHover={{\r\n                          boxShadow: \"0 0 15px var(--brand-gold), 0 0 30px rgba(var(--brand-gold-rgb), 0.3)\",\r\n                          scale: 1.1,\r\n                        }}\r\n                        animate={{\r\n                          // Firefly-like random movement\r\n                          x: [\r\n                            0,\r\n                            feature.id % 2 === 0 ? feature.id % 5 : -feature.id % 5,\r\n                            feature.id % 3 === 0 ? -feature.id % 4 : feature.id % 4,\r\n                            0\r\n                          ],\r\n                          y: [\r\n                            0,\r\n                            feature.id % 3 === 0 ? feature.id % 5 : -feature.id % 5,\r\n                            feature.id % 2 === 0 ? -feature.id % 4 : feature.id % 4,\r\n                            0\r\n                          ],\r\n                          scale: [1, 1.05, 0.98, 1],\r\n                          opacity: [0.9, 1, 0.95, 0.9],\r\n                          boxShadow: [\r\n                            \"0 0 10px rgba(var(--brand-gold-rgb), 0.2), 0 0 20px rgba(var(--brand-gold-rgb), 0.1)\",\r\n                            \"0 0 15px rgba(var(--brand-gold-rgb), 0.4), 0 0 25px rgba(var(--brand-gold-rgb), 0.2)\",\r\n                            \"0 0 12px rgba(var(--brand-gold-rgb), 0.3), 0 0 22px rgba(var(--brand-gold-rgb), 0.15)\",\r\n                            \"0 0 10px rgba(var(--brand-gold-rgb), 0.2), 0 0 20px rgba(var(--brand-gold-rgb), 0.1)\"\r\n                          ]\r\n                        }}\r\n                        transition={{\r\n                          // Varied durations for more natural movement\r\n                          duration: 4 + (feature.id % 3),\r\n                          delay: animationOffset,\r\n                          repeat: Infinity,\r\n                          repeatType: \"loop\",\r\n                          ease: \"easeInOut\",\r\n                          times: [0, 0.3, 0.7, 1]\r\n                        }}\r\n                      >\r\n                        {React.createElement(feature.icon, {\r\n                          className: `${\r\n                            feature.size === \"tiny\"\r\n                              ? \"w-3 h-3 md:w-4 md:h-4\"\r\n                              : feature.size === \"small\"\r\n                                ? \"w-3.5 h-3.5 md:w-4.5 md:h-4.5\"\r\n                                : \"w-4 h-4 md:w-5 md:h-5\"\r\n                          } text-[var(--brand-gold)]`,\r\n                          strokeWidth: feature.size === \"tiny\" ? 2 : 1.5\r\n                        })}\r\n                      </motion.div>\r\n\r\n                      {/* Firefly glow effect */}\r\n                      <div className=\"absolute inset-0 overflow-visible rounded-full\">\r\n                        {/* Firefly glow trails - different for each size */}\r\n                        {feature.size === \"tiny\" ? (\r\n                          // Tiny icons have a simple glow\r\n                          <motion.div\r\n                            className=\"absolute inset-0 rounded-full bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/30 blur-[2px]\"\r\n                            animate={{\r\n                              scale: [1, 1.5, 1],\r\n                              opacity: [0.5, 0.8, 0.5]\r\n                            }}\r\n                            transition={{\r\n                              duration: 2 + (feature.id % 2),\r\n                              repeat: Infinity,\r\n                              repeatType: \"loop\",\r\n                              ease: \"easeInOut\",\r\n                              delay: feature.id * 0.1\r\n                            }}\r\n                          />\r\n                        ) : feature.size === \"small\" ? (\r\n                          // Small icons have corner glows\r\n                          <>\r\n                            {/* Top-left and bottom-right corners for small icons */}\r\n                            <motion.div\r\n                              className=\"absolute -top-1 -left-1 w-2/3 h-2/3 rounded-br-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]\"\r\n                              animate={{\r\n                                scale: [0, 1.3],\r\n                                opacity: [0.7, 0]\r\n                              }}\r\n                              transition={{\r\n                                duration: 2.5,\r\n                                repeat: Infinity,\r\n                                repeatType: \"loop\",\r\n                                ease: \"easeOut\",\r\n                                delay: feature.id * 0.1,\r\n                                repeatDelay: 0.2\r\n                              }}\r\n                            />\r\n                            <motion.div\r\n                              className=\"absolute -bottom-1 -right-1 w-2/3 h-2/3 rounded-tl-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]\"\r\n                              animate={{\r\n                                scale: [0, 1.3],\r\n                                opacity: [0.7, 0]\r\n                              }}\r\n                              transition={{\r\n                                duration: 2.5,\r\n                                repeat: Infinity,\r\n                                repeatType: \"loop\",\r\n                                ease: \"easeOut\",\r\n                                delay: feature.id * 0.1 + 1.25,\r\n                                repeatDelay: 0.2\r\n                              }}\r\n                            />\r\n                          </>\r\n                        ) : (\r\n                          // Normal icons have all four corner glows\r\n                          <>\r\n                            {/* Top-left corner pulse */}\r\n                            <motion.div\r\n                              className=\"absolute -top-1 -left-1 w-1/2 h-1/2 rounded-br-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]\"\r\n                              animate={{\r\n                                scale: [0, 1.5],\r\n                                opacity: [0.8, 0]\r\n                              }}\r\n                              transition={{\r\n                                duration: 3,\r\n                                repeat: Infinity,\r\n                                repeatType: \"loop\",\r\n                                ease: \"easeOut\",\r\n                                delay: feature.id * 0.1,\r\n                                repeatDelay: 0.3\r\n                              }}\r\n                            />\r\n\r\n                            {/* Top-right corner pulse */}\r\n                            <motion.div\r\n                              className=\"absolute -top-1 -right-1 w-1/2 h-1/2 rounded-bl-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]\"\r\n                              animate={{\r\n                                scale: [0, 1.5],\r\n                                opacity: [0.8, 0]\r\n                              }}\r\n                              transition={{\r\n                                duration: 3,\r\n                                repeat: Infinity,\r\n                                repeatType: \"loop\",\r\n                                ease: \"easeOut\",\r\n                                delay: feature.id * 0.1 + 0.75,\r\n                                repeatDelay: 0.3\r\n                              }}\r\n                            />\r\n\r\n                            {/* Bottom-left corner pulse */}\r\n                            <motion.div\r\n                              className=\"absolute -bottom-1 -left-1 w-1/2 h-1/2 rounded-tr-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]\"\r\n                              animate={{\r\n                                scale: [0, 1.5],\r\n                                opacity: [0.8, 0]\r\n                              }}\r\n                              transition={{\r\n                                duration: 3,\r\n                                repeat: Infinity,\r\n                                repeatType: \"loop\",\r\n                                ease: \"easeOut\",\r\n                                delay: feature.id * 0.1 + 1.5,\r\n                                repeatDelay: 0.3\r\n                              }}\r\n                            />\r\n\r\n                            {/* Bottom-right corner pulse */}\r\n                            <motion.div\r\n                              className=\"absolute -bottom-1 -right-1 w-1/2 h-1/2 rounded-tl-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]\"\r\n                              animate={{\r\n                                scale: [0, 1.5],\r\n                                opacity: [0.8, 0]\r\n                              }}\r\n                              transition={{\r\n                                duration: 3,\r\n                                repeat: Infinity,\r\n                                repeatType: \"loop\",\r\n                                ease: \"easeOut\",\r\n                                delay: feature.id * 0.1 + 2.25,\r\n                                repeatDelay: 0.3\r\n                              }}\r\n                            />\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent\r\n                    side=\"top\"\r\n                    className=\"max-w-[200px] z-50 bg-white/80 dark:bg-neutral-800/90 backdrop-blur-sm border border-[var(--brand-gold)]/30\"\r\n                  >\r\n                    <div>\r\n                      <p className=\"font-semibold text-[var(--brand-gold)]\">\r\n                        {feature.label}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        {feature.description}\r\n                      </p>\r\n                    </div>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              );\r\n            })\r\n          : null}\r\n      </TooltipProvider>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;;;AAzBA;;;;;AAoCe,SAAS,wBAAwB,EAAE,UAAU,KAAK,EAAgC;;IAC/F,6BAA6B;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,YAAY;QACd;4CAAG,EAAE;IAEL,8DAA8D;IAC9D,MAAM,WAAW;QACf,2CAA2C;QAC3C;YACE,IAAI;YACJ,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,KAAK,UAAU,OAAO;YACtB,MAAM,UAAU,QAAQ;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,QAAQ,UAAU,QAAQ;YAC1B,MAAM,UAAU,QAAQ;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,uNAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;YACb,QAAQ,UAAU,QAAQ;YAC1B,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,QAAQ,UAAU,QAAQ;YAC1B,MAAM,UAAU,QAAQ;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QAEA,sCAAsC;QACtC;YACE,IAAI;YACJ,MAAM,+MAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,MAAM,UAAU,OAAO;YACvB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,QAAQ,UAAU,QAAQ;YAC1B,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,MAAM,UAAU,QAAQ;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,iNAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,QAAQ,UAAU,QAAQ;YAC1B,MAAM,UAAU,QAAQ;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,MAAM,UAAU,OAAO;YACvB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,MAAM,UAAU,QAAQ;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,QAAQ,UAAU,QAAQ;YAC1B,OAAO,UAAU,OAAO;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QAEA;YACE,IAAI;YACJ,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,QAAQ,UAAU,QAAQ;YAC1B,MAAM,UAAU,QAAQ;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,KAAK,UAAU,QAAQ;YACvB,OAAO,UAAU,QAAQ;YACzB,OAAO;YACP,MAAM;QACR;KACD;IAED,+EAA+E;IAC/E,MAAM,kBAAkB,UACpB,SAAS,MAAM,CAAC,CAAA,IAAK;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;SAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,KACzE;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,+HAAA,CAAA,kBAAe;sBACb,WACG,gBAAgB,GAAG,CAAC,CAAC;gBACnB,yDAAyD;gBACzD,MAAM,kBAAkB,AAAC,QAAQ,EAAE,GAAG,MAAO;gBAC7C,MAAM,gBAAqC;oBACzC,GAAI,QAAQ,GAAG,KAAK,YAAY;wBAAE,KAAK,QAAQ,GAAG;oBAAC,IAAI,CAAC,CAAC;oBACzD,GAAI,QAAQ,KAAK,KAAK,YAAY;wBAAE,OAAO,QAAQ,KAAK;oBAAC,IAAI,CAAC,CAAC;oBAC/D,GAAI,QAAQ,MAAM,KAAK,YACnB;wBAAE,QAAQ,QAAQ,MAAM;oBAAC,IACzB,CAAC,CAAC;oBACN,GAAI,QAAQ,IAAI,KAAK,YAAY;wBAAE,MAAM,QAAQ,IAAI;oBAAC,IAAI,CAAC,CAAC;gBAC9D;gBAEA,qBACE,6LAAC,+HAAA,CAAA,UAAO;;sCACN,6LAAC,+HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;gCACP,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCACV,MAAM;oCACN,WAAW;oCACX,SAAS;oCACT,OAAO,QAAQ,KAAK;gCACtB;gCACA,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAW,CAAC,wDAAwD,EAClE,QAAQ,IAAI,KAAK,SACb,0BACA,QAAQ,IAAI,KAAK,UACf,0BACA,0BACP,CAAC,EAAE,QAAQ,IAAI,KAAK,SAAS,uCAAuC,gCAAgC;wCACrG,OAAO;4CACL,WAAW;wCACb;wCACA,YAAY;4CACV,WAAW;4CACX,OAAO;wCACT;wCACA,SAAS;4CACP,+BAA+B;4CAC/B,GAAG;gDACD;gDACA,QAAQ,EAAE,GAAG,MAAM,IAAI,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG;gDACtD,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,QAAQ,EAAE,GAAG;gDACtD;6CACD;4CACD,GAAG;gDACD;gDACA,QAAQ,EAAE,GAAG,MAAM,IAAI,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG;gDACtD,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,QAAQ,EAAE,GAAG;gDACtD;6CACD;4CACD,OAAO;gDAAC;gDAAG;gDAAM;gDAAM;6CAAE;4CACzB,SAAS;gDAAC;gDAAK;gDAAG;gDAAM;6CAAI;4CAC5B,WAAW;gDACT;gDACA;gDACA;gDACA;6CACD;wCACH;wCACA,YAAY;4CACV,6CAA6C;4CAC7C,UAAU,IAAK,QAAQ,EAAE,GAAG;4CAC5B,OAAO;4CACP,QAAQ;4CACR,YAAY;4CACZ,MAAM;4CACN,OAAO;gDAAC;gDAAG;gDAAK;gDAAK;6CAAE;wCACzB;kDAEC,cAAA,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ,IAAI,EAAE;4CACjC,WAAW,GACT,QAAQ,IAAI,KAAK,SACb,0BACA,QAAQ,IAAI,KAAK,UACf,kCACA,wBACP,yBAAyB,CAAC;4CAC3B,aAAa,QAAQ,IAAI,KAAK,SAAS,IAAI;wCAC7C;;;;;;kDAIF,6LAAC;wCAAI,WAAU;kDAEZ,QAAQ,IAAI,KAAK,SAChB,gCAAgC;sDAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;gDAClB,SAAS;oDAAC;oDAAK;oDAAK;iDAAI;4CAC1B;4CACA,YAAY;gDACV,UAAU,IAAK,QAAQ,EAAE,GAAG;gDAC5B,QAAQ;gDACR,YAAY;gDACZ,MAAM;gDACN,OAAO,QAAQ,EAAE,GAAG;4CACtB;;;;;mDAEA,QAAQ,IAAI,KAAK,UACnB,gCAAgC;sDAChC;;8DAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;yDAAI;wDACf,SAAS;4DAAC;4DAAK;yDAAE;oDACnB;oDACA,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,YAAY;wDACZ,MAAM;wDACN,OAAO,QAAQ,EAAE,GAAG;wDACpB,aAAa;oDACf;;;;;;8DAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;yDAAI;wDACf,SAAS;4DAAC;4DAAK;yDAAE;oDACnB;oDACA,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,YAAY;wDACZ,MAAM;wDACN,OAAO,QAAQ,EAAE,GAAG,MAAM;wDAC1B,aAAa;oDACf;;;;;;;2DAIJ,0CAA0C;sDAC1C;;8DAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;yDAAI;wDACf,SAAS;4DAAC;4DAAK;yDAAE;oDACnB;oDACA,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,YAAY;wDACZ,MAAM;wDACN,OAAO,QAAQ,EAAE,GAAG;wDACpB,aAAa;oDACf;;;;;;8DAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;yDAAI;wDACf,SAAS;4DAAC;4DAAK;yDAAE;oDACnB;oDACA,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,YAAY;wDACZ,MAAM;wDACN,OAAO,QAAQ,EAAE,GAAG,MAAM;wDAC1B,aAAa;oDACf;;;;;;8DAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;yDAAI;wDACf,SAAS;4DAAC;4DAAK;yDAAE;oDACnB;oDACA,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,YAAY;wDACZ,MAAM;wDACN,OAAO,QAAQ,EAAE,GAAG,MAAM;wDAC1B,aAAa;oDACf;;;;;;8DAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;yDAAI;wDACf,SAAS;4DAAC;4DAAK;yDAAE;oDACnB;oDACA,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,YAAY;wDACZ,MAAM;wDACN,OAAO,QAAQ,EAAE,GAAG,MAAM;wDAC1B,aAAa;oDACf;;;;;;;;;;;;;;;;;;;;;;;;sCAOZ,6LAAC,+HAAA,CAAA,iBAAc;4BACb,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,QAAQ,KAAK;;;;;;kDAEhB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;mBArNd,QAAQ,EAAE;;;;;YA2N5B,KACA;;;;;;;;;;;AAIZ;GApcwB;KAAA", "debugId": null}}, {"offset": {"line": 6315, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/EnhancedMetricsDisplay.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport { motion, useAnimation } from \"framer-motion\";\r\nimport { Heart, Users, Star, Eye } from \"lucide-react\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\n\r\ninterface EnhancedMetricsDisplayProps {\r\n  likes: number;\r\n  subscribers: number;\r\n  rating: number;\r\n  views?: number;\r\n  minimal?: boolean;\r\n}\r\n\r\nexport default function EnhancedMetricsDisplay({\r\n  likes,\r\n  subscribers,\r\n  rating,\r\n  views = 125000,\r\n  minimal = false,\r\n}: EnhancedMetricsDisplayProps) {\r\n  // State for animated counters\r\n  const [animatedLikes, setAnimatedLikes] = useState(0);\r\n  const [animatedSubscribers, setAnimatedSubscribers] = useState(0);\r\n  const [animatedViews, setAnimatedViews] = useState(0);\r\n  const [animatedRating, setAnimatedRating] = useState(0);\r\n  const [hasAnimated, setHasAnimated] = useState(false);\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Intersection Observer setup\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const controls = useAnimation();\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Format large numbers with Indian number system (lakhs, crores, etc.)\r\n  const formatNumber = (num: number) => {\r\n    return formatIndianNumberShort(num);\r\n  };\r\n\r\n  // Function to animate counters - wrapped in useCallback to prevent recreation on each render\r\n  const animateCounters = useCallback(() => {\r\n    const duration = 2000; // 2 seconds\r\n    const framesPerSecond = 60;\r\n    const totalFrames = (duration / 1000) * framesPerSecond;\r\n\r\n    let frame = 0;\r\n    const timer = setInterval(() => {\r\n      const progress = frame / totalFrames;\r\n      const easeOutProgress = 1 - Math.pow(1 - progress, 3); // Cubic ease out\r\n\r\n      setAnimatedLikes(Math.floor(likes * easeOutProgress));\r\n      setAnimatedSubscribers(Math.floor(subscribers * easeOutProgress));\r\n      setAnimatedViews(Math.floor(views * easeOutProgress));\r\n      setAnimatedRating(parseFloat((rating * easeOutProgress).toFixed(1)));\r\n\r\n      frame++;\r\n      if (frame > totalFrames) {\r\n        clearInterval(timer);\r\n        setAnimatedLikes(likes);\r\n        setAnimatedSubscribers(subscribers);\r\n        setAnimatedViews(views);\r\n        setAnimatedRating(rating);\r\n      }\r\n    }, 1000 / framesPerSecond);\r\n\r\n    return timer;\r\n  }, [likes, subscribers, views, rating]);\r\n\r\n  // Setup intersection observer to detect when component is fully in view\r\n  useEffect(() => {\r\n    if (!containerRef.current || !isClient) return;\r\n\r\n    const currentRef = containerRef.current; // Store ref in a variable for cleanup\r\n\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        // Trigger animation when element is at least 50% in view\r\n        if (entry.isIntersecting && entry.intersectionRatio >= 0.5) {\r\n          controls.start(\"visible\");\r\n\r\n          if (!hasAnimated) {\r\n            animateCounters();\r\n            setHasAnimated(true);\r\n          }\r\n        }\r\n      },\r\n      { threshold: 0.5 } // 0.5 means 50% of the element must be in view\r\n    );\r\n\r\n    observer.observe(currentRef);\r\n\r\n    return () => {\r\n      observer.unobserve(currentRef);\r\n    };\r\n  }, [controls, hasAnimated, animateCounters, isClient]);\r\n\r\n  if (!isClient) {\r\n    return <div className=\"h-24\" />; // Placeholder height to prevent layout shift\r\n  }\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className=\"relative grid grid-cols-2 sm:grid-cols-4 gap-3 w-full max-w-4xl mx-auto py-6 px-4\"\r\n    >\r\n      {/* Background glow effect */}\r\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\r\n        <motion.div\r\n          className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[80%] h-[80%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-[50px]\"\r\n          animate={{\r\n            scale: [1, 1.1, 1],\r\n            opacity: [0.3, 0.5, 0.3],\r\n          }}\r\n          transition={{\r\n            duration: 8,\r\n            repeat: Infinity,\r\n            repeatType: \"reverse\",\r\n            ease: \"easeInOut\",\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      <EnhancedMetricCard\r\n        icon={<Heart className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]\" />}\r\n        label=\"Likes\"\r\n        value={formatNumber(animatedLikes)}\r\n        controls={controls}\r\n        delay={0}\r\n        minimal={minimal}\r\n      />\r\n\r\n      <EnhancedMetricCard\r\n        icon={<Users className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]\" />}\r\n        label=\"Subscribers\"\r\n        value={formatNumber(animatedSubscribers)}\r\n        controls={controls}\r\n        delay={0.1}\r\n        minimal={minimal}\r\n      />\r\n\r\n      <EnhancedMetricCard\r\n        icon={<Star className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]\" />}\r\n        label=\"Rating\"\r\n        value={animatedRating.toFixed(1)}\r\n        controls={controls}\r\n        delay={0.2}\r\n        minimal={minimal}\r\n      />\r\n\r\n      <EnhancedMetricCard\r\n        icon={<Eye className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]\" />}\r\n        label=\"Views\"\r\n        value={formatNumber(animatedViews)}\r\n        controls={controls}\r\n        delay={0.3}\r\n        minimal={minimal}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface EnhancedMetricCardProps {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  value: string;\r\n  controls: ReturnType<typeof useAnimation>;\r\n  delay: number;\r\n  minimal?: boolean;\r\n}\r\n\r\nfunction EnhancedMetricCard({\r\n  icon,\r\n  label,\r\n  value,\r\n  controls,\r\n  delay,\r\n  minimal: _minimal = false\r\n}: EnhancedMetricCardProps) {\r\n  return (\r\n    <motion.div\r\n      className=\"relative rounded-lg p-2 sm:p-3 md:p-4 bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-[var(--brand-gold)]/20 flex flex-col items-center justify-center text-center shadow-sm overflow-hidden\"\r\n      variants={{\r\n        hidden: { opacity: 0, y: 10 },\r\n        visible: {\r\n          opacity: 1,\r\n          y: 0,\r\n          transition: {\r\n            duration: 0.3,\r\n            delay: delay,\r\n          },\r\n        },\r\n      }}\r\n      initial=\"hidden\"\r\n      animate={controls}\r\n      whileHover={{\r\n        scale: 1.05,\r\n        boxShadow:\r\n          \"0 0 15px rgba(var(--brand-gold-rgb), 0.2), 0 0 5px rgba(var(--brand-gold-rgb), 0.1)\",\r\n        transition: { duration: 0.2 },\r\n      }}\r\n    >\r\n      {/* Shimmer effect */}\r\n      <motion.div\r\n        className=\"absolute inset-0 w-full h-full\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <motion.div\r\n          className=\"absolute top-0 left-0 w-[200%] h-full\"\r\n          style={{\r\n            background: \"linear-gradient(90deg, transparent 0%, rgba(var(--brand-gold-rgb), 0.1) 50%, transparent 100%)\",\r\n          }}\r\n          initial={{ x: \"-100%\" }}\r\n          animate={{ x: \"100%\" }}\r\n          transition={{\r\n            duration: 2,\r\n            repeat: Infinity,\r\n            repeatType: \"loop\",\r\n            repeatDelay: 5,\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"mb-1 sm:mb-2 text-[var(--brand-gold)] opacity-90\"\r\n        style={{\r\n          filter:\r\n            \"drop-shadow(0 0 2px rgba(var(--brand-gold-rgb), 0.3)) drop-shadow(0 0 1px rgba(var(--brand-gold-rgb), 0.2))\",\r\n        }}\r\n        animate={{\r\n          scale: [1, 1.1, 1],\r\n          filter: [\r\n            \"drop-shadow(0 0 2px rgba(var(--brand-gold-rgb), 0.3))\",\r\n            \"drop-shadow(0 0 4px rgba(var(--brand-gold-rgb), 0.5))\",\r\n            \"drop-shadow(0 0 2px rgba(var(--brand-gold-rgb), 0.3))\"\r\n          ]\r\n        }}\r\n        transition={{\r\n          duration: 2,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n          delay: delay + 1\r\n        }}\r\n      >\r\n        {icon}\r\n      </motion.div>\r\n\r\n      <div className=\"text-base sm:text-xl md:text-2xl font-bold text-[var(--brand-gold)] mb-1 glow-text dark:glow-text-stronger\">\r\n        {value}\r\n      </div>\r\n\r\n      <div className=\"text-[10px] sm:text-xs text-neutral-600 dark:text-neutral-400\">\r\n        {label}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAee,SAAS,uBAAuB,EAC7C,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,MAAM,EACd,UAAU,KAAK,EACa;;IAC5B,8BAA8B;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,8BAA8B;IAC9B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,YAAY;QACd;2CAAG,EAAE;IAEL,uEAAuE;IACvE,MAAM,eAAe,CAAC;QACpB,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;IACjC;IAEA,6FAA6F;IAC7F,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YAClC,MAAM,WAAW,MAAM,YAAY;YACnC,MAAM,kBAAkB;YACxB,MAAM,cAAc,AAAC,WAAW,OAAQ;YAExC,IAAI,QAAQ;YACZ,MAAM,QAAQ;6EAAY;oBACxB,MAAM,WAAW,QAAQ;oBACzB,MAAM,kBAAkB,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU,IAAI,iBAAiB;oBAExE,iBAAiB,KAAK,KAAK,CAAC,QAAQ;oBACpC,uBAAuB,KAAK,KAAK,CAAC,cAAc;oBAChD,iBAAiB,KAAK,KAAK,CAAC,QAAQ;oBACpC,kBAAkB,WAAW,CAAC,SAAS,eAAe,EAAE,OAAO,CAAC;oBAEhE;oBACA,IAAI,QAAQ,aAAa;wBACvB,cAAc;wBACd,iBAAiB;wBACjB,uBAAuB;wBACvB,iBAAiB;wBACjB,kBAAkB;oBACpB;gBACF;4EAAG,OAAO;YAEV,OAAO;QACT;8DAAG;QAAC;QAAO;QAAa;QAAO;KAAO;IAEtC,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,UAAU;YAExC,MAAM,aAAa,aAAa,OAAO,EAAE,sCAAsC;YAE/E,MAAM,WAAW,IAAI;oDACnB,CAAC,CAAC,MAAM;oBACN,yDAAyD;oBACzD,IAAI,MAAM,cAAc,IAAI,MAAM,iBAAiB,IAAI,KAAK;wBAC1D,SAAS,KAAK,CAAC;wBAEf,IAAI,CAAC,aAAa;4BAChB;4BACA,eAAe;wBACjB;oBACF;gBACF;mDACA;gBAAE,WAAW;YAAI,EAAE,+CAA+C;;YAGpE,SAAS,OAAO,CAAC;YAEjB;oDAAO;oBACL,SAAS,SAAS,CAAC;gBACrB;;QACF;2CAAG;QAAC;QAAU;QAAa;QAAiB;KAAS;IAErD,IAAI,CAAC,UAAU;QACb,qBAAO,6LAAC;YAAI,WAAU;;;;;kBAAW,6CAA6C;IAChF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAK;yBAAI;oBAC1B;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,YAAY;wBACZ,MAAM;oBACR;;;;;;;;;;;0BAIJ,6LAAC;gBACC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAM;gBACN,OAAO,aAAa;gBACpB,UAAU;gBACV,OAAO;gBACP,SAAS;;;;;;0BAGX,6LAAC;gBACC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAM;gBACN,OAAO,aAAa;gBACpB,UAAU;gBACV,OAAO;gBACP,SAAS;;;;;;0BAGX,6LAAC;gBACC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBACtB,OAAM;gBACN,OAAO,eAAe,OAAO,CAAC;gBAC9B,UAAU;gBACV,OAAO;gBACP,SAAS;;;;;;0BAGX,6LAAC;gBACC,oBAAM,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;gBACrB,OAAM;gBACN,OAAO,aAAa;gBACpB,UAAU;gBACV,OAAO;gBACP,SAAS;;;;;;;;;;;;AAIjB;GApJwB;;QAiBL,4LAAA,CAAA,eAAY;;;KAjBP;AA+JxB,SAAS,mBAAmB,EAC1B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,QAAQ,EACR,KAAK,EACL,SAAS,WAAW,KAAK,EACD;IACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;YACR,QAAQ;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC5B,SAAS;gBACP,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,UAAU;oBACV,OAAO;gBACT;YACF;QACF;QACA,SAAQ;QACR,SAAS;QACT,YAAY;YACV,OAAO;YACP,WACE;YACF,YAAY;gBAAE,UAAU;YAAI;QAC9B;;0BAGA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,YAAY;oBACd;oBACA,SAAS;wBAAE,GAAG;oBAAQ;oBACtB,SAAS;wBAAE,GAAG;oBAAO;oBACrB,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,YAAY;wBACZ,aAAa;wBACb,MAAM;oBACR;;;;;;;;;;;0BAIJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,QACE;gBACJ;gBACA,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,QAAQ;wBACN;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;oBACN,OAAO,QAAQ;gBACjB;0BAEC;;;;;;0BAGH,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAGH,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;MAzFS", "debugId": null}}, {"offset": {"line": 6683, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/EnhancedMetricsContainer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport EnhancedMetricsDisplay from \"./EnhancedMetricsDisplay\";\r\n\r\ninterface EnhancedMetricsContainerProps {\r\n  likes: number;\r\n  subscribers: number;\r\n  rating: number;\r\n  views?: number;\r\n  minimal?: boolean;\r\n}\r\n\r\nexport default function EnhancedMetricsContainer({\r\n  likes,\r\n  subscribers,\r\n  rating,\r\n  views,\r\n  minimal = false,\r\n}: EnhancedMetricsContainerProps) {\r\n  return (\r\n    <motion.section\r\n      className=\"relative w-full py-4 overflow-hidden\"\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      {/* Background elements */}\r\n      <div className=\"absolute inset-0 -z-10 overflow-hidden pointer-events-none\">\r\n        {/* Subtle pattern overlay */}\r\n        <div className=\"absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-5 dark:opacity-10\"></div>\r\n      </div>\r\n      \r\n      {/* Enhanced metrics display */}\r\n      <div className=\"relative w-full px-2 sm:px-4\">\r\n        <EnhancedMetricsDisplay\r\n          likes={likes}\r\n          subscribers={subscribers}\r\n          rating={rating}\r\n          views={views}\r\n          minimal={minimal}\r\n        />\r\n      </div>\r\n    </motion.section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS,yBAAyB,EAC/C,KAAK,EACL,WAAW,EACX,MAAM,EACN,KAAK,EACL,UAAU,KAAK,EACe;IAC9B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sKAAA,CAAA,UAAsB;oBACrB,OAAO;oBACP,aAAa;oBACb,QAAQ;oBACR,OAAO;oBACP,SAAS;;;;;;;;;;;;;;;;;AAKnB;KAhCwB", "debugId": null}}, {"offset": {"line": 6757, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/schema.ts"], "sourcesContent": ["import * as z from \"zod\";\r\nimport { IndianMobileSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Regular expression for validating hex color codes (e.g., #RRGGBB, #RGB)\r\n// const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/; // Removed as theme_color is removed\r\n\r\n// Zod schema for business card data validation (Phase 1)\r\nexport const businessCardSchema = z.object({\r\n  // Optional fields first\r\n  logo_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for logo/profile photo.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .nullable(), // Allow empty string, null, or valid URL\r\n  established_year: z\r\n    .number()\r\n    .int({ message: \"Established year must be a whole number.\" })\r\n    .min(1800, { message: \"Established year must be after 1800.\" })\r\n    .max(new Date().getFullYear(), { message: \"Established year cannot be in the future.\" })\r\n    .optional()\r\n    .nullable(),\r\n  // Address broken down - NOW REQUIRED (from onboarding)\r\n  address_line: z\r\n    .string()\r\n    .min(1, { message: \"Address line is required.\" })\r\n    .max(100, { message: \"Address line cannot exceed 100 characters.\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality/area is required.\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required.\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required.\" }),\r\n  pincode: z\r\n    .string()\r\n    .min(6, { message: \"Pincode must be 6 digits.\" })\r\n    .max(6, { message: \"Pincode must be 6 digits.\" })\r\n    .regex(/^\\d+$/, { message: \"Pincode must contain only digits.\" }),\r\n  phone: IndianMobileSchema, // Primary display phone - NOW REQUIRED\r\n  // timing_info removed\r\n  // delivery_info removed\r\n  // website_url removed\r\n  instagram_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Instagram.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  facebook_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Facebook.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // linkedin_url removed\r\n  // twitter_url removed\r\n  // youtube_url removed\r\n  whatsapp_number: IndianMobileSchema // For wa.me link\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // call_number removed\r\n  about_bio: z\r\n    .string()\r\n    .max(100, { message: \"Bio cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  theme_color: z // Added for Growth plan\r\n    .string()\r\n    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {\r\n      message: \"Invalid hex color format (e.g., #RRGGBB or #RGB).\",\r\n    })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // card_texture field removed as it doesn't exist in the database\r\n  business_hours: z.any().optional().nullable(), // Added for Growth plan - Using z.any() for now, refine if specific structure needed\r\n  delivery_info: z // Added for Growth plan\r\n    .string()\r\n    .max(100, { message: \"Delivery info cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  business_category: z\r\n    .string()\r\n    .min(1, { message: \"Business category is required.\" }),\r\n  \r\n  status: z.enum([\"online\", \"offline\"]).default(\"offline\"),\r\n  // Custom branding fields for Pro/Enterprise users\r\n  custom_branding: z.object({\r\n    custom_header_text: z.string().max(50).optional().or(z.literal(\"\")),\r\n    custom_header_image_url: z.string().url().optional().or(z.literal(\"\")), // Legacy field\r\n    custom_header_image_light_url: z.string().url().optional().or(z.literal(\"\")), // Light theme\r\n    custom_header_image_dark_url: z.string().url().optional().or(z.literal(\"\")), // Dark theme\r\n    hide_dukancard_branding: z.boolean().optional(),\r\n    // File objects for pending uploads (not saved to database)\r\n    pending_light_header_file: z.any().optional(), // File object for light theme\r\n    pending_dark_header_file: z.any().optional(), // File object for dark theme\r\n  }).optional()\r\n  .refine((data) => {\r\n    // Only require custom_header_text OR any header image if hide_dukancard_branding is explicitly true\r\n    if (data?.hide_dukancard_branding === true) {\r\n      const hasText = data?.custom_header_text && data.custom_header_text.trim() !== \"\";\r\n      const hasLegacyImage = data?.custom_header_image_url && data.custom_header_image_url.trim() !== \"\";\r\n      const hasLightImage = data?.custom_header_image_light_url && data.custom_header_image_light_url.trim() !== \"\";\r\n      const hasDarkImage = data?.custom_header_image_dark_url && data.custom_header_image_dark_url.trim() !== \"\";\r\n\r\n      if (!hasText && !hasLegacyImage && !hasLightImage && !hasDarkImage) {\r\n        return false;\r\n      }\r\n    }\r\n    return true;\r\n  }, {\r\n    message: \"Custom header text or image is required when hiding Dukancard branding\",\r\n    path: [\"custom_header_text\"]\r\n  }),\r\n  // Custom ads for Pro/Enterprise users\r\n  custom_ads: z.object({\r\n    enabled: z.boolean().optional(),\r\n    image_url: z.string().url().optional().or(z.literal(\"\")),\r\n    link_url: z.string().url().optional().or(z.literal(\"\")),\r\n    uploaded_at: z.string().optional().or(z.literal(\"\")).nullable(),\r\n  }).optional(),\r\n  business_slug: z\r\n    .string()\r\n    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {\r\n      message:\r\n        \"Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen.\",\r\n    })\r\n    .min(3, { message: \"Slug must be at least 3 characters long.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n\r\n  // Required fields\r\n  member_name: z\r\n    .string()\r\n    .min(1, { message: \"Member name is required.\" })\r\n    .max(50, { message: \"Name cannot exceed 50 characters.\" }),\r\n  title: z\r\n    .string()\r\n    .min(1, { message: \"Title/Designation is required.\" })\r\n    .max(50, { message: \"Title cannot exceed 50 characters.\" }),\r\n  business_name: z\r\n    .string()\r\n    .min(1, { message: \"Business name is required.\" })\r\n    .max(100, { message: \"Business name cannot exceed 100 characters.\" }),\r\n\r\n  // Read-only/managed fields (keep for type safety if needed)\r\n  id: z.string().uuid().optional(),\r\n  contact_email: z.string().email({ message: \"Please enter a valid email address\" }).min(1, { message: \"Contact email is required\" }),\r\n  has_active_subscription: z.boolean().optional(),\r\n  trial_end_date: z.string().optional().nullable(), // Database returns string, not Date\r\n  created_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n  updated_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n\r\n  // Interaction fields (added in Phase 2) - make optional as they might not always be fetched\r\n  total_likes: z.number().int().nonnegative().optional(),\r\n  total_subscriptions: z.number().int().nonnegative().optional(),\r\n  average_rating: z.number().nonnegative().optional(),\r\n  total_visits: z.number().int().nonnegative().optional(),\r\n});\r\n\r\n// TypeScript type inferred from the Zod schema\r\nexport type BusinessCardData = z.infer<typeof businessCardSchema>;\r\n\r\n// Default values for initializing the form or preview (Phase 1)\r\nexport const defaultBusinessCardData: Partial<BusinessCardData> = {\r\n  member_name: \"\",\r\n  title: \"\",\r\n  business_name: \"\",\r\n  logo_url: null,\r\n  established_year: null,\r\n  address_line: \"\",\r\n  locality: \"\",\r\n  city: \"\",\r\n  state: \"\",\r\n  pincode: \"\",\r\n  phone: \"\",\r\n  instagram_url: \"\",\r\n  facebook_url: \"\",\r\n  whatsapp_number: \"\",\r\n  about_bio: \"\",\r\n  theme_color: \"\",\r\n  business_hours: null,\r\n  delivery_info: \"\",\r\n  business_category: \"\",\r\n  \r\n  status: \"offline\",\r\n  business_slug: \"\",\r\n  contact_email: \"\", // Added contact_email field\r\n  custom_branding: {\r\n    custom_header_text: \"\",\r\n    custom_header_image_url: \"\", // Legacy field\r\n    custom_header_image_light_url: \"\", // Light theme\r\n    custom_header_image_dark_url: \"\", // Dark theme\r\n    hide_dukancard_branding: false,\r\n    pending_light_header_file: null, // File object for light theme\r\n    pending_dark_header_file: null, // File object for dark theme\r\n  },\r\n  custom_ads: {\r\n    enabled: false,\r\n    image_url: \"\",\r\n    link_url: \"\",\r\n    uploaded_at: null,\r\n  },\r\n};\r\n\r\n// Define which fields are strictly required to go online\r\nexport const requiredFieldsForOnline: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\",\r\n  \"contact_email\", // Added contact_email as required for online status\r\n  \"business_category\" // Added business_category as required for online status\r\n];\r\n\r\n// Define which fields are required for saving regardless of status (all onboarding fields except plan)\r\nexport const requiredFieldsForSaving: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"contact_email\",\r\n  \"business_category\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\"\r\n];\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAMO,MAAM,qBAAqB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;IACzC,wBAAwB;IACxB,UAAU,CAAA,GAAA,uIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA6C,GAC5D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE,KACb,QAAQ;IACX,kBAAkB,CAAA,GAAA,uIAAA,CAAA,SACT,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA2C,GAC1D,GAAG,CAAC,MAAM;QAAE,SAAS;IAAuC,GAC5D,GAAG,CAAC,IAAI,OAAO,WAAW,IAAI;QAAE,SAAS;IAA4C,GACrF,QAAQ,GACR,QAAQ;IACX,uDAAuD;IACvD,cAAc,CAAA,GAAA,uIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA6C;IACpE,UAAU,CAAA,GAAA,uIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;IAClD,MAAM,CAAA,GAAA,uIAAA,CAAA,SACG,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB;IACzC,OAAO,CAAA,GAAA,uIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAqB;IAC1C,SAAS,CAAA,GAAA,uIAAA,CAAA,SACA,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,KAAK,CAAC,SAAS;QAAE,SAAS;IAAoC;IACjE,OAAO,gIAAA,CAAA,qBAAkB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,sBAAsB;IACtB,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAoC,GACnD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,CAAA,GAAA,uIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAmC,GAClD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,iBAAiB,iIAAmB,iBAAiB;IAApC,CAAA,qBAAkB,CAChC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,sBAAsB;IACtB,WAAW,CAAA,GAAA,uIAAA,CAAA,SACF,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAAoC,GACxD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,aAAa,CAAA,GAAA,uIAAA,CAAA,SACJ,AAAD,IACL,KAAK,CAAC,sCAAsC;QAC3C,SAAS;IACX,GACC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,iEAAiE;IACjE,gBAAgB,CAAA,GAAA,uIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C,GAClE,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,mBAAmB,CAAA,GAAA,uIAAA,CAAA,SACV,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC;IAEtD,QAAQ,CAAA,GAAA,uIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAU;KAAU,EAAE,OAAO,CAAC;IAC9C,kDAAkD;IAClD,iBAAiB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;QACxB,oBAAoB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QAC/D,yBAAyB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QAClE,+BAA+B,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACxE,8BAA8B,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACvE,yBAAyB,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7C,2DAA2D;QAC3D,2BAA2B,CAAA,GAAA,uIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;QAC3C,0BAA0B,CAAA,GAAA,uIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;IAC5C,GAAG,QAAQ,GACV,MAAM,CAAC,CAAC;QACP,oGAAoG;QACpG,IAAI,MAAM,4BAA4B,MAAM;YAC1C,MAAM,UAAU,MAAM,sBAAsB,KAAK,kBAAkB,CAAC,IAAI,OAAO;YAC/E,MAAM,iBAAiB,MAAM,2BAA2B,KAAK,uBAAuB,CAAC,IAAI,OAAO;YAChG,MAAM,gBAAgB,MAAM,iCAAiC,KAAK,6BAA6B,CAAC,IAAI,OAAO;YAC3G,MAAM,eAAe,MAAM,gCAAgC,KAAK,4BAA4B,CAAC,IAAI,OAAO;YAExG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,cAAc;gBAClE,OAAO;YACT;QACF;QACA,OAAO;IACT,GAAG;QACD,SAAS;QACT,MAAM;YAAC;SAAqB;IAC9B;IACA,sCAAsC;IACtC,YAAY,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;QACnB,SAAS,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7B,WAAW,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACpD,UAAU,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACnD,aAAa,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE,KAAK,QAAQ;IAC/D,GAAG,QAAQ;IACX,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,KAAK,CAAC,8BAA8B;QACnC,SACE;IACJ,GACC,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAEhB,kBAAkB;IAClB,aAAa,CAAA,GAAA,uIAAA,CAAA,SACJ,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAC7C,GAAG,CAAC,IAAI;QAAE,SAAS;IAAoC;IAC1D,OAAO,CAAA,GAAA,uIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC,GACnD,GAAG,CAAC,IAAI;QAAE,SAAS;IAAqC;IAC3D,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B,GAC/C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C;IAErE,4DAA4D;IAC5D,IAAI,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,GAAG,QAAQ;IAC9B,eAAe,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAAqC,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B;IACjI,yBAAyB,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;IAC7C,gBAAgB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC9C,YAAY,CAAA,GAAA,uIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,uIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IACA,YAAY,CAAA,GAAA,uIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,uIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IAEA,4FAA4F;IAC5F,aAAa,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IACpD,qBAAqB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IAC5D,gBAAgB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,WAAW,GAAG,QAAQ;IACjD,cAAc,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;AACvD;AAMO,MAAM,0BAAqD;IAChE,aAAa;IACb,OAAO;IACP,eAAe;IACf,UAAU;IACV,kBAAkB;IAClB,cAAc;IACd,UAAU;IACV,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IAEnB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,iBAAiB;QACf,oBAAoB;QACpB,yBAAyB;QACzB,+BAA+B;QAC/B,8BAA8B;QAC9B,yBAAyB;QACzB,2BAA2B;QAC3B,0BAA0B;IAC5B;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,aAAa;IACf;AACF;AAGO,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB,wDAAwD;CAC7E;AAGM,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 6994, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/WhatsAppIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Directly use React.SVGProps for type safety without an empty interface\r\nconst WhatsAppIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    viewBox=\"0 0 24 24\"\r\n    fill=\"currentColor\" // Reverted to fill\r\n    {...props} // Spread any additional props like className, style, etc.\r\n  >\r\n    <path\r\n      d={\r\n        \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\"\r\n      }\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport default WhatsAppIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,yEAAyE;AACzE,MAAM,eAAwD,CAAC,sBAC7D,6LAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK,eAAe,mBAAmB;;QACtC,GAAG,KAAK;kBAET,cAAA,6LAAC;YACC,GACE;;;;;;;;;;;KATF;uCAeS", "debugId": null}}, {"offset": {"line": 7031, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/InstagramIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Use React.SVGProps directly instead of an empty interface\r\nconst InstagramIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 24 24\"\r\n      fill=\"currentColor\"\r\n      {...props} // Pass className and other props\r\n    >\r\n      <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default InstagramIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,4DAA4D;AAC5D,MAAM,gBAAyD,CAAC;IAC9D,qBACE,6LAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACJ,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;AAGd;KAXM;uCAaS", "debugId": null}}, {"offset": {"line": 7069, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/FacebookIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Use React.SVGProps directly instead of an empty interface\r\nconst FacebookIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 24 24\"\r\n      fill=\"currentColor\"\r\n      {...props} // Pass className and other props\r\n    >\r\n      <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default FacebookIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,4DAA4D;AAC5D,MAAM,eAAwD,CAAC;IAC7D,qBACE,6LAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACJ,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;AAGd;KAXM;uCAaS", "debugId": null}}, {"offset": {"line": 7107, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/utils/cardUtils.ts"], "sourcesContent": ["export const formatWhatsAppUrl = (number?: string): string | undefined => {\r\n  if (!number) return undefined;\r\n  const digits = number.replace(/\\D/g, \"\");\r\n  if (digits.length < 10) return undefined;\r\n  const formattedNumber = digits.startsWith(\"91\") ? digits : `91${digits}`;\r\n  return `https://wa.me/${formattedNumber}`;\r\n};\r\n\r\nexport const formatTelUrl = (number?: string): string | undefined => {\r\n  if (!number) return undefined;\r\n  const digits = number.replace(/\\D/g, \"\");\r\n  if (digits.length < 10) return undefined;\r\n  return `tel:+91${digits}`;\r\n};\r\n\r\nexport const formatPrice = (price: number | null | undefined): string => {\r\n  if (price === null || price === undefined) return \"N/A\";\r\n  return `₹${price.toLocaleString(\"en-IN\")}`;\r\n};\r\n\r\nexport const formatTimeTo12Hour = (time24: string): string => {\r\n  if (!time24 || time24.length < 5) return time24;\r\n\r\n  const [hourStr, minuteStr] = time24.split(\":\");\r\n  const hour = parseInt(hourStr, 10);\r\n\r\n  if (isNaN(hour)) return time24;\r\n\r\n  const period = hour >= 12 ? \"PM\" : \"AM\";\r\n  const hour12 = hour % 12 || 12;\r\n  return `${hour12}:${minuteStr} ${period}`;\r\n};\r\n\r\nexport const formatDayGroup = (days: string[]): string => {\r\n  const dayAbbreviations: Record<string, string> = {\r\n    monday: \"Mon\",\r\n    tuesday: \"Tue\",\r\n    wednesday: \"Wed\",\r\n    thursday: \"Thu\",\r\n    friday: \"Fri\",\r\n    saturday: \"Sat\",\r\n    sunday: \"Sun\",\r\n  };\r\n\r\n  const dayOrder = [\r\n    \"monday\",\r\n    \"tuesday\",\r\n    \"wednesday\",\r\n    \"thursday\",\r\n    \"friday\",\r\n    \"saturday\",\r\n    \"sunday\",\r\n  ];\r\n  const sortedDays = [...days].sort(\r\n    (a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b)\r\n  );\r\n\r\n  const shortDays = sortedDays.map((day) => dayAbbreviations[day] || day);\r\n\r\n  const weekdays = [\"monday\", \"tuesday\", \"wednesday\", \"thursday\", \"friday\"];\r\n  if (\r\n    sortedDays.length === 5 &&\r\n    weekdays.every((day) => sortedDays.includes(day))\r\n  ) {\r\n    return \"Mon-Fri\";\r\n  }\r\n\r\n  if (\r\n    sortedDays.length === 2 &&\r\n    sortedDays.includes(\"saturday\") &&\r\n    sortedDays.includes(\"sunday\")\r\n  ) {\r\n    return \"Sat-Sun\";\r\n  }\r\n\r\n  if (sortedDays.length === 7) {\r\n    return \"All days\";\r\n  }\r\n\r\n  if (isConsecutive(sortedDays, dayOrder)) {\r\n    return `${shortDays[0]}-${shortDays[shortDays.length - 1]}`;\r\n  }\r\n\r\n  return shortDays.join(\", \");\r\n};\r\n\r\nexport const isConsecutive = (days: string[], dayOrder: string[]): boolean => {\r\n  if (days.length <= 1) return true;\r\n\r\n  const indices = days\r\n    .map((day) => dayOrder.indexOf(day))\r\n    .sort((a, b) => a - b);\r\n\r\n  for (let i = 1; i < indices.length; i++) {\r\n    if (indices[i] !== indices[i - 1] + 1) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\n// Generate card style with theme colors\r\nexport const generateCardStyle = (\r\n  finalThemeColor: string\r\n): React.CSSProperties => {\r\n  return {\r\n    \"--theme-color\": finalThemeColor,\r\n    \"--theme-color-80\": `${finalThemeColor}CC`,\r\n    \"--theme-color-50\": `${finalThemeColor}80`,\r\n    \"--theme-color-30\": `${finalThemeColor}4D`,\r\n    \"--theme-color-20\": `${finalThemeColor}33`,\r\n    \"--theme-color-10\": `${finalThemeColor}1A`,\r\n    \"--theme-color-5\": `${finalThemeColor}0D`,\r\n    \"--theme-accent-end\": \"#E5C76E\", // Less yellow accent\r\n  } as React.CSSProperties;\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,SAAS,OAAO,OAAO,CAAC,OAAO;IACrC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO;IAC/B,MAAM,kBAAkB,OAAO,UAAU,CAAC,QAAQ,SAAS,CAAC,EAAE,EAAE,QAAQ;IACxE,OAAO,CAAC,cAAc,EAAE,iBAAiB;AAC3C;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,SAAS,OAAO,OAAO,CAAC,OAAO;IACrC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO;IAC/B,OAAO,CAAC,OAAO,EAAE,QAAQ;AAC3B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,OAAO,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,UAAU;AAC5C;AAEO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG,OAAO;IAEzC,MAAM,CAAC,SAAS,UAAU,GAAG,OAAO,KAAK,CAAC;IAC1C,MAAM,OAAO,SAAS,SAAS;IAE/B,IAAI,MAAM,OAAO,OAAO;IAExB,MAAM,SAAS,QAAQ,KAAK,OAAO;IACnC,MAAM,SAAS,OAAO,MAAM;IAC5B,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,mBAA2C;QAC/C,QAAQ;QACR,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAC/B,CAAC,GAAG,IAAM,SAAS,OAAO,CAAC,KAAK,SAAS,OAAO,CAAC;IAGnD,MAAM,YAAY,WAAW,GAAG,CAAC,CAAC,MAAQ,gBAAgB,CAAC,IAAI,IAAI;IAEnE,MAAM,WAAW;QAAC;QAAU;QAAW;QAAa;QAAY;KAAS;IACzE,IACE,WAAW,MAAM,KAAK,KACtB,SAAS,KAAK,CAAC,CAAC,MAAQ,WAAW,QAAQ,CAAC,OAC5C;QACA,OAAO;IACT;IAEA,IACE,WAAW,MAAM,KAAK,KACtB,WAAW,QAAQ,CAAC,eACpB,WAAW,QAAQ,CAAC,WACpB;QACA,OAAO;IACT;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IAEA,IAAI,cAAc,YAAY,WAAW;QACvC,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,EAAE;IAC7D;IAEA,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,MAAM,gBAAgB,CAAC,MAAgB;IAC5C,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;IAE7B,MAAM,UAAU,KACb,GAAG,CAAC,CAAC,MAAQ,SAAS,OAAO,CAAC,MAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAC/B;IAEA,OAAO;QACL,iBAAiB;QACjB,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,mBAAmB,GAAG,gBAAgB,EAAE,CAAC;QACzC,sBAAsB;IACxB;AACF", "debugId": null}}, {"offset": {"line": 7217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardTextures.ts"], "sourcesContent": ["// Define available card textures\r\nexport type CardTexture = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  path: string;\r\n  category: \"paper\" | \"premium\" | \"modern\";\r\n  darkModeOpacity: number;\r\n  lightModeOpacity: number;\r\n  cssClass?: string; // Optional CSS class for static textures\r\n};\r\n\r\n// Array of available textures\r\nexport const cardTextures: CardTexture[] = [\r\n  {\r\n    id: \"linen-paper\",\r\n    name: \"Linen Paper\",\r\n    description: \"Classic linen texture with subtle cross-hatching\",\r\n    path: \"/textures/linen-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"cotton-paper\",\r\n    name: \"Cotton Paper\",\r\n    description: \"Soft, fibrous cotton paper texture\",\r\n    path: \"/textures/cotton-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"recycled-paper\",\r\n    name: \"Recycled Paper\",\r\n    description: \"Eco-friendly recycled paper with small flecks\",\r\n    path: \"/textures/recycled-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"laid-paper\",\r\n    name: \"Laid Paper\",\r\n    description: \"Traditional laid paper with horizontal lines\",\r\n    path: \"/textures/laid-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"marble\",\r\n    name: \"Marble\",\r\n    description: \"Elegant marble texture with subtle veining\",\r\n    path: \"/textures/marble.svg\",\r\n    category: \"premium\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"brushed-metal\",\r\n    name: \"Brushed Metal\",\r\n    description: \"Sophisticated brushed metal finish\",\r\n    path: \"/textures/brushed-metal.svg\",\r\n    category: \"premium\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"subtle-dots\",\r\n    name: \"Subtle Dots\",\r\n    description: \"Modern pattern with subtle dot grid\",\r\n    path: \"/textures/subtle-dots.svg\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"geometric\",\r\n    name: \"Geometric\",\r\n    description: \"Contemporary geometric pattern\",\r\n    path: \"/textures/geometric.svg\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"texture-png\",\r\n    name: \"Classic Texture\",\r\n    description: \"Original texture from the application\",\r\n    path: \"/texture.png\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"none\",\r\n    name: \"No Texture\",\r\n    description: \"Clean look without any texture\",\r\n    path: \"\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0,\r\n    lightModeOpacity: 0,\r\n  },\r\n];\r\n\r\n// Function to get a texture by ID\r\nexport const getTextureById = (id: string): CardTexture => {\r\n  return cardTextures.find((texture) => texture.id === id) || cardTextures[0];\r\n};\r\n\r\n// Default texture ID\r\nexport const DEFAULT_TEXTURE_ID = \"linen-paper\";\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;AAa1B,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;CACD;AAGM,MAAM,iBAAiB,CAAC;IAC7B,OAAO,aAAa,IAAI,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK,OAAO,YAAY,CAAC,EAAE;AAC7E;AAGO,MAAM,qBAAqB", "debugId": null}}, {"offset": {"line": 7328, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardBackgroundEffects.tsx"], "sourcesContent": ["import { getTextureById, DEFAULT_TEXTURE_ID } from \"./CardTextures\";\r\nimport { useMemo } from \"react\";\r\n\r\ninterface CardBackgroundEffectsProps {\r\n  finalThemeColor: string;\r\n}\r\n\r\nexport default function CardBackgroundEffects({ finalThemeColor: _finalThemeColor }: CardBackgroundEffectsProps) {\r\n  // Get texture details based on default texture ID\r\n  const textureDetails = useMemo(() => getTextureById(DEFAULT_TEXTURE_ID), []);\r\n\r\n  return (\r\n    <>\r\n      {/* Card background with subtle pattern */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none z-5\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/card-texture.svg\")`,\r\n          backgroundSize: \"cover\",\r\n          backgroundPosition: \"center\",\r\n          backgroundRepeat: \"no-repeat\",\r\n          opacity: 0.6,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Custom texture overlay with subtle theme compatibility */}\r\n      {textureDetails.path && (\r\n        <div\r\n          className=\"absolute inset-0 mix-blend-overlay pointer-events-none texture-background z-10 dark:opacity-[var(--dark-opacity)] opacity-[var(--light-opacity)]\"\r\n          style={\r\n            {\r\n              backgroundImage: textureDetails.path\r\n                ? `url(${textureDetails.path})`\r\n                : \"none\",\r\n              \"--dark-opacity\": `${textureDetails.darkModeOpacity * 0.7}`,\r\n              \"--light-opacity\": `${textureDetails.lightModeOpacity * 0.7}`,\r\n              backgroundSize: \"cover\",\r\n              backgroundPosition: \"center\",\r\n              backgroundRepeat: \"repeat\",\r\n            } as React.CSSProperties\r\n          }\r\n        ></div>\r\n      )}\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none z-10\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/subtle-pattern.svg\")`,\r\n          backgroundRepeat: \"repeat\",\r\n          backgroundSize: \"20px 20px\",\r\n          opacity: 0.15,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Subtle embossed effect overlay */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/20 mix-blend-overlay pointer-events-none z-15 opacity-20 dark:opacity-15\"></div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAMe,SAAS,sBAAsB,EAAE,iBAAiB,gBAAgB,EAA8B;;IAC7G,kDAAkD;IAClD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE,IAAM,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD,EAAE,sLAAA,CAAA,qBAAkB;wDAAG,EAAE;IAE3E,qBACE;;0BAEE,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,mCAAmC,CAAC;oBACtD,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;oBAClB,SAAS;gBACX;;;;;;YAID,eAAe,IAAI,kBAClB,6LAAC;gBACC,WAAU;gBACV,OACE;oBACE,iBAAiB,eAAe,IAAI,GAChC,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC,CAAC,CAAC,GAC7B;oBACJ,kBAAkB,GAAG,eAAe,eAAe,GAAG,KAAK;oBAC3D,mBAAmB,GAAG,eAAe,gBAAgB,GAAG,KAAK;oBAC7D,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;;;;;;0BAMN,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,qCAAqC,CAAC;oBACxD,kBAAkB;oBAClB,gBAAgB;oBAChB,SAAS;gBACX;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;GApDwB;KAAA", "debugId": null}}, {"offset": {"line": 7411, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardCornerDecorations.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\nexport default function CardCornerDecorations() {\r\n  return (\r\n    <>\r\n      {/* Subtle corner decorations */}\r\n      <div className=\"absolute top-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(0deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute top-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(90deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(180deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(270deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAe;8BACtC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAgB;8BACvC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAiB;8BACxC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAiB;8BACxC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;;;AAMtB;KAlDwB", "debugId": null}}, {"offset": {"line": 7544, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardGlowEffects.tsx"], "sourcesContent": ["export default function CardGlowEffects() {\r\n  return (\r\n    <>\r\n      {/* Subtle glow effects */}\r\n      <div className=\"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15\"></div>\r\n      <div className=\"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15\"></div>\r\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-[var(--theme-color-5)] blur-3xl opacity-15 dark:opacity-10\"></div>\r\n\r\n      {/* Enhanced inner glow effect with theme color */}\r\n      <div className=\"absolute inset-0 rounded-xl shadow-[inset_0_0_20px_rgba(0,0,0,0.1),inset_0_0_5px_var(--theme-color-20)] dark:shadow-[inset_0_0_20px_rgba(255,255,255,0.03),inset_0_0_5px_var(--theme-color-30)] pointer-events-none z-25\"></div>\r\n\r\n      {/* Vibrant edge glow effect */}\r\n      <div\r\n        className=\"absolute inset-x-0 bottom-0 h-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to top, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-y-0 right-0 w-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to left, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-x-0 top-0 h-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to bottom, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-y-0 left-0 w-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to right, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Enhanced edge effects to simulate premium card thickness */}\r\n      <div className=\"absolute inset-x-0 bottom-0 h-4 bg-gradient-to-t from-black/40 to-transparent pointer-events-none z-25\"></div>\r\n      <div className=\"absolute inset-y-0 right-0 w-4 bg-gradient-to-l from-black/40 to-transparent pointer-events-none z-25\"></div>\r\n\r\n      {/* Enhanced highlight on top edge for premium look */}\r\n      <div className=\"absolute inset-x-0 top-0 h-[4px] bg-gradient-to-b from-white/60 to-transparent pointer-events-none z-25\"></div>\r\n      <div className=\"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-white/60 to-transparent pointer-events-none z-25\"></div>\r\n\r\n      {/* Colorful edge highlight */}\r\n      <div className=\"absolute inset-x-0 bottom-0 h-1 bg-gradient-to-t from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70\"></div>\r\n      <div className=\"absolute inset-y-0 right-0 w-1 bg-gradient-to-l from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70\"></div>\r\n\r\n      {/* Enhanced shadow for depth with theme color influence */}\r\n      <div className=\"absolute inset-0 shadow-[0_15px_60px_rgba(0,0,0,0.3),0_5px_20px_var(--theme-color-20)] dark:shadow-[0_15px_60px_rgba(0,0,0,0.5),0_5px_20px_var(--theme-color-30)] rounded-xl pointer-events-none z-25\"></div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,wDAAwD,CAAC;oBACtE,SAAS;gBACX;;;;;;0BAEF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,yDAAyD,CAAC;oBACvE,SAAS;gBACX;;;;;;0BAEF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,2DAA2D,CAAC;oBACzE,SAAS;gBACX;;;;;;0BAEF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,0DAA0D,CAAC;oBACxE,SAAS;gBACX;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;KAzDwB", "debugId": null}}, {"offset": {"line": 7688, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardHeader.tsx"], "sourcesContent": ["import { Calendar } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport NextImage from \"next/image\";\r\nimport { getThemeSpecificHeaderImage, getBrandingText, hasCustomBrandingAccess, shouldShowDukancardBranding } from \"@/lib/utils/customBranding\";\r\n\r\ninterface CardHeaderProps {\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n  establishedYear?: number | null;\r\n  customBranding?: {\r\n    hide_dukancard_branding?: boolean;\r\n    custom_header_text?: string;\r\n    custom_header_image_url?: string; // Legacy field\r\n    custom_header_image_light_url?: string; // Light theme\r\n    custom_header_image_dark_url?: string; // Dark theme\r\n  };\r\n}\r\n\r\nexport default function CardHeader({ userPlan, establishedYear, customBranding }: CardHeaderProps) {\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  // Get theme-specific header image with fallback logic\r\n  const themeSpecificHeaderImage = getThemeSpecificHeaderImage(\r\n    userPlan,\r\n    customBranding,\r\n    resolvedTheme\r\n  );\r\n\r\n  // Get custom branding text\r\n  const customBrandingText = getBrandingText(userPlan, customBranding);\r\n\r\n  // Check if user has Pro/Enterprise access\r\n  const hasProEnterpriseAccess = hasCustomBrandingAccess(userPlan);\r\n\r\n  // Determine what to show in the header based on plan and toggle state\r\n  const shouldShowCustomHeaderImage = hasProEnterpriseAccess &&\r\n    customBranding?.hide_dukancard_branding &&\r\n    themeSpecificHeaderImage;\r\n\r\n  const shouldShowCustomHeaderText = hasProEnterpriseAccess &&\r\n    customBranding?.hide_dukancard_branding &&\r\n    customBrandingText &&\r\n    !shouldShowCustomHeaderImage; // Only show text if no image (image has priority)\r\n\r\n  // Use the centralized function for consistent branding logic\r\n  const shouldShowDukancardBrandingResult = shouldShowDukancardBranding(userPlan, customBranding);\r\n\r\n  return (\r\n    <div className=\"flex justify-between items-start\">\r\n      <div className=\"flex items-center\">\r\n        {/* Priority: Theme-specific header image > Custom header text > Dukancard branding */}\r\n        {shouldShowCustomHeaderImage ? (\r\n          <div className=\"max-w-[120px] max-h-[32px] overflow-hidden\">\r\n            <NextImage\r\n              src={themeSpecificHeaderImage}\r\n              alt=\"Custom header\"\r\n              width={120}\r\n              height={32}\r\n              className=\"h-8 w-auto object-contain\"\r\n              style={{ maxWidth: '120px', maxHeight: '32px' }}\r\n              onError={(e) => {\r\n                // Gracefully handle broken images by hiding the element\r\n                e.currentTarget.style.display = 'none';\r\n              }}\r\n            />\r\n          </div>\r\n        ) : shouldShowCustomHeaderText ? (\r\n          <span className=\"font-medium text-sm text-[var(--theme-color)]\">\r\n            {customBrandingText}\r\n          </span>\r\n        ) : (\r\n          /* Show Dukancard branding if toggle is OFF or no custom content */\r\n          shouldShowDukancardBrandingResult && (\r\n            <span className=\"font-bold text-md text-[var(--theme-color)]\">\r\n              Dukan\r\n              <span className=\"text-neutral-900 dark:text-white\">card</span>\r\n            </span>\r\n          )\r\n        )}\r\n      </div>\r\n\r\n      {/* Established Year badge */}\r\n      {establishedYear && (\r\n        <div className=\"flex items-center justify-center text-xs py-0.5 px-2 rounded-full bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)]\">\r\n          <Calendar className=\"w-3 h-3 mr-1 text-[var(--theme-color)]\" />\r\n          <span className=\"font-semibold\">Est. {establishedYear}</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAce,SAAS,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAmB;;IAC/F,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEjC,sDAAsD;IACtD,MAAM,2BAA2B,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EACzD,UACA,gBACA;IAGF,2BAA2B;IAC3B,MAAM,qBAAqB,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;IAErD,0CAA0C;IAC1C,MAAM,yBAAyB,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE;IAEvD,sEAAsE;IACtE,MAAM,8BAA8B,0BAClC,gBAAgB,2BAChB;IAEF,MAAM,6BAA6B,0BACjC,gBAAgB,2BAChB,sBACA,CAAC,6BAA6B,kDAAkD;IAElF,6DAA6D;IAC7D,MAAM,oCAAoC,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU;IAEhF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAEZ,4CACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAS;wBACR,KAAK;wBACL,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,OAAO;4BAAE,UAAU;4BAAS,WAAW;wBAAO;wBAC9C,SAAS,CAAC;4BACR,wDAAwD;4BACxD,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wBAClC;;;;;;;;;;2BAGF,2CACF,6LAAC;oBAAK,WAAU;8BACb;;;;;2BAGH,iEAAiE,GACjE,mDACE,6LAAC;oBAAK,WAAU;;wBAA8C;sCAE5D,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;;;;;;YAO1D,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAK,WAAU;;4BAAgB;4BAAM;;;;;;;;;;;;;;;;;;;AAKhD;GAxEwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 7827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardProfile.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  Too<PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>Provider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { User, Building, Info, Loader2 } from \"lucide-react\";\r\n\r\ntype LogoUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface CardProfileProps {\r\n  logo_url?: string | null;\r\n  localPreviewUrl?: string | null;\r\n  logoUploadStatus: LogoUploadStatus;\r\n  member_name?: string;\r\n  business_name?: string;\r\n  title?: string;\r\n  about_bio?: string;\r\n  finalThemeColor: string;\r\n}\r\n\r\nexport default function CardProfile({\r\n  logo_url,\r\n  localPreviewUrl,\r\n  logoUploadStatus,\r\n  member_name: _member_name,\r\n  business_name,\r\n  title: _title,\r\n  about_bio,\r\n  finalThemeColor,\r\n}: CardProfileProps) {\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <div className=\"relative w-20 h-20 sm:w-24 sm:h-24 rounded-full border-3 border-[var(--theme-color)] overflow-hidden flex items-center justify-center shadow-lg mb-2 sm:mb-3 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 transform hover:scale-105 transition-transform duration-300\">\r\n        {/* Only render Image if we have a valid URL */}\r\n        {(localPreviewUrl || (logo_url && typeof logo_url === 'string' && logo_url.trim() !== \"\")) && (\r\n          <Image\r\n            src={localPreviewUrl || (logo_url || \"\")}\r\n            alt={`${business_name} logo`}\r\n            width={96}\r\n            height={96}\r\n            className=\"object-cover w-full h-full\"\r\n            onError={(e) => (e.currentTarget.style.display = \"none\")}\r\n          />\r\n        )}\r\n        {!localPreviewUrl &&\r\n          (!logo_url || (typeof logo_url === 'string' && logo_url.trim() === \"\")) &&\r\n          logoUploadStatus !== \"uploading\" && (\r\n            <User\r\n              className=\"w-12 h-12 opacity-50\"\r\n              color={finalThemeColor}\r\n            />\r\n          )}\r\n        {logoUploadStatus === \"uploading\" && (\r\n          <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n            <Loader2 className=\"w-8 h-8 text-white animate-spin\" />\r\n          </div>\r\n        )}\r\n\r\n        {/* Subtle glow effect for the profile image */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-tr from-[var(--theme-color-10)] via-transparent to-[var(--theme-color-10)] opacity-40\"></div>\r\n        <div className=\"absolute -bottom-1 -right-1 w-full h-full rounded-full bg-black/5 blur-sm -z-10\"></div>\r\n      </div>\r\n\r\n      {/* Business name only */}\r\n      <h3 className=\"text-base sm:text-lg font-bold text-[--theme-color] mb-2 tracking-wide px-2 text-center\">\r\n        {business_name ? (\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <span className=\"line-clamp-1 relative cursor-default\">\r\n                  {/* Simple text without shadow effect */}\r\n                  <span className=\"relative\">\r\n                    {business_name}\r\n                  </span>\r\n                </span>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p>{business_name}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        ) : (\r\n          <Building\r\n            className=\"inline-block h-5 w-5 opacity-50\"\r\n            color={finalThemeColor}\r\n          />\r\n        )}\r\n      </h3>\r\n\r\n      {about_bio && (\r\n        <div className=\"flex items-start text-xs text-neutral-600 dark:text-neutral-300 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 rounded-lg max-w-xs mx-auto mb-2 sm:mb-3\">\r\n          <Info className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <p className=\"text-start line-clamp-2 cursor-default\">\r\n                  {about_bio}\r\n                </p>\r\n              </TooltipTrigger>\r\n              <TooltipContent className=\"max-w-xs\">\r\n                <p>{about_bio}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAMA;AAAA;AAAA;AAAA;;;;;AAee,SAAS,YAAY,EAClC,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,aAAa,YAAY,EACzB,aAAa,EACb,OAAO,MAAM,EACb,SAAS,EACT,eAAe,EACE;IACjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBAEZ,CAAC,mBAAoB,YAAY,OAAO,aAAa,YAAY,SAAS,IAAI,OAAO,EAAG,mBACvF,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,mBAAoB,YAAY;wBACrC,KAAK,GAAG,cAAc,KAAK,CAAC;wBAC5B,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,SAAS,CAAC,IAAO,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;oBAGpD,CAAC,mBACA,CAAC,CAAC,YAAa,OAAO,aAAa,YAAY,SAAS,IAAI,OAAO,EAAG,KACtE,qBAAqB,6BACnB,6LAAC,qMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,OAAO;;;;;;oBAGZ,qBAAqB,6BACpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAKvB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAG,WAAU;0BACX,8BACC,6LAAC,+HAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0CACN,6LAAC,+HAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6LAAC;oCAAK,WAAU;8CAEd,cAAA,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;0CAIP,6LAAC,+HAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;yCAKV,6LAAC,6MAAA,CAAA,WAAQ;oBACP,WAAU;oBACV,OAAO;;;;;;;;;;;YAKZ,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;8CAGL,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;KAxFwB", "debugId": null}}, {"offset": {"line": 8043, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardDivider.tsx"], "sourcesContent": ["export default function CardDivider() {\r\n  return (\r\n    <div className=\"h-6 w-full mx-auto max-w-xs mb-3 relative flex items-center justify-center\">\r\n      <div\r\n        className=\"h-px w-full absolute\"\r\n        style={{\r\n          background: `linear-gradient(to right, transparent, var(--theme-color-30), transparent)`,\r\n        }}\r\n      ></div>\r\n      <div className=\"relative z-10 flex items-center justify-center space-x-2\">\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]\"></div>\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-30)]\"></div>\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]\"></div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,0EAA0E,CAAC;gBAC1F;;;;;;0BAEF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;KAhBwB", "debugId": null}}, {"offset": {"line": 8111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardBusinessInfo.tsx"], "sourcesContent": ["import {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  Toolt<PERSON><PERSON>rovider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { Phone, MapPin, Clock, Truck, Mail } from \"lucide-react\";\r\nimport { formatTimeTo12Hour, formatDayGroup } from \"./utils/cardUtils\";\r\n\r\ninterface CardBusinessInfoProps {\r\n  fullAddress?: string;\r\n  displayAddressLine?: string;\r\n  locality?: string;\r\n  displayCityStatePin?: string;\r\n  phone?: string;\r\n  displayPhone?: string;\r\n  isAuthenticated: boolean;\r\n  telUrl?: string;\r\n  displayEmail?: string;\r\n  mailtoUrl?: string;\r\n  business_hours?: Record<string, { isOpen?: boolean; openTime?: string; closeTime?: string }> | null;\r\n  delivery_info?: string;\r\n}\r\n\r\nexport default function CardBusinessInfo({\r\n  fullAddress,\r\n  displayAddressLine,\r\n  locality,\r\n  displayCityStatePin,\r\n  phone,\r\n  displayPhone,\r\n  isAuthenticated,\r\n  telUrl,\r\n  displayEmail,\r\n  mailtoUrl,\r\n  business_hours,\r\n  delivery_info,\r\n}: CardBusinessInfoProps) {\r\n  return (\r\n    <div className=\"flex flex-col gap-2 sm:gap-3 max-w-xs mx-auto overflow-hidden\">\r\n      {/* Address section (on top) */}\r\n      {fullAddress && (\r\n        <div className=\"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full\">\r\n          <div className=\"flex items-start mb-2.5\">\r\n            <MapPin className=\"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"flex flex-col overflow-hidden\">\r\n              {displayAddressLine && locality ? (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <span className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                        {displayAddressLine}, {locality}\r\n                      </span>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>\r\n                        {displayAddressLine}, {locality}\r\n                      </p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              ) : (\r\n                <>\r\n                  {displayAddressLine && (\r\n                    <TooltipProvider>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <span className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                            {displayAddressLine}\r\n                          </span>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>\r\n                          <p>{displayAddressLine}</p>\r\n                        </TooltipContent>\r\n                      </Tooltip>\r\n                    </TooltipProvider>\r\n                  )}\r\n                  {locality && (\r\n                    <TooltipProvider>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <span className=\"text-xs text-neutral-600 dark:text-neutral-300 line-clamp-1 cursor-default\">\r\n                            {locality}\r\n                          </span>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>\r\n                          <p>{locality}</p>\r\n                        </TooltipContent>\r\n                      </Tooltip>\r\n                    </TooltipProvider>\r\n                  )}\r\n                </>\r\n              )}\r\n              {displayCityStatePin && (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <span className=\"text-xs text-neutral-500 dark:text-neutral-400 line-clamp-1 cursor-default\">\r\n                        {displayCityStatePin}\r\n                      </span>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>{displayCityStatePin}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n\r\n        </div>\r\n      )}\r\n      {/* Contact info section (below address) */}\r\n      <div className=\"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full\">\r\n        {/* Phone */}\r\n        {phone && (\r\n          <div className=\"flex items-center mb-2.5\">\r\n            <Phone className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"overflow-hidden\">\r\n              {displayPhone && (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <a\r\n                        href={isAuthenticated && telUrl ? telUrl : \"#\"}\r\n                        className={\r\n                          isAuthenticated && telUrl\r\n                            ? \"hover:underline font-medium text-xs truncate block\"\r\n                            : \"cursor-default font-medium text-xs truncate block\"\r\n                        }\r\n                      >\r\n                        {displayPhone}\r\n                      </a>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>{displayPhone}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Email */}\r\n        {displayEmail && (\r\n          <div className=\"flex items-center mb-2.5\">\r\n            <Mail className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"overflow-hidden\">\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <a\r\n                      href={\r\n                        isAuthenticated && mailtoUrl ? mailtoUrl : \"#\"\r\n                      }\r\n                      className={\r\n                        isAuthenticated\r\n                          ? \"hover:underline font-medium text-xs truncate block\"\r\n                          : \"cursor-default font-medium text-xs truncate block\"\r\n                      }\r\n                    >\r\n                      {displayEmail}\r\n                    </a>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>\r\n                    <p>{displayEmail}</p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Business hours - only show if at least one day is open */}\r\n        {(() => {\r\n          // Check if business_hours exists and is an object\r\n          if (!business_hours || typeof business_hours !== \"object\" || Object.keys(business_hours).length === 0) {\r\n            return null;\r\n          }\r\n\r\n          try {\r\n            // Check if at least one day has isOpen: true\r\n            const hasOpenDay = Object.values(business_hours).some(\r\n              (hours) => hours && typeof hours === \"object\" && (hours as { isOpen?: boolean }).isOpen\r\n            );\r\n\r\n            // If no days are open, don't show the business hours section\r\n            if (!hasOpenDay) {\r\n              return null;\r\n            }\r\n\r\n            // Get open days and their hours\r\n            const openDays = Object.entries(\r\n              business_hours as Record<string, unknown>\r\n            )\r\n              .filter(([, hours]) => {\r\n                return (\r\n                  hours &&\r\n                  typeof hours === \"object\" &&\r\n                  (hours as { isOpen?: boolean }).isOpen\r\n                );\r\n              })\r\n              .map(([day, hours]) => {\r\n                const hourData = hours as {\r\n                  isOpen: boolean;\r\n                  openTime?: string;\r\n                  closeTime?: string;\r\n                };\r\n                return {\r\n                  day,\r\n                  hours:\r\n                    hourData.openTime && hourData.closeTime\r\n                      ? `${formatTimeTo12Hour(\r\n                          hourData.openTime\r\n                        )} - ${formatTimeTo12Hour(\r\n                          hourData.closeTime\r\n                        )}`\r\n                      : \"\",\r\n                };\r\n              })\r\n              .filter((item) => item.hours);\r\n\r\n            // If no valid open days with hours, return null\r\n            if (openDays.length === 0) {\r\n              return null;\r\n            }\r\n\r\n            // Group days with the same hours\r\n            const hourGroups: Record<string, string[]> = {};\r\n            openDays.forEach(({ day, hours }) => {\r\n              if (!hourGroups[hours]) {\r\n                hourGroups[hours] = [];\r\n              }\r\n              hourGroups[hours].push(day);\r\n            });\r\n\r\n            // Return the business hours section with formatted day groups\r\n            return (\r\n              <div className=\"flex items-start mb-2.5\">\r\n                <Clock className=\"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]\" />\r\n                <div className=\"text-xs font-medium\">\r\n                  {Object.entries(hourGroups).map(\r\n                    ([hours, days], index) => {\r\n                      // Format days (e.g., \"Mon, Tue, Wed\" or \"Mon-Wed\")\r\n                      const formattedDays = formatDayGroup(days);\r\n\r\n                      return (\r\n                        <div\r\n                          key={index}\r\n                          className=\"flex justify-between\"\r\n                        >\r\n                          <span className=\"capitalize\">\r\n                            {formattedDays}:\r\n                          </span>\r\n                          <span className=\"ml-2\">{hours}</span>\r\n                        </div>\r\n                      );\r\n                    }\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          } catch (error) {\r\n            // If there's an error parsing the business hours, return null\r\n            console.error(\"Error parsing business hours:\", error);\r\n            return null;\r\n          }\r\n        })()}\r\n        {/* Delivery info - now available for all users */}\r\n        {delivery_info && (\r\n          <div className=\"flex items-center\">\r\n            <Truck className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <TooltipProvider>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <p className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                    {delivery_info}\r\n                  </p>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>{delivery_info}</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAiBe,SAAS,iBAAiB,EACvC,WAAW,EACX,kBAAkB,EAClB,QAAQ,EACR,mBAAmB,EACnB,KAAK,EACL,YAAY,EACZ,eAAe,EACf,MAAM,EACN,YAAY,EACZ,SAAS,EACT,cAAc,EACd,aAAa,EACS;IACtB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAI,WAAU;;gCACZ,sBAAsB,yBACrB,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDAAK,WAAU;;wDACb;wDAAmB;wDAAG;;;;;;;;;;;;0DAG3B,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;;wDACE;wDAAmB;wDAAG;;;;;;;;;;;;;;;;;;;;;;yDAM/B;;wCACG,oCACC,6LAAC,+HAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;kEACN,6LAAC,+HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC;4DAAK,WAAU;sEACb;;;;;;;;;;;kEAGL,6LAAC,+HAAA,CAAA,iBAAc;kEACb,cAAA,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;wCAKX,0BACC,6LAAC,+HAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;kEACN,6LAAC,+HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC;4DAAK,WAAU;sEACb;;;;;;;;;;;kEAGL,6LAAC,+HAAA,CAAA,iBAAc;kEACb,cAAA,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;gCAOf,qCACC,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;0DAGL,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,6LAAC;gBAAI,WAAU;;oBAEZ,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAI,WAAU;0CACZ,8BACC,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDACC,MAAM,mBAAmB,SAAS,SAAS;oDAC3C,WACE,mBAAmB,SACf,uDACA;8DAGL;;;;;;;;;;;0DAGL,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASjB,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDACC,MACE,mBAAmB,YAAY,YAAY;oDAE7C,WACE,kBACI,uDACA;8DAGL;;;;;;;;;;;0DAGL,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQf,CAAC;wBACA,kDAAkD;wBAClD,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,YAAY,OAAO,IAAI,CAAC,gBAAgB,MAAM,KAAK,GAAG;4BACrG,OAAO;wBACT;wBAEA,IAAI;4BACF,6CAA6C;4BAC7C,MAAM,aAAa,OAAO,MAAM,CAAC,gBAAgB,IAAI,CACnD,CAAC,QAAU,SAAS,OAAO,UAAU,YAAY,AAAC,MAA+B,MAAM;4BAGzF,6DAA6D;4BAC7D,IAAI,CAAC,YAAY;gCACf,OAAO;4BACT;4BAEA,gCAAgC;4BAChC,MAAM,WAAW,OAAO,OAAO,CAC7B,gBAEC,MAAM,CAAC,CAAC,GAAG,MAAM;gCAChB,OACE,SACA,OAAO,UAAU,YACjB,AAAC,MAA+B,MAAM;4BAE1C,GACC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;gCAChB,MAAM,WAAW;gCAKjB,OAAO;oCACL;oCACA,OACE,SAAS,QAAQ,IAAI,SAAS,SAAS,GACnC,GAAG,CAAA,GAAA,4LAAA,CAAA,qBAAkB,AAAD,EAClB,SAAS,QAAQ,EACjB,GAAG,EAAE,CAAA,GAAA,4LAAA,CAAA,qBAAkB,AAAD,EACtB,SAAS,SAAS,GACjB,GACH;gCACR;4BACF,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,KAAK;4BAE9B,gDAAgD;4BAChD,IAAI,SAAS,MAAM,KAAK,GAAG;gCACzB,OAAO;4BACT;4BAEA,iCAAiC;4BACjC,MAAM,aAAuC,CAAC;4BAC9C,SAAS,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;gCAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;oCACtB,UAAU,CAAC,MAAM,GAAG,EAAE;gCACxB;gCACA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;4BACzB;4BAEA,8DAA8D;4BAC9D,qBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,YAAY,GAAG,CAC7B,CAAC,CAAC,OAAO,KAAK,EAAE;4CACd,mDAAmD;4CACnD,MAAM,gBAAgB,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE;4CAErC,qBACE,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;;4DACb;4DAAc;;;;;;;kEAEjB,6LAAC;wDAAK,WAAU;kEAAQ;;;;;;;+CANnB;;;;;wCASX;;;;;;;;;;;;wBAKV,EAAE,OAAO,OAAO;4BACd,8DAA8D;4BAC9D,QAAQ,KAAK,CAAC,iCAAiC;4BAC/C,OAAO;wBACT;oBACF,CAAC;oBAEA,+BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC,+HAAA,CAAA,kBAAe;0CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;sDACN,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;sDAGL,6LAAC,+HAAA,CAAA,iBAAc;sDACb,cAAA,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;KAzQwB", "debugId": null}}, {"offset": {"line": 8655, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/BusinessCardPreview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useMemo } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { BusinessCardData, defaultBusinessCardData } from \"../schema\";\r\nimport QRCode from \"react-qr-code\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Phone,\r\n  ShoppingBag,\r\n  Star,\r\n  Heart,\r\n  UserPlus,\r\n  QrCode, // Added for fake QR code in demo mode\r\n} from \"lucide-react\";\r\nimport WhatsAppIcon from \"@/app/components/icons/WhatsAppIcon\";\r\nimport InstagramIcon from \"@/app/components/icons/InstagramIcon\";\r\nimport FacebookIcon from \"@/app/components/icons/FacebookIcon\";\r\nimport { ProductServiceData } from \"../../products/actions\";\r\nimport {\r\n  maskEmail,\r\n  maskPhoneNumber,\r\n  formatIndianNumberShort,\r\n} from \"@/lib/utils\";\r\nimport {\r\n  formatWhatsAppUrl,\r\n  formatTelUrl,\r\n  formatPrice,\r\n} from \"./utils/cardUtils\";\r\nimport {\r\n  generateCustomBrandingStyles,\r\n  getPrimaryThemeColor,\r\n} from \"@/lib/utils/customBranding\";\r\nimport CardBackgroundEffects from \"./CardBackgroundEffects\";\r\nimport CardCornerDecorations from \"./CardCornerDecorations\";\r\nimport CardGlowEffects from \"./CardGlowEffects\";\r\nimport CardHeader from \"./CardHeader\";\r\nimport CardProfile from \"./CardProfile\";\r\nimport CardDivider from \"./CardDivider\";\r\nimport CardBusinessInfo from \"./CardBusinessInfo\";\r\n\r\n\r\ntype LogoUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface BusinessCardPreviewProps {\r\n  data: BusinessCardData & {\r\n    products_services?: ProductServiceData[];\r\n  };\r\n  totalLikes?: number;\r\n  totalSubscriptions?: number;\r\n  averageRating?: number;\r\n  isSubscribed?: boolean;\r\n  hasLiked?: boolean;\r\n  isLoadingInteraction?: boolean;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n  logoUploadStatus?: LogoUploadStatus;\r\n  localPreviewUrl?: string | null;\r\n  isAuthenticated?: boolean;\r\n  isCurrentUserBusiness?: boolean;\r\n  isDemo?: boolean; // New prop to indicate if this is a demo card for homepage\r\n}\r\n\r\nexport default function BusinessCardPreview({\r\n  data,\r\n  userPlan,\r\n  logoUploadStatus = \"idle\",\r\n  localPreviewUrl = null,\r\n  isAuthenticated = false,\r\n  totalLikes = 0,\r\n  totalSubscriptions = 0,\r\n  averageRating = 0,\r\n  isDemo = false, // New prop with default value\r\n}: BusinessCardPreviewProps) {\r\n  const {\r\n    logo_url = data.logo_url ?? defaultBusinessCardData.logo_url,\r\n    member_name = defaultBusinessCardData.member_name || \"Your Name\",\r\n    business_name = defaultBusinessCardData.business_name ||\r\n      \"Your Business Name\",\r\n    about_bio = defaultBusinessCardData.about_bio,\r\n    address_line = defaultBusinessCardData.address_line,\r\n    locality = data.locality ?? defaultBusinessCardData.locality,\r\n    city = defaultBusinessCardData.city,\r\n    state = defaultBusinessCardData.state,\r\n    established_year = data.established_year ?? defaultBusinessCardData.established_year,\r\n    pincode = defaultBusinessCardData.pincode,\r\n    phone = defaultBusinessCardData.phone,\r\n    instagram_url = defaultBusinessCardData.instagram_url,\r\n    facebook_url = defaultBusinessCardData.facebook_url,\r\n    whatsapp_number = defaultBusinessCardData.whatsapp_number,\r\n    theme_color = data.theme_color,\r\n    business_hours = data.business_hours,\r\n    delivery_info = data.delivery_info,\r\n    business_slug = defaultBusinessCardData.business_slug || \"\",\r\n    products_services = [],\r\n    contact_email = defaultBusinessCardData.contact_email,\r\n    title = data.title || \"\",\r\n  } = data;\r\n\r\n  const whatsappUrl = formatWhatsAppUrl(whatsapp_number);\r\n  const telUrl = formatTelUrl(phone);\r\n  const mailtoUrl = contact_email ? `mailto:${contact_email}` : undefined;\r\n\r\n  const fullAddress = [address_line, locality, city, state, pincode]\r\n    .filter(Boolean)\r\n    .join(\", \");\r\n\r\n  const displayPhone = isAuthenticated ? phone : maskPhoneNumber(phone);\r\n  const displayEmail = isAuthenticated\r\n    ? contact_email\r\n    : maskEmail(contact_email);\r\n  // Always show address_line regardless of authentication status\r\n  const displayAddressLine = address_line?.trim() || \"\";\r\n  const displayCityStatePin = `${city || \"\"}, ${state || \"\"} ${\r\n    pincode ? `- ${pincode}` : \"\"\r\n  }`.trim();\r\n\r\n  const qrValue = business_slug\r\n    ? `https://dukancard.in/${business_slug}`\r\n    : null;\r\n  const qrDisplayUrl = business_slug\r\n    ? `dukancard.in/${business_slug}`\r\n    : \"Set Slug to activate\";\r\n\r\n  // Updated theme color logic - use custom branding colors if available and user is Pro/Enterprise\r\n  const finalThemeColor = useMemo(() => {\r\n    return getPrimaryThemeColor(userPlan, data.custom_branding, theme_color);\r\n  }, [theme_color, userPlan, data.custom_branding]);\r\n\r\n  // Generate card styles with custom branding support\r\n  const cardStyle = useMemo(() => {\r\n    return generateCustomBrandingStyles(userPlan, data.custom_branding, theme_color);\r\n  }, [userPlan, data.custom_branding, theme_color]);\r\n\r\n  return (\r\n    <motion.div\r\n      data-card-element\r\n      className={cn(`\r\n        relative w-full max-w-sm\r\n        rounded-xl overflow-hidden\r\n        transition-all duration-500\r\n\r\n        bg-gradient-to-br from-neutral-100 to-white dark:from-neutral-900 dark:to-neutral-950\r\n        shadow-xl\r\n        transform-gpu\r\n        border-0\r\n      `)}\r\n      style={cardStyle}\r\n    >\r\n      <CardBackgroundEffects finalThemeColor={finalThemeColor} />\r\n\r\n      <CardCornerDecorations />\r\n\r\n      {/* Content container */}\r\n      <div className=\"relative p-3 xs:p-4 sm:p-5 flex flex-col justify-between text-neutral-800 dark:text-white z-10\">\r\n        <CardHeader\r\n          userPlan={userPlan}\r\n          establishedYear={established_year}\r\n          customBranding={data.custom_branding}\r\n        />\r\n\r\n        {/* Floating interaction buttons are rendered at the bottom */}\r\n\r\n        {/* Main content - Profile section */}\r\n        <div className=\"mt-1\">\r\n          <CardProfile\r\n            logo_url={logo_url}\r\n            localPreviewUrl={localPreviewUrl}\r\n            logoUploadStatus={logoUploadStatus}\r\n            member_name={member_name}\r\n            business_name={business_name}\r\n            title={title}\r\n            about_bio={about_bio}\r\n            finalThemeColor={finalThemeColor}\r\n          />\r\n\r\n          <CardDivider />\r\n\r\n          <CardBusinessInfo\r\n            fullAddress={fullAddress}\r\n            displayAddressLine={displayAddressLine}\r\n            locality={locality}\r\n            displayCityStatePin={displayCityStatePin}\r\n            phone={phone}\r\n            displayPhone={displayPhone}\r\n            isAuthenticated={isAuthenticated}\r\n            telUrl={telUrl}\r\n            displayEmail={displayEmail}\r\n            mailtoUrl={mailtoUrl}\r\n            business_hours={business_hours}\r\n            delivery_info={delivery_info}\r\n          />\r\n\r\n          {/* Products & Services section - Full width */}\r\n          {products_services && products_services.length > 0 && (\r\n            <div className=\"mt-3 max-w-xs mx-auto\">\r\n              <div className=\"flex items-center text-xs uppercase font-bold tracking-wider text-[--theme-color] mb-2 justify-center\">\r\n                <ShoppingBag\r\n                  className=\"w-3 h-3 mr-1.5\"\r\n                  color={finalThemeColor}\r\n                />\r\n                Products & Services\r\n              </div>\r\n\r\n              <div className=\"space-y-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 p-2.5 rounded-lg\">\r\n                {products_services.slice(0, 3).map((item) => (\r\n                  <div\r\n                    key={item.id}\r\n                    className=\"text-neutral-700 dark:text-neutral-200\"\r\n                  >\r\n                    <div className=\"flex justify-between items-baseline gap-2\">\r\n                      <TooltipProvider>\r\n                        <Tooltip>\r\n                          <TooltipTrigger asChild>\r\n                            <span className=\"font-medium text-xs truncate cursor-default\">\r\n                              {item.name}\r\n                            </span>\r\n                          </TooltipTrigger>\r\n                          <TooltipContent>\r\n                            <p>{item.name}</p>\r\n                          </TooltipContent>\r\n                        </Tooltip>\r\n                      </TooltipProvider>\r\n                      <span className=\"text-xs font-bold bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)] py-0.5 px-2 rounded-full flex-shrink-0\">\r\n                        {formatPrice(item.base_price)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* QR Code section */}\r\n          <div className=\"mt-3 max-w-xs mx-auto\">\r\n            {isDemo ? (\r\n              // Demo mode - show fake QR code\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <p className=\"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default\">\r\n                    dukancard.in/demo-business\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0\">\r\n                  <QrCode\r\n                    className=\"w-14 h-14 text-neutral-800\"\r\n                    strokeWidth={1.5}\r\n                  />\r\n                </div>\r\n              </div>\r\n            ) : qrValue ? (\r\n              // Real QR code for actual business cards\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <TooltipProvider>\r\n                    <Tooltip>\r\n                      <TooltipTrigger asChild>\r\n                        <p className=\"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default\">\r\n                          {qrDisplayUrl}\r\n                        </p>\r\n                      </TooltipTrigger>\r\n                      <TooltipContent>\r\n                        <p>{qrDisplayUrl}</p>\r\n                      </TooltipContent>\r\n                    </Tooltip>\r\n                  </TooltipProvider>\r\n                </div>\r\n                <div\r\n                  id=\"business-card-qrcode\"\r\n                  className=\"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0\"\r\n                >\r\n                  <QRCode\r\n                    value={qrValue}\r\n                    size={60}\r\n                    level=\"M\"\r\n                    bgColor=\"#FFFFFF\"\r\n                    fgColor=\"#000000\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Placeholder for when no slug is set\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <p className=\"text-xs font-mono text-neutral-500 dark:text-neutral-500 line-clamp-1\">\r\n                    {qrDisplayUrl}\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white p-1.5 rounded-lg shadow-md opacity-50 flex-shrink-0\">\r\n                  <svg\r\n                    className=\"w-14 h-14 text-[--theme-color]\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Interaction metrics */}\r\n          <div className=\"flex justify-center items-center gap-2 sm:gap-4 text-xs text-neutral-500 dark:text-neutral-400 mt-3 mb-2 flex-wrap\">\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <Heart className=\"w-4 h-4 text-red-500\" />\r\n              <span className=\"font-medium\">\r\n                {formatIndianNumberShort(totalLikes)}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <UserPlus className=\"w-4 h-4 text-blue-500\" />\r\n              <span className=\"font-medium\">\r\n                {formatIndianNumberShort(totalSubscriptions)}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <Star className=\"w-4 h-4 text-amber-500 fill-current\" />\r\n              <span className=\"font-medium\">{averageRating.toFixed(1)}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Interaction buttons have been moved outside the card */}\r\n        </div>\r\n\r\n        {/* Social Links */}\r\n        <div className=\"pt-3 pb-2\">\r\n          <TooltipProvider>\r\n            <div className=\"flex justify-center items-center space-x-2\">\r\n              {instagram_url && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <InstagramIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={instagram_url}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <InstagramIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Instagram Button\" : \"Instagram\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {facebook_url && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <FacebookIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={facebook_url}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <FacebookIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Facebook Button\" : \"Facebook\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {whatsappUrl && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <WhatsAppIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={whatsappUrl}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <WhatsAppIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo WhatsApp Button\" : \"Chat on WhatsApp\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {displayPhone && telUrl && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <Phone className=\"w-4 h-4\" color={finalThemeColor} />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable or non-clickable based on authentication\r\n                      <a\r\n                        href={isAuthenticated ? telUrl : \"#\"}\r\n                        className={`w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all ${\r\n                          isAuthenticated\r\n                            ? \"hover:scale-110 hover:shadow-md\"\r\n                            : \"cursor-default opacity-70\"\r\n                        }`}\r\n                      >\r\n                        <Phone className=\"w-4 h-4\" color={finalThemeColor} />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Call Button\" : \"Call directly\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n            </div>\r\n          </TooltipProvider>\r\n        </div>\r\n\r\n        {/* Bottom accent bar */}\r\n        <div\r\n          className=\"absolute bottom-0 left-0 right-0 h-1.5 mt-2\"\r\n          style={{\r\n            background: `linear-gradient(to right, var(--theme-color), var(--theme-accent-end), var(--theme-color))`,\r\n          }}\r\n        ></div>\r\n      </div>\r\n\r\n      <CardGlowEffects />\r\n    </motion.div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAOA;AAKA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA7CA;;;;;;;;;;;;;;;;;;;;;AAoEe,SAAS,oBAAoB,EAC1C,IAAI,EACJ,QAAQ,EACR,mBAAmB,MAAM,EACzB,kBAAkB,IAAI,EACtB,kBAAkB,KAAK,EACvB,aAAa,CAAC,EACd,qBAAqB,CAAC,EACtB,gBAAgB,CAAC,EACjB,SAAS,KAAK,EACW;;IACzB,MAAM,EACJ,WAAW,KAAK,QAAQ,IAAI,kKAAA,CAAA,0BAAuB,CAAC,QAAQ,EAC5D,cAAc,kKAAA,CAAA,0BAAuB,CAAC,WAAW,IAAI,WAAW,EAChE,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,IACnD,oBAAoB,EACtB,YAAY,kKAAA,CAAA,0BAAuB,CAAC,SAAS,EAC7C,eAAe,kKAAA,CAAA,0BAAuB,CAAC,YAAY,EACnD,WAAW,KAAK,QAAQ,IAAI,kKAAA,CAAA,0BAAuB,CAAC,QAAQ,EAC5D,OAAO,kKAAA,CAAA,0BAAuB,CAAC,IAAI,EACnC,QAAQ,kKAAA,CAAA,0BAAuB,CAAC,KAAK,EACrC,mBAAmB,KAAK,gBAAgB,IAAI,kKAAA,CAAA,0BAAuB,CAAC,gBAAgB,EACpF,UAAU,kKAAA,CAAA,0BAAuB,CAAC,OAAO,EACzC,QAAQ,kKAAA,CAAA,0BAAuB,CAAC,KAAK,EACrC,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,EACrD,eAAe,kKAAA,CAAA,0BAAuB,CAAC,YAAY,EACnD,kBAAkB,kKAAA,CAAA,0BAAuB,CAAC,eAAe,EACzD,cAAc,KAAK,WAAW,EAC9B,iBAAiB,KAAK,cAAc,EACpC,gBAAgB,KAAK,aAAa,EAClC,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,IAAI,EAAE,EAC3D,oBAAoB,EAAE,EACtB,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,EACrD,QAAQ,KAAK,KAAK,IAAI,EAAE,EACzB,GAAG;IAEJ,MAAM,cAAc,CAAA,GAAA,4LAAA,CAAA,oBAAiB,AAAD,EAAE;IACtC,MAAM,SAAS,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD,EAAE;IAC5B,MAAM,YAAY,gBAAgB,CAAC,OAAO,EAAE,eAAe,GAAG;IAE9D,MAAM,cAAc;QAAC;QAAc;QAAU;QAAM;QAAO;KAAQ,CAC/D,MAAM,CAAC,SACP,IAAI,CAAC;IAER,MAAM,eAAe,kBAAkB,QAAQ,CAAA,GAAA,+GAAA,CAAA,kBAAe,AAAD,EAAE;IAC/D,MAAM,eAAe,kBACjB,gBACA,CAAA,GAAA,+GAAA,CAAA,YAAS,AAAD,EAAE;IACd,+DAA+D;IAC/D,MAAM,qBAAqB,cAAc,UAAU;IACnD,MAAM,sBAAsB,GAAG,QAAQ,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,EACzD,UAAU,CAAC,EAAE,EAAE,SAAS,GAAG,IAC3B,CAAC,IAAI;IAEP,MAAM,UAAU,gBACZ,CAAC,qBAAqB,EAAE,eAAe,GACvC;IACJ,MAAM,eAAe,gBACjB,CAAC,aAAa,EAAE,eAAe,GAC/B;IAEJ,iGAAiG;IACjG,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAAE;YAC9B,OAAO,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,KAAK,eAAe,EAAE;QAC9D;uDAAG;QAAC;QAAa;QAAU,KAAK,eAAe;KAAC;IAEhD,oDAAoD;IACpD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YACxB,OAAO,CAAA,GAAA,iIAAA,CAAA,+BAA4B,AAAD,EAAE,UAAU,KAAK,eAAe,EAAE;QACtE;iDAAG;QAAC;QAAU,KAAK,eAAe;QAAE;KAAY;IAEhD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,mBAAiB;QACjB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,CAAC;;;;;;;;;MASf,CAAC;QACD,OAAO;;0BAEP,6LAAC,gMAAA,CAAA,UAAqB;gBAAC,iBAAiB;;;;;;0BAExC,6LAAC,gMAAA,CAAA,UAAqB;;;;;0BAGtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qLAAA,CAAA,UAAU;wBACT,UAAU;wBACV,iBAAiB;wBACjB,gBAAgB,KAAK,eAAe;;;;;;kCAMtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sLAAA,CAAA,UAAW;gCACV,UAAU;gCACV,iBAAiB;gCACjB,kBAAkB;gCAClB,aAAa;gCACb,eAAe;gCACf,OAAO;gCACP,WAAW;gCACX,iBAAiB;;;;;;0CAGnB,6LAAC,sLAAA,CAAA,UAAW;;;;;0CAEZ,6LAAC,2LAAA,CAAA,UAAgB;gCACf,aAAa;gCACb,oBAAoB;gCACpB,UAAU;gCACV,qBAAqB;gCACrB,OAAO;gCACP,cAAc;gCACd,iBAAiB;gCACjB,QAAQ;gCACR,cAAc;gCACd,WAAW;gCACX,gBAAgB;gCAChB,eAAe;;;;;;4BAIhB,qBAAqB,kBAAkB,MAAM,GAAG,mBAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uNAAA,CAAA,cAAW;gDACV,WAAU;gDACV,OAAO;;;;;;4CACP;;;;;;;kDAIJ,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAClC,6LAAC;gDAEC,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+HAAA,CAAA,kBAAe;sEACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;kFACN,6LAAC,+HAAA,CAAA,iBAAc;wEAAC,OAAO;kFACrB,cAAA,6LAAC;4EAAK,WAAU;sFACb,KAAK,IAAI;;;;;;;;;;;kFAGd,6LAAC,+HAAA,CAAA,iBAAc;kFACb,cAAA,6LAAC;sFAAG,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sEAInB,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,4LAAA,CAAA,cAAW,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;+CAjB3B,KAAK,EAAE;;;;;;;;;;;;;;;;0CA2BtB,6LAAC;gCAAI,WAAU;0CACZ,SACC,gCAAgC;8CAChC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,6LAAC;oDAAE,WAAU;8DAAmF;;;;;;;;;;;;sDAIlG,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDACL,WAAU;gDACV,aAAa;;;;;;;;;;;;;;;;2CAIjB,UACF,yCAAyC;8CACzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,6LAAC,+HAAA,CAAA,kBAAe;8DACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0EACN,6LAAC,+HAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,6LAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;0EAGL,6LAAC,+HAAA,CAAA,iBAAc;0EACb,cAAA,6LAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKZ,6LAAC;4CACC,IAAG;4CACH,WAAU;sDAEV,cAAA,6LAAC,sJAAA,CAAA,UAAM;gDACL,OAAO;gDACP,MAAM;gDACN,OAAM;gDACN,SAAQ;gDACR,SAAQ;;;;;;;;;;;;;;;;2CAKd,sCAAsC;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,6LAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;sDAGL,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,SAAQ;gDACR,MAAK;gDACL,QAAO;0DAEP,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;;;;;;;;;;;;kDAG7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;;;;;;;;;;;;kDAG7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAe,cAAc,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,kBAAe;sCACd,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,+BACC,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+IAAA,CAAA,UAAa;wDAAC,WAAU;;;;;;;;;;2DAG3B,6BAA6B;8DAC7B,6LAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,+IAAA,CAAA,UAAa;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI/B,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,0BAA0B;;;;;;;;;;;;oCAKzC,8BACC,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;2DAG1B,6BAA6B;8DAC7B,6LAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI9B,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,yBAAyB;;;;;;;;;;;;oCAKxC,6BACC,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;2DAG1B,6BAA6B;8DAC7B,6LAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI9B,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,yBAAyB;;;;;;;;;;;;oCAKxC,gBAAgB,wBACf,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAU,OAAO;;;;;;;;;;2DAGpC,iEAAiE;8DACjE,6LAAC;oDACC,MAAM,kBAAkB,SAAS;oDACjC,WAAW,CAAC,yLAAyL,EACnM,kBACI,oCACA,6BACJ;8DAEF,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAU,OAAO;;;;;;;;;;;;;;;;0DAIxC,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3C,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,0FAA0F,CAAC;wBAC1G;;;;;;;;;;;;0BAIJ,6LAAC,0LAAA,CAAA,UAAe;;;;;;;;;;;AAGtB;GAjZwB;KAAA", "debugId": null}}, {"offset": {"line": 9499, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/EnhancedCardShowcase.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport BusinessCardPreview from \"@/app/(dashboard)/dashboard/business/card/components/BusinessCardPreview\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\ninterface EnhancedCardShowcaseProps {\r\n  cardData: BusinessCardData;\r\n  isDemo?: boolean;\r\n  isAuthenticated?: boolean;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n}\r\n\r\nexport default function EnhancedCardShowcase({\r\n  cardData,\r\n  isDemo = false,\r\n  isAuthenticated = false,\r\n  userPlan,\r\n}: EnhancedCardShowcaseProps) {\r\n  return (\r\n    <div className=\"relative w-full flex justify-center items-center\">\r\n      {/* Premium product-like glow effect behind the card */}\r\n      <div className=\"absolute inset-0 -z-10\">\r\n        {/* Main central glow - further reduced intensity */}\r\n        <motion.div\r\n          className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[90%] h-[90%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 rounded-full blur-[50px]\"\r\n          animate={{\r\n            opacity: [0.3, 0.4, 0.3],\r\n            scale: [0.95, 1.01, 0.95],\r\n          }}\r\n          transition={{\r\n            duration: 8,\r\n            repeat: Infinity,\r\n            repeatType: \"reverse\",\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n\r\n        {/* Secondary glow for depth - further reduced intensity */}\r\n        <motion.div\r\n          className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[70%] h-[70%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-[30px]\"\r\n          animate={{\r\n            opacity: [0.2, 0.3, 0.2],\r\n            scale: [1, 1.03, 1],\r\n          }}\r\n          transition={{\r\n            duration: 10,\r\n            repeat: Infinity,\r\n            repeatType: \"reverse\",\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n\r\n        {/* Ambient corner glows - further reduced intensity */}\r\n        <motion.div\r\n          className=\"absolute top-0 left-0 w-[40%] h-[40%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 rounded-full blur-[40px]\"\r\n          animate={{\r\n            opacity: [0.1, 0.2, 0.1],\r\n            x: [-5, 5, -5],\r\n            y: [-5, 5, -5],\r\n          }}\r\n          transition={{\r\n            duration: 12,\r\n            repeat: Infinity,\r\n            repeatType: \"reverse\",\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n        <motion.div\r\n          className=\"absolute bottom-0 right-0 w-[40%] h-[40%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 rounded-full blur-[40px]\"\r\n          animate={{\r\n            opacity: [0.1, 0.2, 0.1],\r\n            x: [5, -5, 5],\r\n            y: [5, -5, 5],\r\n          }}\r\n          transition={{\r\n            duration: 12,\r\n            repeat: Infinity,\r\n            repeatType: \"reverse\",\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n\r\n        {/* Product spotlight effect - reduced intensity */}\r\n        <div className=\"absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[40%] bg-white/5 dark:bg-white/3 blur-[60px] opacity-40\"></div>\r\n      </div>\r\n\r\n      {/* Card container - centered with fixed width */}\r\n      <motion.div\r\n        className=\"w-full max-w-sm mx-auto\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ duration: 0.8 }}\r\n      >\r\n        <BusinessCardPreview\r\n          data={cardData}\r\n          userPlan={userPlan || \"basic\"}\r\n          isAuthenticated={isAuthenticated}\r\n          totalLikes={isDemo ? 50251 : (cardData.total_likes ?? 0)}\r\n          totalSubscriptions={isDemo ? 32053 : (cardData.total_subscriptions ?? 0)}\r\n          averageRating={isDemo ? 4.8 : (cardData.average_rating ?? 0)}\r\n          isDemo={isDemo}\r\n        />\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS,qBAAqB,EAC3C,QAAQ,EACR,SAAS,KAAK,EACd,kBAAkB,KAAK,EACvB,QAAQ,EACkB;IAC1B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO;gCAAC;gCAAM;gCAAM;6BAAK;wBAC3B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;4BACZ,MAAM;wBACR;;;;;;kCAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO;gCAAC;gCAAG;gCAAM;6BAAE;wBACrB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;4BACZ,MAAM;wBACR;;;;;;kCAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,GAAG;gCAAC,CAAC;gCAAG;gCAAG,CAAC;6BAAE;4BACd,GAAG;gCAAC,CAAC;gCAAG;gCAAG,CAAC;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;4BACZ,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,GAAG;gCAAC;gCAAG,CAAC;gCAAG;6BAAE;4BACb,GAAG;gCAAC;gCAAG,CAAC;gCAAG;6BAAE;wBACf;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;4BACZ,MAAM;wBACR;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,8LAAA,CAAA,UAAmB;oBAClB,MAAM;oBACN,UAAU,YAAY;oBACtB,iBAAiB;oBACjB,YAAY,SAAS,QAAS,SAAS,WAAW,IAAI;oBACtD,oBAAoB,SAAS,QAAS,SAAS,mBAAmB,IAAI;oBACtE,eAAe,SAAS,MAAO,SAAS,cAAc,IAAI;oBAC1D,QAAQ;;;;;;;;;;;;;;;;;AAKlB;KA7FwB", "debugId": null}}, {"offset": {"line": 9687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/DigitalCardFeature.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { exampleCardData } from \"./exampleCardData\";\r\nimport CardBackground from \"./CardBackground\";\r\nimport EnhancedFeatureElements from \"./EnhancedFeatureElements\";\r\nimport EnhancedMetricsContainer from \"./EnhancedMetricsContainer\";\r\nimport EnhancedCardShowcase from \"./EnhancedCardShowcase\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\ninterface DigitalCardFeatureProps {\r\n  minimal?: boolean;\r\n  hideTitle?: boolean;\r\n  isAuthenticated?: boolean;\r\n  userType?: \"business\" | \"customer\" | null;\r\n  businessCardData?: BusinessCardData;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n}\r\n\r\nexport default function DigitalCardFeature({\r\n  minimal = false,\r\n  hideTitle = false,\r\n  isAuthenticated = false,\r\n  userType = null,\r\n  businessCardData,\r\n  userPlan\r\n}: DigitalCardFeatureProps) {\r\n  // Ref for card\r\n  const cardRef = useRef(null);\r\n\r\n  // Determine which card data to use\r\n  const cardData = (isAuthenticated && userType === \"business\" && businessCardData)\r\n    ? businessCardData\r\n    : exampleCardData;\r\n\r\n  // Determine if this is a demo card\r\n  const isDemo = !(isAuthenticated && userType === \"business\" && businessCardData);\r\n\r\n  return (\r\n    <div className={minimal ? \"relative\" : \"relative w-full overflow-hidden py-16 md:py-20\"}>\r\n      {/* Background effects */}\r\n      <CardBackground />\r\n\r\n      <div className={minimal ? \"\" : \"container mx-auto px-4 max-w-7xl\"}>\r\n        {/* Section title - only shown in full mode */}\r\n        {!minimal && (\r\n          <motion.div\r\n            className=\"relative mb-8 md:mb-10 text-center\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            viewport={{ once: true, amount: 0.3 }}\r\n            transition={{ duration: 0.7 }}\r\n          >\r\n            <h2 className=\"text-2xl md:text-3xl lg:text-4xl font-bold text-center\">\r\n              Transform Your Business{\" \"}\r\n              <span className=\"text-[var(--brand-gold)]\">\r\n                Digital Presence\r\n              </span>\r\n            </h2>\r\n\r\n            {/* Subtle gradient line under heading */}\r\n            <motion.div\r\n              className=\"mt-3 mx-auto w-24 h-0.5 bg-gradient-to-r from-transparent via-[var(--brand-gold)]/50 to-transparent\"\r\n              initial={{ width: 0, opacity: 0 }}\r\n              whileInView={{ width: \"120px\", opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 1, delay: 0.3 }}\r\n            />\r\n          </motion.div>\r\n        )}\r\n\r\n        {/* Minimal mode title */}\r\n        {minimal && !hideTitle && (\r\n          <div className=\"mb-6\">\r\n            <h2 className=\"text-2xl md:text-3xl font-bold mb-2\">\r\n              Get Your Business <span className=\"text-[var(--brand-gold)]\">Online Instantly</span>\r\n            </h2>\r\n            <p className=\"text-neutral-600 dark:text-neutral-400 mb-4\">\r\n              Launch your digital storefront in <span className=\"font-semibold\">just 30 seconds</span> — <span className=\"text-[var(--brand-gold)] font-semibold\">completely FREE</span>\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Card showcase with enhanced visuals */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.7, delay: minimal ? 0.2 : 0.4 }}\r\n          className=\"relative w-full max-w-4xl mx-auto flex justify-center\"\r\n        >\r\n          <div ref={cardRef} className=\"relative w-full max-w-md\">\r\n            {/* Enhanced card component with dynamic demo mode */}\r\n            <EnhancedCardShowcase\r\n              cardData={cardData}\r\n              isDemo={isDemo}\r\n              isAuthenticated={isAuthenticated}\r\n              userPlan={userPlan}\r\n            />\r\n\r\n            {/* Enhanced feature elements - visible in both modes */}\r\n            <div className=\"absolute inset-0 pointer-events-none hidden md:block\">\r\n              <EnhancedFeatureElements minimal={minimal} />\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Enhanced metrics container - now visible in both modes */}\r\n        <motion.div\r\n          className=\"mt-8 md:mt-10 w-full max-w-4xl mx-auto\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.7, delay: 0.4 }}\r\n        >\r\n          <EnhancedMetricsContainer\r\n            likes={isDemo ? 50251 : (businessCardData?.total_likes ?? 0)}\r\n            subscribers={isDemo ? 32053 : (businessCardData?.total_subscriptions ?? 0)}\r\n            rating={isDemo ? 4.8 : (businessCardData?.average_rating ?? 0)}\r\n            views={isDemo ? 1300000 : (businessCardData?.total_visits ?? 0)}\r\n            minimal={minimal}\r\n          />\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBe,SAAS,mBAAmB,EACzC,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,kBAAkB,KAAK,EACvB,WAAW,IAAI,EACf,gBAAgB,EAChB,QAAQ,EACgB;;IACxB,eAAe;IACf,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,mCAAmC;IACnC,MAAM,WAAW,AAAC,mBAAmB,aAAa,cAAc,mBAC5D,mBACA,8JAAA,CAAA,kBAAe;IAEnB,mCAAmC;IACnC,MAAM,SAAS,CAAC,CAAC,mBAAmB,aAAa,cAAc,gBAAgB;IAE/E,qBACE,6LAAC;QAAI,WAAW,UAAU,aAAa;;0BAErC,6LAAC,8JAAA,CAAA,UAAc;;;;;0BAEf,6LAAC;gBAAI,WAAW,UAAU,KAAK;;oBAE5B,CAAC,yBACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAI;wBACpC,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAG,WAAU;;oCAAyD;oCAC7C;kDACxB,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAM7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAE;gCAChC,aAAa;oCAAE,OAAO;oCAAS,SAAS;gCAAE;gCAC1C,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;;;;;;;;;;;;oBAM3C,WAAW,CAAC,2BACX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;kDAChC,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAE/D,6LAAC;gCAAE,WAAU;;oCAA8C;kDACvB,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAsB;kDAAG,6LAAC;wCAAK,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;kCAM1J,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,UAAU,MAAM;wBAAI;wBACxD,WAAU;kCAEV,cAAA,6LAAC;4BAAI,KAAK;4BAAS,WAAU;;8CAE3B,6LAAC,oKAAA,CAAA,UAAoB;oCACnB,UAAU;oCACV,QAAQ;oCACR,iBAAiB;oCACjB,UAAU;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uKAAA,CAAA,UAAuB;wCAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;kCAMxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC,wKAAA,CAAA,UAAwB;4BACvB,OAAO,SAAS,QAAS,kBAAkB,eAAe;4BAC1D,aAAa,SAAS,QAAS,kBAAkB,uBAAuB;4BACxE,QAAQ,SAAS,MAAO,kBAAkB,kBAAkB;4BAC5D,OAAO,SAAS,UAAW,kBAAkB,gBAAgB;4BAC7D,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GAzGwB;KAAA", "debugId": null}}, {"offset": {"line": 9955, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/shared/PopularCategoriesSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getPopularCategories } from \"@/lib/config/categories\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\n\r\ninterface PopularCategoriesSectionProps {\r\n  className?: string;\r\n  maxCategories?: number;\r\n}\r\n\r\nexport default function PopularCategoriesSection({\r\n  className = \"\",\r\n  maxCategories = 15,\r\n}: PopularCategoriesSectionProps) {\r\n  // Get popular categories from the shared config\r\n  const displayCategories = getPopularCategories(maxCategories);\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.05,\r\n        delayChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      className={`w-full mt-0 ${className}`}\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      <motion.div\r\n        className=\"flex justify-center mb-4 md:mb-5\"\r\n        initial={{ opacity: 0, y: -10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <div className=\"relative\">\r\n          <h2 className=\"text-xl md:text-2xl font-bold text-neutral-800 dark:text-neutral-100 text-center px-4\">\r\n            <span className=\"relative z-10 inline-block\">\r\n              Popular Categories\r\n              <motion.span\r\n                className=\"absolute -bottom-1 left-0 right-0 h-[6px] bg-[var(--brand-gold)]/20 rounded-full\"\r\n                initial={{ width: 0 }}\r\n                animate={{ width: \"100%\" }}\r\n                transition={{ duration: 0.8, delay: 0.3 }}\r\n              />\r\n            </span>\r\n          </h2>\r\n        </div>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className={`grid ${\r\n          className || \"grid-cols-3 md:grid-cols-5\"\r\n        } gap-4 md:gap-5 lg:gap-6`}\r\n        variants={containerVariants}\r\n        initial=\"hidden\"\r\n        animate=\"visible\"\r\n      >\r\n        {displayCategories.map((category, index) => (\r\n          <motion.div\r\n            key={category.name}\r\n            className=\"flex flex-col items-center\"\r\n            variants={itemVariants}\r\n            transition={{ duration: 0.3, delay: index * 0.05 }}\r\n            whileHover={{ y: -5, transition: { duration: 0.2 } }}\r\n          >\r\n            <Link\r\n              href={`/discover?category=${encodeURIComponent(category.name)}`}\r\n              className=\"w-full flex flex-col items-center\"\r\n            >\r\n              <div className=\"relative group cursor-pointer\">\r\n                {/* Hover glow effect */}\r\n                <motion.div\r\n                  className=\"absolute -inset-1 rounded-xl bg-[var(--brand-gold)]/10 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\r\n                  initial={{ scale: 0.9 }}\r\n                  whileHover={{ scale: 1.1 }}\r\n                />\r\n\r\n                {/* Icon container */}\r\n                <div className=\"relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 lg:w-20 lg:h-20 bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-xl group-hover:border-[var(--brand-gold)] transition-all duration-300 shadow-sm group-hover:shadow-md\">\r\n                  <motion.div\r\n                    initial={{ scale: 1 }}\r\n                    whileHover={{ scale: 1.1 }}\r\n                    transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\r\n                  >\r\n                    {React.createElement(category.icon, {\r\n                      className:\r\n                        \"w-7 h-7 md:w-8 md:h-8 lg:w-9 lg:h-9 text-[var(--brand-gold)]\",\r\n                    })}\r\n                  </motion.div>\r\n                </div>\r\n              </div>\r\n\r\n              <span className=\"mt-3 text-sm md:text-sm lg:text-sm text-center font-medium text-neutral-700 dark:text-neutral-300 truncate max-w-full\">\r\n                {category.name}\r\n              </span>\r\n            </Link>\r\n          </motion.div>\r\n        ))}\r\n      </motion.div>\r\n\r\n      {/* View All Categories Button */}\r\n      <motion.div\r\n        className=\"flex justify-center mt-8\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5, delay: 0.3 }}\r\n      >\r\n        <Link href=\"/discover\">\r\n          <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.98 }}>\r\n            <Button className=\"bg-white dark:bg-neutral-900 hover:bg-neutral-100 dark:hover:bg-neutral-800 text-neutral-800 dark:text-neutral-200 border border-neutral-200 dark:border-neutral-800 px-5 py-2 rounded-full font-medium text-sm relative overflow-hidden group shadow-sm hover:shadow-md transition-all duration-300\">\r\n              <span className=\"flex items-center\">\r\n                View All Categories\r\n                <ArrowRight className=\"ml-2 w-4 h-4 text-[var(--brand-gold)]\" />\r\n              </span>\r\n            </Button>\r\n          </motion.div>\r\n        </Link>\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAce,SAAS,yBAAyB,EAC/C,YAAY,EAAE,EACd,gBAAgB,EAAE,EACY;IAC9B,gDAAgD;IAChD,MAAM,oBAAoB,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE;IAE/C,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,YAAY,EAAE,WAAW;QACrC,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCACZ,cAAA,6LAAC;4BAAK,WAAU;;gCAA6B;8CAE3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAO;oCACzB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,KAAK,EACf,aAAa,6BACd,wBAAwB,CAAC;gBAC1B,UAAU;gBACV,SAAQ;gBACR,SAAQ;0BAEP,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,UAAU;wBACV,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAK;wBACjD,YAAY;4BAAE,GAAG,CAAC;4BAAG,YAAY;gCAAE,UAAU;4BAAI;wBAAE;kCAEnD,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,mBAAmB,EAAE,mBAAmB,SAAS,IAAI,GAAG;4BAC/D,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAI;4CACtB,YAAY;gDAAE,OAAO;4CAAI;;;;;;sDAI3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;gDAAE;gDACpB,YAAY;oDAAE,OAAO;gDAAI;gDACzB,YAAY;oDAAE,MAAM;oDAAU,WAAW;oDAAK,SAAS;gDAAG;0DAEzD,cAAA,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE;oDAClC,WACE;gDACJ;;;;;;;;;;;;;;;;;8CAKN,6LAAC;oCAAK,WAAU;8CACb,SAAS,IAAI;;;;;;;;;;;;uBAlCb,SAAS,IAAI;;;;;;;;;;0BA0CxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,YAAY;4BAAE,OAAO;wBAAK;wBAAG,UAAU;4BAAE,OAAO;wBAAK;kCAC/D,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,WAAU;sCAChB,cAAA,6LAAC;gCAAK,WAAU;;oCAAoB;kDAElC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;KA3HwB", "debugId": null}}, {"offset": {"line": 10246, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/HomeCategoriesSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport PopularCategoriesSection from \"@/app/(main)/components/shared/PopularCategoriesSection\";\r\n\r\nexport default function HomeCategoriesSection() {\r\n  return (\r\n    <section className=\"w-full pt-6 md:pt-8 pb-6 md:pb-8 relative\">\r\n      {/* Subtle top divider */}\r\n      <div className=\"absolute top-0 left-1/2 -translate-x-1/2 w-1/2 max-w-md h-px bg-gradient-to-r from-transparent via-neutral-300 dark:via-neutral-700 to-transparent\"></div>\r\n\r\n      {/* Subtle bottom divider */}\r\n      <div className=\"absolute bottom-0 left-1/2 -translate-x-1/2 w-1/2 max-w-md h-px bg-gradient-to-r from-transparent via-neutral-300 dark:via-neutral-700 to-transparent\"></div>\r\n\r\n      <div className=\"w-full mx-auto\">\r\n        <div className=\"max-w-[1920px] mx-auto\">\r\n          <PopularCategoriesSection />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAMe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uKAAA,CAAA,UAAwB;;;;;;;;;;;;;;;;;;;;;AAKnC;KAhBwB", "debugId": null}}, {"offset": {"line": 10310, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/EnhancedCardActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { Download, FileDown, QrCode as QrCodeIcon, CreditCard } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\nimport QRCode from \"react-qr-code\";\r\nimport { generateAndDownloadQRCode, downloadRawQRImage } from \"@/lib/qrCodeGenerator\";\r\nimport { downloadBusinessCard, findBusinessCardElement } from \"@/lib/cardDownloader\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface EnhancedCardActionsProps {\r\n  businessSlug: string;\r\n  businessName: string;\r\n  ownerName?: string;\r\n  businessAddress?: string;\r\n  themeColor?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function EnhancedCardActions({\r\n  businessSlug,\r\n  businessName,\r\n  ownerName = \"\",\r\n  businessAddress = \"\",\r\n  themeColor = \"#F59E0B\",\r\n  className,\r\n}: EnhancedCardActionsProps) {\r\n  const [qrCodeSvg, setQrCodeSvg] = useState<SVGSVGElement | null>(null);\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Set isClient to true after component mounts\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Get QR code SVG element after component mounts\r\n  useEffect(() => {\r\n    if (qrCodeRef.current) {\r\n      const svg = qrCodeRef.current.querySelector(\"svg\");\r\n      if (svg instanceof SVGSVGElement) {\r\n        setQrCodeSvg(svg);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Download A4 formatted QR code\r\n  const handleDownloadA4QR = async () => {\r\n    if (!businessSlug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    if (!qrCodeSvg) {\r\n      toast.error(\"QR code not available for download.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Format the address for display\r\n      const formattedAddress =\r\n        businessAddress.trim() || \"Address not available\";\r\n      const formattedOwnerName = ownerName.trim() || \"Owner\";\r\n\r\n      await generateAndDownloadQRCode(qrCodeSvg, {\r\n        businessName,\r\n        ownerName: formattedOwnerName,\r\n        address: formattedAddress,\r\n        slug: businessSlug,\r\n        qrValue: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}`,\r\n        themeColor,\r\n      });\r\n\r\n      toast.success(\"A4 QR code downloaded!\");\r\n    } catch (error) {\r\n      console.error(\"Error generating QR code:\", error);\r\n      toast.error(\"Could not download QR code.\");\r\n    }\r\n  };\r\n\r\n  // Download raw QR image\r\n  const handleDownloadRawQR = async () => {\r\n    if (!businessSlug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    if (!qrCodeSvg) {\r\n      toast.error(\"QR code not available for download.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await downloadRawQRImage(qrCodeSvg, businessSlug);\r\n      toast.success(\"High-quality QR image downloaded!\");\r\n    } catch (error) {\r\n      console.error(\"Error downloading QR image:\", error);\r\n      toast.error(\"Could not download QR image.\");\r\n    }\r\n  };\r\n\r\n  // Download business card as PNG\r\n  const handleDownloadCardPNG = async () => {\r\n    if (!businessSlug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Find the business card element\r\n      let cardElement = findBusinessCardElement();\r\n\r\n      // Additional validation to ensure we have the right element\r\n      if (cardElement) {\r\n        const rect = cardElement.getBoundingClientRect();\r\n\r\n        // If the element is too large, it might be a container, try to find the actual card\r\n        if (rect.width > 500) {\r\n          const actualCard = cardElement.querySelector('[data-card-element]') as HTMLElement;\r\n          if (actualCard) {\r\n            cardElement = actualCard;\r\n          }\r\n        }\r\n      }\r\n\r\n      if (!cardElement) {\r\n        toast.error(\"Business card not found for download.\");\r\n        return;\r\n      }\r\n\r\n      await downloadBusinessCard(cardElement, {\r\n        businessName,\r\n        businessSlug,\r\n      });\r\n\r\n      toast.success(\"Digital card downloaded as PNG!\");\r\n    } catch (error) {\r\n      console.error(\"Error downloading business card as PNG:\", error);\r\n      toast.error(\"Could not download business card.\");\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Hidden QR code for download\r\n  const qrValue = businessSlug ? `https://dukancard.in/${businessSlug}` : \"\";\r\n\r\n  return (\r\n    <div className={cn(\"w-full max-w-sm mx-auto space-y-5 mt-6\", className)}>\r\n      {/* Hidden QR code for download */}\r\n      <div className=\"hidden\">\r\n        <div id=\"public-card-qrcode\" ref={qrCodeRef}>\r\n          <QRCode\r\n            value={qrValue}\r\n            size={300}\r\n            level=\"M\"\r\n            bgColor=\"#FFFFFF\"\r\n            fgColor=\"#000000\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Enhanced Download Button with Glow Effect */}\r\n      <div className=\"w-full relative group\">\r\n        {/* Button glow effect with properly rounded corners */}\r\n        {isClient && (\r\n          <motion.div\r\n            className=\"absolute -inset-0.5 rounded-full blur-md\"\r\n            style={{\r\n              background: \"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))\"\r\n            }}\r\n            initial={{ opacity: 0.7 }}\r\n            animate={{\r\n              opacity: [0.7, 0.9, 0.7],\r\n              boxShadow: [\r\n                \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\",\r\n                \"0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)\",\r\n                \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\"\r\n              ]\r\n            }}\r\n            transition={{\r\n              duration: 2,\r\n              repeat: Infinity,\r\n              repeatType: \"loop\",\r\n              ease: \"easeInOut\"\r\n            }}\r\n          />\r\n        )}\r\n\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              className={cn(\r\n                \"w-full py-6 relative overflow-hidden group\",\r\n                \"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90\",\r\n                \"text-black dark:text-neutral-900 font-medium text-base\",\r\n                \"border-none rounded-full shadow-lg hover:shadow-xl\",\r\n                \"transition-all duration-300 ease-out\"\r\n              )}\r\n            >\r\n              {/* Shimmer effect */}\r\n              <span className=\"absolute inset-0 w-full h-full overflow-hidden\">\r\n                <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none\" />\r\n              </span>\r\n\r\n              <div className=\"flex items-center justify-center gap-3 relative z-10\">\r\n                <div className=\"bg-white/20 p-2 rounded-lg\">\r\n                  <Download className=\"h-5 w-5 text-black dark:text-neutral-900\" />\r\n                </div>\r\n                <span className=\"text-black dark:text-neutral-900 font-semibold\">Download Options</span>\r\n              </div>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"center\" className=\"w-56 bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800\">\r\n            <DropdownMenuItem onClick={handleDownloadCardPNG} className=\"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800\">\r\n              <CreditCard className=\"mr-2 h-4 w-4\" />\r\n              <span>Download Digital Card (PNG)</span>\r\n            </DropdownMenuItem>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem onClick={handleDownloadA4QR} className=\"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800\">\r\n              <FileDown className=\"mr-2 h-4 w-4\" />\r\n              <span>Download A4 Size QR</span>\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem onClick={handleDownloadRawQR} className=\"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800\">\r\n              <QrCodeIcon className=\"mr-2 h-4 w-4\" />\r\n              <span>Download High-Quality QR Image</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AA8EoB;;AA5EpB;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AA4Be,SAAS,oBAAoB,EAC1C,YAAY,EACZ,YAAY,EACZ,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,aAAa,SAAS,EACtB,SAAS,EACgB;;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,YAAY;QACd;wCAAG,EAAE;IAEL,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,MAAM,UAAU,OAAO,CAAC,aAAa,CAAC;gBAC5C,IAAI,eAAe,eAAe;oBAChC,aAAa;gBACf;YACF;QACF;wCAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc;YACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,WAAW;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,mBACJ,gBAAgB,IAAI,MAAM;YAC5B,MAAM,qBAAqB,UAAU,IAAI,MAAM;YAE/C,MAAM,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;gBACzC;gBACA,WAAW;gBACX,SAAS;gBACT,MAAM;gBACN,SAAS,GAAG,6DAAoC,uBAAuB,CAAC,EAAE,cAAc;gBACxF;YACF;YAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc;YACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,WAAW;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,gCAAgC;IAChC,MAAM,wBAAwB;QAC5B,IAAI,CAAC,cAAc;YACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,iCAAiC;YACjC,IAAI,cAAc,CAAA,GAAA,wHAAA,CAAA,0BAAuB,AAAD;YAExC,4DAA4D;YAC5D,IAAI,aAAa;gBACf,MAAM,OAAO,YAAY,qBAAqB;gBAE9C,oFAAoF;gBACpF,IAAI,KAAK,KAAK,GAAG,KAAK;oBACpB,MAAM,aAAa,YAAY,aAAa,CAAC;oBAC7C,IAAI,YAAY;wBACd,cAAc;oBAChB;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa;gBACtC;gBACA;YACF;YAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAIA,8BAA8B;IAC9B,MAAM,UAAU,eAAe,CAAC,qBAAqB,EAAE,cAAc,GAAG;IAExE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;0BAE3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,IAAG;oBAAqB,KAAK;8BAChC,cAAA,6LAAC,sJAAA,CAAA,UAAM;wBACL,OAAO;wBACP,MAAM;wBACN,OAAM;wBACN,SAAQ;wBACR,SAAQ;;;;;;;;;;;;;;;;0BAMd,6LAAC;gBAAI,WAAU;;oBAEZ,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;wBACA,SAAS;4BAAE,SAAS;wBAAI;wBACxB,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,WAAW;gCACT;gCACA;gCACA;6BACD;wBACH;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;4BACZ,MAAM;wBACR;;;;;;kCAIJ,6LAAC,wIAAA,CAAA,eAAY;;0CACX,6LAAC,wIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8CACA,0DACA,0DACA,sDACA;;sDAIF,6LAAC;4CAAK,WAAU;sDACd,cAAA,6LAAC;gDAAK,WAAU;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAK,WAAU;8DAAiD;;;;;;;;;;;;;;;;;;;;;;;0CAIvE,6LAAC,wIAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAS,WAAU;;kDAC5C,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS;wCAAuB,WAAU;;0DAC1D,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,wIAAA,CAAA,wBAAqB;;;;;kDACtB,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS;wCAAoB,WAAU;;0DACvD,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS;wCAAqB,WAAU;;0DACxD,6LAAC,6MAAA,CAAA,SAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;GAvNwB;KAAA", "debugId": null}}, {"offset": {"line": 10690, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/HeroActionButtons.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport Link from \"next/link\";\r\nimport { ArrowRight, Share2 } from \"lucide-react\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\nimport EnhancedCardActions from \"@/app/components/shared/EnhancedCardActions\";\r\n\r\ninterface HeroActionButtonsProps {\r\n  isAuthenticated: boolean;\r\n  userType: \"business\" | \"customer\" | null;\r\n  businessCardData?: BusinessCardData;\r\n}\r\n\r\nexport default function HeroActionButtons({\r\n  isAuthenticated,\r\n  userType,\r\n  businessCardData\r\n}: HeroActionButtonsProps) {\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Set isClient to true after component mounts\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Share handler for business users\r\n  const handleShare = () => {\r\n    if (!businessCardData?.business_slug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    const shareUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessCardData.business_slug}`;\r\n    const shareText = `Check out my digital business card: ${businessCardData.business_name || \"My Business\"} on Dukancard!`;\r\n\r\n    // Use Web Share API if available\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: `${businessCardData.business_name} - Digital Business Card`,\r\n        text: shareText,\r\n        url: shareUrl,\r\n      }).catch((error) => {\r\n        console.error('Error sharing:', error);\r\n        // Fallback to clipboard\r\n        copyToClipboard(shareUrl);\r\n      });\r\n    } else {\r\n      // Fallback to clipboard\r\n      copyToClipboard(shareUrl);\r\n    }\r\n  };\r\n\r\n  // Copy to clipboard helper\r\n  const copyToClipboard = (text: string) => {\r\n    navigator.clipboard.writeText(text)\r\n      .then(() => toast.success(\"Link copied to clipboard!\"))\r\n      .catch(() => toast.error(\"Failed to copy link.\"));\r\n  };\r\n\r\n  // Show different buttons based on user type\r\n  if (isAuthenticated && userType === \"business\" && businessCardData) {\r\n    // Format address for the download QR functionality\r\n    const formatAddress = (data: BusinessCardData) => {\r\n      const addressParts = [\r\n        data.address_line,\r\n        data.locality,\r\n        data.city,\r\n        data.state,\r\n        data.pincode,\r\n      ].filter(Boolean);\r\n      return addressParts.join(\", \") || \"Address not available\";\r\n    };\r\n\r\n    return (\r\n      <div className=\"w-full max-w-sm mx-auto space-y-5\">\r\n        {/* Download QR Button - using the same component as public cards */}\r\n        <EnhancedCardActions\r\n          businessSlug={businessCardData.business_slug || \"\"}\r\n          businessName={businessCardData.business_name || \"\"}\r\n          ownerName={businessCardData.member_name || \"\"}\r\n          businessAddress={formatAddress(businessCardData)}\r\n          themeColor={businessCardData.theme_color || \"#F59E0B\"}\r\n        />\r\n\r\n        {/* Share Button - matching the style of download button */}\r\n        <div className=\"w-full relative group\">\r\n          {/* Button glow effect with properly rounded corners */}\r\n          {isClient && (\r\n            <motion.div\r\n              className=\"absolute -inset-0.5 rounded-full blur-md\"\r\n              style={{\r\n                background: \"linear-gradient(to right, rgba(34, 197, 94, 0.6), rgba(34, 197, 94, 0.8))\"\r\n              }}\r\n              initial={{ opacity: 0.7 }}\r\n              animate={{\r\n                opacity: [0.7, 0.9, 0.7],\r\n                boxShadow: [\r\n                  \"0 0 15px 2px rgba(34, 197, 94, 0.3)\",\r\n                  \"0 0 20px 4px rgba(34, 197, 94, 0.5)\",\r\n                  \"0 0 15px 2px rgba(34, 197, 94, 0.3)\"\r\n                ]\r\n              }}\r\n              transition={{\r\n                duration: 2,\r\n                repeat: Infinity,\r\n                repeatType: \"loop\",\r\n                ease: \"easeInOut\"\r\n              }}\r\n            />\r\n          )}\r\n\r\n          <Button\r\n            onClick={handleShare}\r\n            disabled={!businessCardData.business_slug}\r\n            className={cn(\r\n              \"w-full py-6 relative overflow-hidden group\",\r\n              \"bg-green-600 hover:bg-green-700\",\r\n              \"text-white font-medium text-base\",\r\n              \"border-none rounded-full shadow-lg hover:shadow-xl\",\r\n              \"transition-all duration-300 ease-out\"\r\n            )}\r\n          >\r\n            {/* Shimmer effect */}\r\n            <span className=\"absolute inset-0 w-full h-full overflow-hidden\">\r\n              <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none\" />\r\n            </span>\r\n\r\n            <div className=\"flex items-center justify-center gap-3 relative z-10\">\r\n              <div className=\"bg-white/20 p-2 rounded-lg\">\r\n                <Share2 className=\"h-5 w-5 text-white\" />\r\n              </div>\r\n              <span className=\"text-white font-semibold\">Share Your Card</span>\r\n            </div>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Default button for non-business users or non-authenticated users\r\n  return (\r\n    <Link href=\"/login\">\r\n      <div className=\"flex justify-center items-center\">\r\n        <Button className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900 px-6 py-2 rounded-full font-medium text-base relative overflow-hidden group\">\r\n        <span className=\"flex items-center\">\r\n          List Your Business Now\r\n          <ArrowRight className=\"ml-2 w-4 h-4\" />\r\n        </span>\r\n      </Button>\r\n      </div>\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAqCwB;;AAnCxB;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;;;AAVA;;;;;;;;;AAkBe,SAAS,kBAAkB,EACxC,eAAe,EACf,QAAQ,EACR,gBAAgB,EACO;;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,YAAY;QACd;sCAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,cAAc;QAClB,IAAI,CAAC,kBAAkB,eAAe;YACpC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,WAAW,GAAG,6DAAoC,uBAAuB,CAAC,EAAE,iBAAiB,aAAa,EAAE;QAClH,MAAM,YAAY,CAAC,oCAAoC,EAAE,iBAAiB,aAAa,IAAI,cAAc,cAAc,CAAC;QAExH,iCAAiC;QACjC,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO,GAAG,iBAAiB,aAAa,CAAC,wBAAwB,CAAC;gBAClE,MAAM;gBACN,KAAK;YACP,GAAG,KAAK,CAAC,CAAC;gBACR,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,wBAAwB;gBACxB,gBAAgB;YAClB;QACF,OAAO;YACL,wBAAwB;YACxB,gBAAgB;QAClB;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC,MAC3B,IAAI,CAAC,IAAM,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,8BACzB,KAAK,CAAC,IAAM,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IAC7B;IAEA,4CAA4C;IAC5C,IAAI,mBAAmB,aAAa,cAAc,kBAAkB;QAClE,mDAAmD;QACnD,MAAM,gBAAgB,CAAC;YACrB,MAAM,eAAe;gBACnB,KAAK,YAAY;gBACjB,KAAK,QAAQ;gBACb,KAAK,IAAI;gBACT,KAAK,KAAK;gBACV,KAAK,OAAO;aACb,CAAC,MAAM,CAAC;YACT,OAAO,aAAa,IAAI,CAAC,SAAS;QACpC;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,sJAAA,CAAA,UAAmB;oBAClB,cAAc,iBAAiB,aAAa,IAAI;oBAChD,cAAc,iBAAiB,aAAa,IAAI;oBAChD,WAAW,iBAAiB,WAAW,IAAI;oBAC3C,iBAAiB,cAAc;oBAC/B,YAAY,iBAAiB,WAAW,IAAI;;;;;;8BAI9C,6LAAC;oBAAI,WAAU;;wBAEZ,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;4BACd;4BACA,SAAS;gCAAE,SAAS;4BAAI;4BACxB,SAAS;gCACP,SAAS;oCAAC;oCAAK;oCAAK;iCAAI;gCACxB,WAAW;oCACT;oCACA;oCACA;iCACD;4BACH;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,YAAY;gCACZ,MAAM;4BACR;;;;;;sCAIJ,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,iBAAiB,aAAa;4BACzC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8CACA,mCACA,oCACA,sDACA;;8CAIF,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;8CAGlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CAAK,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMvD;IAEA,mEAAmE;IACnE,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAK;kBACT,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gBAAC,WAAU;0BAClB,cAAA,6LAAC;oBAAK,WAAU;;wBAAoB;sCAElC,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhC;GA3IwB;KAAA", "debugId": null}}, {"offset": {"line": 10935, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/StickyHeroSectionClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport ModernSearchSection from \"./ModernSearchSection\";\r\nimport DigitalCardFeature from \"./DigitalCardFeature\";\r\nimport HomeCategoriesSection from \"./HomeCategoriesSection\";\r\nimport HeroActionButtons from \"./HeroActionButtons\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\ninterface StickyHeroSectionClientProps {\r\n  isAuthenticated: boolean;\r\n  userType: \"business\" | \"customer\" | null;\r\n  businessCardData?: BusinessCardData;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n}\r\n\r\nexport default function StickyHeroSectionClient({\r\n  isAuthenticated,\r\n  userType,\r\n  businessCardData,\r\n  userPlan\r\n}: StickyHeroSectionClientProps) {\r\n\r\n  return (\r\n    <section className=\"relative w-full bg-white dark:bg-black pt-24 pb-16 md:pt-32 md:pb-24\">\r\n      {/* Simplified background - just a static gradient */}\r\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\r\n        {/* Simple static glow */}\r\n        <div className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[400px] bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-[120px]\"></div>\r\n\r\n        {/* Subtle pattern overlay */}\r\n        <div className=\"absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-5 dark:opacity-10\"></div>\r\n      </div>\r\n\r\n      {/* Mobile layout (stacked) with simplified animations */}\r\n      <div className=\"md:hidden container mx-auto px-4\">\r\n        {/* Search section on top */}\r\n        <div className=\"mb-6 w-full\">\r\n          <ModernSearchSection minimal={true} />\r\n        </div>\r\n\r\n        {/* Popular Categories section below search */}\r\n        <div className=\"mb-12\">\r\n          <HomeCategoriesSection />\r\n        </div>\r\n\r\n        {/* Card section below */}\r\n        <div>\r\n          <div className=\"mb-6 text-center\">\r\n            {isAuthenticated && userType === \"business\" && businessCardData ? (\r\n              <>\r\n                <h2 className=\"text-2xl md:text-3xl font-bold mb-2\">\r\n                  Your Digital Business Card is <span className=\"text-[var(--brand-gold)]\">Live!</span>\r\n                </h2>\r\n                <p className=\"text-neutral-600 dark:text-neutral-400 mb-4\">\r\n                  Share your card with customers and <span className=\"font-semibold\">grow your business</span> — <span className=\"text-[var(--brand-gold)] font-semibold\">completely FREE</span>\r\n                </p>\r\n              </>\r\n            ) : (\r\n              <>\r\n                <h2 className=\"text-2xl md:text-3xl font-bold mb-2\">\r\n                  Get Your Business <span className=\"text-[var(--brand-gold)]\">Online Instantly</span>\r\n                </h2>\r\n                <p className=\"text-neutral-600 dark:text-neutral-400 mb-4\">\r\n                  Launch your digital storefront in <span className=\"font-semibold\">just 30 seconds</span> — <span className=\"text-[var(--brand-gold)] font-semibold\">completely FREE</span>\r\n                </p>\r\n              </>\r\n            )}\r\n          </div>\r\n          <DigitalCardFeature\r\n            minimal={true}\r\n            hideTitle={true}\r\n            isAuthenticated={isAuthenticated}\r\n            userType={userType}\r\n            businessCardData={businessCardData}\r\n            userPlan={userPlan}\r\n          />\r\n\r\n          {/* Action buttons below the card */}\r\n          <div className=\"mt-6\">\r\n            <HeroActionButtons\r\n              isAuthenticated={isAuthenticated}\r\n              userType={userType}\r\n              businessCardData={businessCardData}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Desktop layout with simplified animations */}\r\n      <div className=\"hidden md:block container mx-auto px-4 max-w-7xl\">\r\n        <div className=\"flex flex-col lg:flex-row gap-8 lg:gap-16\">\r\n          {/* Left column - Search and Categories */}\r\n          <div className=\"w-full lg:w-1/2\">\r\n            <div className=\"lg:sticky lg:top-24\">\r\n              {/* Search section */}\r\n              <div className=\"mb-6 w-full\">\r\n                <ModernSearchSection minimal={true} />\r\n              </div>\r\n\r\n              {/* Popular Categories section below search */}\r\n              <div>\r\n                <HomeCategoriesSection />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right column - Card */}\r\n          <div className=\"w-full lg:w-1/2\">\r\n            <div className=\"lg:sticky lg:top-24\">\r\n              <div className=\"mb-6 text-center\">\r\n                {isAuthenticated && userType === \"business\" && businessCardData ? (\r\n                  <>\r\n                    <h2 className=\"text-2xl md:text-3xl font-bold mb-2\">\r\n                      Your Digital Business Card is <span className=\"text-[var(--brand-gold)]\">Live!</span>\r\n                    </h2>\r\n                    <p className=\"text-neutral-600 dark:text-neutral-400 mb-4\">\r\n                      Share your card with customers and <span className=\"font-semibold\">grow your business</span> — <span className=\"text-[var(--brand-gold)] font-semibold\">completely FREE</span>\r\n                    </p>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <h2 className=\"text-2xl md:text-3xl font-bold mb-2\">\r\n                      Get Your Business <span className=\"text-[var(--brand-gold)]\">Online Instantly</span>\r\n                    </h2>\r\n                    <p className=\"text-neutral-600 dark:text-neutral-400 mb-4\">\r\n                      Launch your digital storefront in <span className=\"font-semibold\">just 30 seconds</span> — <span className=\"text-[var(--brand-gold)] font-semibold\">completely FREE</span>\r\n                    </p>\r\n                  </>\r\n                )}\r\n              </div>\r\n              <DigitalCardFeature\r\n                minimal={true}\r\n                hideTitle={true}\r\n                isAuthenticated={isAuthenticated}\r\n                userType={userType}\r\n                businessCardData={businessCardData}\r\n                userPlan={userPlan}\r\n              />\r\n\r\n              {/* Action buttons below the card */}\r\n              <div className=\"mt-6\">\r\n                <HeroActionButtons\r\n                  isAuthenticated={isAuthenticated}\r\n                  userType={userType}\r\n                  businessCardData={businessCardData}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAee,SAAS,wBAAwB,EAC9C,eAAe,EACf,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACqB;IAE7B,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mKAAA,CAAA,UAAmB;4BAAC,SAAS;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qKAAA,CAAA,UAAqB;;;;;;;;;;kCAIxB,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,aAAa,cAAc,iCAC7C;;sDACE,6LAAC;4CAAG,WAAU;;gDAAsC;8DACpB,6LAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;sDAE3E,6LAAC;4CAAE,WAAU;;gDAA8C;8DACtB,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAyB;8DAAG,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;;;;;;;;iEAI5J;;sDACE,6LAAC;4CAAG,WAAU;;gDAAsC;8DAChC,6LAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;sDAE/D,6LAAC;4CAAE,WAAU;;gDAA8C;8DACvB,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAsB;8DAAG,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;0CAK5J,6LAAC,kKAAA,CAAA,UAAkB;gCACjB,SAAS;gCACT,WAAW;gCACX,iBAAiB;gCACjB,UAAU;gCACV,kBAAkB;gCAClB,UAAU;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iKAAA,CAAA,UAAiB;oCAChB,iBAAiB;oCACjB,UAAU;oCACV,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAO1B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mKAAA,CAAA,UAAmB;4CAAC,SAAS;;;;;;;;;;;kDAIhC,6LAAC;kDACC,cAAA,6LAAC,qKAAA,CAAA,UAAqB;;;;;;;;;;;;;;;;;;;;;sCAM5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,mBAAmB,aAAa,cAAc,iCAC7C;;8DACE,6LAAC;oDAAG,WAAU;;wDAAsC;sEACpB,6LAAC;4DAAK,WAAU;sEAA2B;;;;;;;;;;;;8DAE3E,6LAAC;oDAAE,WAAU;;wDAA8C;sEACtB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAAyB;sEAAG,6LAAC;4DAAK,WAAU;sEAAyC;;;;;;;;;;;;;yEAI5J;;8DACE,6LAAC;oDAAG,WAAU;;wDAAsC;sEAChC,6LAAC;4DAAK,WAAU;sEAA2B;;;;;;;;;;;;8DAE/D,6LAAC;oDAAE,WAAU;;wDAA8C;sEACvB,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAAsB;sEAAG,6LAAC;4DAAK,WAAU;sEAAyC;;;;;;;;;;;;;;;;;;;kDAK5J,6LAAC,kKAAA,CAAA,UAAkB;wCACjB,SAAS;wCACT,WAAW;wCACX,iBAAiB;wCACjB,UAAU;wCACV,kBAAkB;wCAClB,UAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iKAAA,CAAA,UAAiB;4CAChB,iBAAiB;4CACjB,UAAU;4CACV,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC;KA1IwB", "debugId": null}}, {"offset": {"line": 11375, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/landing/SectionDivider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface SectionDividerProps {\r\n  variant?: \"gold\" | \"blue\" | \"purple\" | \"neutral\";\r\n  className?: string;\r\n}\r\n\r\nexport default function SectionDivider({\r\n  variant: _variant = \"gold\",\r\n  className = \"\"\r\n}: SectionDividerProps) {\r\n  // Define colors based on variant - removed unused function\r\n\r\n  return (\r\n    <div className={`w-full h-12 md:h-16 relative overflow-hidden ${className}`}>\r\n      {/* Center line */}\r\n      <div className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-7xl px-4 mx-auto\">\r\n        <motion.div\r\n          className=\"w-full h-px bg-gradient-to-r from-transparent via-neutral-400 to-transparent dark:via-neutral-600\"\r\n          initial={{ width: \"0%\", left: \"50%\" }}\r\n          whileInView={{ width: \"100%\", left: \"0%\" }}\r\n          viewport={{ once: true }}\r\n          transition={{ duration: 1.2 }}\r\n        />\r\n      </div>\r\n\r\n      {/* Decorative dots */}\r\n      <motion.div\r\n        className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-neutral-400 dark:bg-neutral-600\"\r\n        initial={{ scale: 0, opacity: 0 }}\r\n        whileInView={{ scale: 1, opacity: 0.7 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.5, delay: 0.7 }}\r\n      />\r\n\r\n      {/* Outer ring */}\r\n      <motion.div\r\n        className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full border-2 border-neutral-300 dark:border-neutral-700\"\r\n        initial={{ scale: 0, opacity: 0 }}\r\n        whileInView={{ scale: 1, opacity: 0.5 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.5, delay: 0.8 }}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,eAAe,EACrC,SAAS,WAAW,MAAM,EAC1B,YAAY,EAAE,EACM;IACpB,2DAA2D;IAE3D,qBACE,6LAAC;QAAI,WAAW,CAAC,6CAA6C,EAAE,WAAW;;0BAEzE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;wBAAM,MAAM;oBAAM;oBACpC,aAAa;wBAAE,OAAO;wBAAQ,MAAM;oBAAK;oBACzC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;;;;;;;;;;;0BAKhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,aAAa;oBAAE,OAAO;oBAAG,SAAS;gBAAI;gBACtC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;;;;;0BAI1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,aAAa;oBAAE,OAAO;oBAAG,SAAS;gBAAI;gBACtC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;;;;;;;;;;;AAIhD;KAtCwB", "debugId": null}}, {"offset": {"line": 11479, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/LandingPageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { AnimatePresence } from \"framer-motion\";\r\nimport ErrorDialog from \"./components/landing/ErrorDialog\";\r\nimport FeaturesSection from \"./components/landing/FeaturesSection\";\r\nimport PricingSection from \"./components/landing/PricingSection\";\r\nimport TestimonialsSection from \"./components/landing/TestimonialsSection\";\r\nimport CTASection from \"./components/landing/CTASection\";\r\nimport NewArrivalsSection from \"./components/landing/NewArrivalsSection\";\r\nimport PopularBusinessesSection from \"./components/landing/PopularBusinessesSection\";\r\nimport StickyHeroSectionClient from \"./components/landing/StickyHeroSectionClient\";\r\nimport SectionDivider from \"./components/landing/SectionDivider\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\n// Helper function to determine user plan (same as public card page)\r\nconst getUserPlan = (\r\n  profile: { subscription_status: string | null; plan_id: string | null }\r\n): \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | undefined => {\r\n  // Simply return the plan_id from the subscription data\r\n  switch (profile.plan_id) {\r\n    case \"free\":\r\n      return \"free\";\r\n    case \"growth\":\r\n      return \"growth\";\r\n    case \"pro\":\r\n      return \"pro\";\r\n    case \"enterprise\":\r\n      return \"enterprise\";\r\n    case \"basic\":\r\n      return \"basic\";\r\n    default:\r\n      return \"free\"; // Default to free if no plan_id specified\r\n  }\r\n};\r\n\r\nexport default function LandingPageClient() {\r\n  const [showError, setShowError] = useState(false);\r\n  const [errorMessage, setErrorMessage] = useState(\"\");\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  // Authentication state\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n  const [userType, setUserType] = useState<\"business\" | \"customer\" | null>(null);\r\n  const [businessCardData, setBusinessCardData] = useState<BusinessCardData | undefined>();\r\n  const [userPlan, setUserPlan] = useState<\"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | undefined>();\r\n  const [authLoading, setAuthLoading] = useState(true);\r\n\r\n  const searchParams = useSearchParams();\r\n  const supabase = createClient();\r\n\r\n  // Handle client-side mounting to prevent hydration errors\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  // Handle authentication client-side\r\n  useEffect(() => {\r\n    const checkAuth = async () => {\r\n      try {\r\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n        if (authError || !user) {\r\n          setIsAuthenticated(false);\r\n          setUserType(null);\r\n          setAuthLoading(false);\r\n          return;\r\n        }\r\n\r\n        setIsAuthenticated(true);\r\n\r\n        // Check user type by looking at both profile tables\r\n        const [customerRes, businessRes] = await Promise.all([\r\n          supabase\r\n            .from(\"customer_profiles\")\r\n            .select(\"id\")\r\n            .eq(\"id\", user.id)\r\n            .maybeSingle(),\r\n          supabase\r\n            .from(\"business_profiles\")\r\n            .select(`\r\n              id, business_name, contact_email, has_active_subscription,\r\n              trial_end_date, created_at, updated_at, logo_url, member_name, title,\r\n              address_line, city, state, pincode, locality, phone, instagram_url,\r\n              facebook_url, whatsapp_number, about_bio, status, business_slug,\r\n              total_likes, total_subscriptions, average_rating, theme_color,\r\n              delivery_info, business_hours, business_category, total_visits, established_year,\r\n              custom_branding, custom_ads\r\n            `)\r\n            .eq(\"id\", user.id)\r\n            .maybeSingle(),\r\n        ]);\r\n\r\n        if (customerRes.error || businessRes.error) {\r\n          console.error(\"Error checking user type:\", customerRes.error || businessRes.error);\r\n          setUserType(null);\r\n        } else if (customerRes.data) {\r\n          setUserType(\"customer\");\r\n        } else if (businessRes.data) {\r\n          setUserType(\"business\");\r\n\r\n          // Fetch subscription data separately and add to business profile data\r\n          const { data: subscription } = await supabase\r\n            .from(\"payment_subscriptions\")\r\n            .select(\"plan_id, subscription_status\")\r\n            .eq(\"business_profile_id\", businessRes.data.id)\r\n            .order(\"created_at\", { ascending: false })\r\n            .limit(1)\r\n            .maybeSingle();\r\n\r\n          // Add subscription data and transform data exactly like public card page\r\n          const businessDataWithSubscription = {\r\n            ...businessRes.data,\r\n            subscription_status: subscription?.subscription_status || null,\r\n            plan_id: subscription?.plan_id || null,\r\n            // Add missing fields for schema compatibility (same as public card page)\r\n            total_reviews: 0, // We don't fetch reviews count on homepage\r\n            primary_phone: businessRes.data.phone,\r\n            business_category: businessRes.data.business_category || \"\",\r\n            // Ensure required fields are properly typed\r\n            status: businessRes.data.status as \"online\" | \"offline\",\r\n            title: businessRes.data.title || \"\",\r\n            member_name: businessRes.data.member_name || \"\",\r\n            business_name: businessRes.data.business_name || \"\",\r\n            contact_email: businessRes.data.contact_email || \"\",\r\n            // Convert date strings to Date objects\r\n            created_at: businessRes.data.created_at\r\n              ? new Date(businessRes.data.created_at)\r\n              : undefined,\r\n            updated_at: businessRes.data.updated_at\r\n              ? new Date(businessRes.data.updated_at)\r\n              : undefined,\r\n            trial_end_date: businessRes.data.trial_end_date\r\n              ? businessRes.data.trial_end_date // Keep as string\r\n              : null,\r\n          };\r\n\r\n          setBusinessCardData(businessDataWithSubscription as BusinessCardData);\r\n\r\n          // Use the same getUserPlan logic as public card page\r\n          const plan = getUserPlan(businessDataWithSubscription);\r\n          setUserPlan(plan);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Unexpected error checking auth:\", error);\r\n        setIsAuthenticated(false);\r\n        setUserType(null);\r\n      } finally {\r\n        setAuthLoading(false);\r\n      }\r\n    };\r\n\r\n    checkAuth();\r\n  }, [supabase]);\r\n\r\n  // Check URL parameters for errors\r\n  useEffect(() => {\r\n    const error = searchParams.get(\"error\");\r\n    const errorCode = searchParams.get(\"error_code\");\r\n    const errorDescription = searchParams.get(\"error_description\");\r\n\r\n    if (error && errorDescription) {\r\n      let message = decodeURIComponent(errorDescription);\r\n      switch (errorCode) {\r\n        case \"otp_expired\":\r\n          message =\r\n            \"The email verification link has expired. Please try registering again.\";\r\n          break;\r\n        case \"access_denied\":\r\n          message = \"Access was denied. \" + message;\r\n          break;\r\n        default:\r\n          message = \"An error occurred: \" + message;\r\n      }\r\n      setErrorMessage(message);\r\n      setShowError(true);\r\n    }\r\n  }, [searchParams]);\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white dark:bg-black relative\">\r\n      {/* Error Dialog */}\r\n      <AnimatePresence>\r\n        {showError && (\r\n          <ErrorDialog\r\n            open={showError}\r\n            onOpenChange={setShowError}\r\n            errorMessage={errorMessage}\r\n          />\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* Main content */}\r\n      <div>\r\n          {/* Hero Section with side-by-side layout - only render after client-side mounting and auth check */}\r\n          {isMounted && !authLoading ? (\r\n            <StickyHeroSectionClient\r\n              isAuthenticated={isAuthenticated}\r\n              userType={userType}\r\n              businessCardData={businessCardData}\r\n              userPlan={userPlan}\r\n            />\r\n          ) : (\r\n            <div className=\"w-full h-[80vh] flex items-center justify-center\">\r\n              <div className=\"animate-pulse w-16 h-16 rounded-full bg-[var(--brand-gold)]/20\"></div>\r\n            </div>\r\n          )}\r\n\r\n        {/* Section divider */}\r\n        <SectionDivider variant=\"gold\" />\r\n\r\n        {/* New Arrivals Section */}\r\n        <NewArrivalsSection />\r\n\r\n        {/* Section divider */}\r\n        <SectionDivider variant=\"neutral\" />\r\n\r\n        {/* Popular Businesses Section */}\r\n        <PopularBusinessesSection />\r\n\r\n        {/* Section divider */}\r\n        <SectionDivider variant=\"blue\" />\r\n\r\n        {/* Pricing Section */}\r\n        <PricingSection />\r\n\r\n        {/* Section divider */}\r\n        <SectionDivider variant=\"gold\" />\r\n\r\n        {/* Features Section */}\r\n        <FeaturesSection />\r\n\r\n        {/* Section divider */}\r\n        <SectionDivider variant=\"purple\" />\r\n\r\n        {/* Testimonials Section */}\r\n        <TestimonialsSection />\r\n\r\n        {/* Section divider */}\r\n        <SectionDivider variant=\"blue\" />\r\n\r\n        {/* CTA Section */}\r\n        <CTASection />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAfA;;;;;;;;;;;;;;AAiBA,oEAAoE;AACpE,MAAM,cAAc,CAClB;IAEA,uDAAuD;IACvD,OAAQ,QAAQ,OAAO;QACrB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,QAAQ,0CAA0C;IAC7D;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uBAAuB;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IACzE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAE5B,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,aAAa;QACf;sCAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;yDAAY;oBAChB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;wBAExE,IAAI,aAAa,CAAC,MAAM;4BACtB,mBAAmB;4BACnB,YAAY;4BACZ,eAAe;4BACf;wBACF;wBAEA,mBAAmB;wBAEnB,oDAAoD;wBACpD,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACnD,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;4BACd,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;YAQT,CAAC,EACA,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;yBACf;wBAED,IAAI,YAAY,KAAK,IAAI,YAAY,KAAK,EAAE;4BAC1C,QAAQ,KAAK,CAAC,6BAA6B,YAAY,KAAK,IAAI,YAAY,KAAK;4BACjF,YAAY;wBACd,OAAO,IAAI,YAAY,IAAI,EAAE;4BAC3B,YAAY;wBACd,OAAO,IAAI,YAAY,IAAI,EAAE;4BAC3B,YAAY;4BAEZ,sEAAsE;4BACtE,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,yBACL,MAAM,CAAC,gCACP,EAAE,CAAC,uBAAuB,YAAY,IAAI,CAAC,EAAE,EAC7C,KAAK,CAAC,cAAc;gCAAE,WAAW;4BAAM,GACvC,KAAK,CAAC,GACN,WAAW;4BAEd,yEAAyE;4BACzE,MAAM,+BAA+B;gCACnC,GAAG,YAAY,IAAI;gCACnB,qBAAqB,cAAc,uBAAuB;gCAC1D,SAAS,cAAc,WAAW;gCAClC,yEAAyE;gCACzE,eAAe;gCACf,eAAe,YAAY,IAAI,CAAC,KAAK;gCACrC,mBAAmB,YAAY,IAAI,CAAC,iBAAiB,IAAI;gCACzD,4CAA4C;gCAC5C,QAAQ,YAAY,IAAI,CAAC,MAAM;gCAC/B,OAAO,YAAY,IAAI,CAAC,KAAK,IAAI;gCACjC,aAAa,YAAY,IAAI,CAAC,WAAW,IAAI;gCAC7C,eAAe,YAAY,IAAI,CAAC,aAAa,IAAI;gCACjD,eAAe,YAAY,IAAI,CAAC,aAAa,IAAI;gCACjD,uCAAuC;gCACvC,YAAY,YAAY,IAAI,CAAC,UAAU,GACnC,IAAI,KAAK,YAAY,IAAI,CAAC,UAAU,IACpC;gCACJ,YAAY,YAAY,IAAI,CAAC,UAAU,GACnC,IAAI,KAAK,YAAY,IAAI,CAAC,UAAU,IACpC;gCACJ,gBAAgB,YAAY,IAAI,CAAC,cAAc,GAC3C,YAAY,IAAI,CAAC,cAAc,CAAC,iBAAiB;mCACjD;4BACN;4BAEA,oBAAoB;4BAEpB,qDAAqD;4BACrD,MAAM,OAAO,YAAY;4BACzB,YAAY;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,mBAAmB;wBACnB,YAAY;oBACd,SAAU;wBACR,eAAe;oBACjB;gBACF;;YAEA;QACF;sCAAG;QAAC;KAAS;IAEb,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,QAAQ,aAAa,GAAG,CAAC;YAC/B,MAAM,YAAY,aAAa,GAAG,CAAC;YACnC,MAAM,mBAAmB,aAAa,GAAG,CAAC;YAE1C,IAAI,SAAS,kBAAkB;gBAC7B,IAAI,UAAU,mBAAmB;gBACjC,OAAQ;oBACN,KAAK;wBACH,UACE;wBACF;oBACF,KAAK;wBACH,UAAU,wBAAwB;wBAClC;oBACF;wBACE,UAAU,wBAAwB;gBACtC;gBACA,gBAAgB;gBAChB,aAAa;YACf;QACF;sCAAG;QAAC;KAAa;IAIjB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,2BACC,6LAAC,2JAAA,CAAA,UAAW;oBACV,MAAM;oBACN,cAAc;oBACd,cAAc;;;;;;;;;;;0BAMpB,6LAAC;;oBAEI,aAAa,CAAC,4BACb,6LAAC,uKAAA,CAAA,UAAuB;wBACtB,iBAAiB;wBACjB,UAAU;wBACV,kBAAkB;wBAClB,UAAU;;;;;6CAGZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAKrB,6LAAC,8JAAA,CAAA,UAAc;wBAAC,SAAQ;;;;;;kCAGxB,6LAAC,kKAAA,CAAA,UAAkB;;;;;kCAGnB,6LAAC,8JAAA,CAAA,UAAc;wBAAC,SAAQ;;;;;;kCAGxB,6LAAC,wKAAA,CAAA,UAAwB;;;;;kCAGzB,6LAAC,8JAAA,CAAA,UAAc;wBAAC,SAAQ;;;;;;kCAGxB,6LAAC,8JAAA,CAAA,UAAc;;;;;kCAGf,6LAAC,8JAAA,CAAA,UAAc;wBAAC,SAAQ;;;;;;kCAGxB,6LAAC,+JAAA,CAAA,UAAe;;;;;kCAGhB,6LAAC,8JAAA,CAAA,UAAc;wBAAC,SAAQ;;;;;;kCAGxB,6LAAC,mKAAA,CAAA,UAAmB;;;;;kCAGpB,6LAAC,8JAAA,CAAA,UAAc;wBAAC,SAAQ;;;;;;kCAGxB,6LAAC,0JAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;AAInB;GApNwB;;QAYD,qIAAA,CAAA,kBAAe;;;KAZd", "debugId": null}}]}